# Microservice cus-finance-biz-bill-center-app

## Overview

The bill center service provides financial bill processing capabilities for Kering China.


## Unit Test

Code coverage: current -> 81%

```
mvn -B clean test jacoco:report --file pom.xml
mvn -B sonar:sonar -Dsonar.host.url=https://sonar.mgmt.arsenal.mgmt4apps.io  -Dsonar.token=squ_* --file pom.xml
```

sonar.coverage.exclusions
```xml
<sonar.coverage.exclusions>
  /src/main/java/**/BillCenterApplication.java,
  /src/main/java/**/dto/*.java,
  /src/main/java/**/dao/*.java,
  /src/main/java/**/mapper/*.java,
  /src/main/java/**/entity/*.java,
  /src/main/java/**/config/*.java,
  /src/main/java/**/client/**/*.java,
  /src/main/java/**/converter/*.java,
  /src/main/java/**/exception/*.java,
  /src/main/java/**/constant/*.java
</sonar.coverage.exclusions>
```

## How to run the application locally

### Copy application.properties

1. First cope the application.properties.template to application-dev.properties
2. Then Use the following command to build the project

```bash
mvn clean package
```

3. Finally run the application using the following command

```bash
java -jar target/cus-finance-biz-bill-center-1.0.0-SNAPSHOT.jar
```

or

Import the project into IntelliJ IDEA and run the project

### Compile Errors

```compile error
PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException:
unable to find valid certification path to requested target and 'parent.relativePath' points at no local
```

This error indicates that there is an SSL error when connects the [rep.maven.apache.org] to the happens when in the kering intranet,
You can comment out the repository settings or use the public edge node.

Reference: https://stackoverflow.com/questions/25911623/problems-using-maven-and-ssl-behind-proxy

It is recommended to use Maven setting.xml for repository configuration
```xml
<?xml version="1.0" encoding="utf-8"?>

<settings xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0                       http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <localRepository>{your path}</localRepository>
  <servers>
    <server>
      <id>cus-lib-snapshot</id>
      <username><EMAIL></username>
      <password>***</password>
    </server>
    <server>
      <id>cus-lib-release</id>
      <username><EMAIL></username>
      <password>***</password>
    </server>
  </servers>
  <pluginGroups>
    <pluginGroup>org.sonarsource.scanner.maven</pluginGroup>
    <pluginGroup>com.spotify</pluginGroup>
  </pluginGroups>
  <profiles>
    <profile>
      <id>default</id>
      <repositories>
        <repository>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>cus-lib-release</id>
          <name>KT Framework Release Repository</name>
          <url>https://artifactory-cn.kering.cn/artifactory/keringtech-maven-remote-cus-alicloud-edge/</url>
        </repository>
        <repository>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>cus-lib-snapshot</id>
          <name>KT Framework Snapshot Repository</name>
          <url>https://artifactory-cn.kering.cn/artifactory/keringtech-maven-snapshot-remote-cus-alicloud-edge/</url>
        </repository>
      </repositories>
    </profile>
  </profiles>
  <mirrors>
    <mirror>
      <id>alimaven</id>
      <name>aliyun maven</name>
      <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
      <mirrorOf>central</mirrorOf>
    </mirror>
  </mirrors>
  <activeProfiles>
    <activeProfile>default</activeProfile>
  </activeProfiles>
</settings>
```

### Property file

Principally we should define the basic settings in the library and only leave the customized properties in the property files
for helm injection.

Please see more detail explained in application.properties.template

### Install the mysql

see: https://hub.docker.com/_/mysql

```
docker pull mysql
docker run -d -p 3306:3306 --name mysql -e MYSQL_PASSWORD=mysql mysql
create database db_billcenter_dev
```
Run DML and DDL scripts in sqlscripts directory

## Project Structure

src/main/java (com.kering.cus.demo)
- client - Put all other services' client invoker in this folder
- client/request - Put all request model definitions in this folder for request other services
- client/response - Put all response model definitions in this folder for request other services
- config - Put all spring related configurations in this folder
- constant - Put all constant definitions in this folder
- controller - Put all rest endpoints in the controller
- converter - Put all converter definitions in this folder
- dao - Put all DAO definitions in this folder
- dto - Put all DTO model definitions in this folder
- entity - Put all entity definitions in this folder
- exception - Put all custom exceptions in this folder
- mapper - Put all mapper definitions in this folder
- service - Put all service layers in this folder
  src/main/resources
- i18n - The message property files for error message
- application.properties.template - The application property template file
- application-[env].properties - The application property file specific for each env dev/qa/preprod/prod
- logback.xml - The log layout configuration

## Tech Stack

- Java 21 + Spring Boot 3.3.1 + Spring 6
- Maven 3.9.2
- MySQL
- Spring Actuator
- Spring MVC
- Jackson Object Mapper
- MapStruct
- Swagger 3
- Sonar
- Spotbugs
- JUnit + mockito
- Docker

## Domain in kubernetes nonprod for dev/qa/preprod

- Dev - https://dev-private-ingress.cus-nonprod.keringcn-syss.net
- QA - https://qa-private-ingress.cus-nonprod.keringcn-syss.net
- Preprod - https://preprod-private-ingress.cus-nonprod.keringcn-syss.net

## Localhost

### Spring Boot Actuator Endpoints

- Base Actuator: http://localhost:8080/actuator
- Liveness Probe: http://localhost:8080/actuator/health/liveness
- Readiness Probe: http://localhost:8080/actuator/health/readiness
- Prometheus metric: http://localhost:8080/actuator/prometheus

### Swagger 3

- Swagger UI - http://localhost:8080/swagger-ui/index.html
- Swagger json file - http://localhost:8080/v3/api-docs

### cus-finance-bill-center-service API

#### 1、OAUTH2 authorize callback
```bash
curl --location 'http://localhost:8080/callback/oauth2/alipay-notify?app_auth_code=xxx' \
```

## Integrations

For all the depended middlewares or facilities, we must follow the convention to decouple the feature and config, in kering
technology, we put all configured properties in the helm values for all environments

### How to integrate the mysql database

Local developer machine
```
# Database Settings
database.url=*********************************************
database.username=xxx
database.password=xxx
```

- database.url - the database connection url
- database.username - the database credential's username
- database.password - the database credential's password

Noted: the credentials need to be secured and should never be configured in the application-[env].property, in this case
the developer can configure the db creds in the local application.property file to start the application, but for the real
environment, we will configure the username and password in vault, and injected via the Kubernetes secrets which is to be mapped
to the pod file, the datasource build logic is implemented in the cus-lib-persistence-support.

```Helm ENV
DATABASE_URL: get the database url from devOps personnel
```
The DATABASE_URL will be injected to the pod environment, and then the container will start with the injected url and pass
to the java application via *-Dpersistence.database.url=${DATABASE_URL}*

```Helm Secrets
DATABASE_USERNAME: <DATABASE_USERNAME>
DATABASE_PASSWORD: <DATABASE_PASSWORD>
```

```Helm ENV
# Secret SDK
# ...
SECRET_CONFIG_PATH_FINANCE_BILLCENTER: <KMS Base Path>
```

```Helm ENV
# Storage SDK.
STORAGE_OSS_BUCKET: <your bucket name>
BUCKET_ROOT_PATH: <your bucket prefix path>
STORAGE_OSS_SECRET_ACCESS_PATH: <OSS Secret KMS Path>
```

```Helm ENV
# Kafka
KAFKA_TOPIC_CUS_BILLCENTER: <CFS kafka topic>
KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_ACQUIRE_TASK: <Bill center acquire task kafka topic>
KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_PROCESS_TASK: <Bill center process task kafka topic>
KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_PUSH_TO_SAP_TASK: <Bill center push to SAP kafka topic>
KAFKA_GROUPID_CUS_CG_CUS_BILL_CENTER_RSVP_EVENT_GROUP: <Bill center kafka group>
```

```Helm ENV
# CFS hostname and port (acquire)
SFTP_HOST: <bill center acquire CFS host>
SFTP_PORT: <bill center acquire CFS port>
```

```Helm Secret
# CFS secret (acquire)
SFTP_USERNAME: <bill center acquire CFS username>
SFTP_PASSWORD: <bill center acquire CFS password>
SFTP_PRIVATE_KEY: <bill center acquire CFS private key, optional>
SFTP_PASSPHRASE: <bill center acquire CFS private key passphrase, optional>
```

```Helm ENV
# SAP CRUSH (S)FTP
CRUSH_SFTP_SERVER_HOST: <SAP crush SFTP host>
CRUSH_SFTP_SERVER_PORT: <SAP crush SFTP port, default 22>
```

```Helm Secret
CRUSH_SFTP_SERVER_USERNAME_JD_SAP: <SAP crush SFTP JD bill username>
CRUSH_SFTP_SERVER_PASSWORD_JD_SAP: <SAP crush SFTP JD bill password>
CRUSH_SFTP_SERVER_USERNAME_WECHAT_SAP: <SAP crush SFTP wechat bill username>
CRUSH_SFTP_SERVER_PASSWORD_WECHAT_SAP: <SAP crush SFTP wechat bill password>
CRUSH_SFTP_SERVER_USERNAME_ALIPAY_SAP: <SAP crush SFTP Alipay bill username>
CRUSH_SFTP_SERVER_PASSWORD_ALIPAY_SAP: <SAP crush SFTP Alipay bill password>
```

```Helm ENV
# EMAIL CENTER
EMAIL_CENTER_REST_ENDPOINT: <Email center endpoint>
SYSTEM_ID: <Email center call system id>
SENDER_ID: <Email center sender id>
TENANT_ID: <Email center sender tenant id>
```

# Reference
1. https://confluence.keringapps.com/spaces/PKCUS/pages/882772624/STD_04+Technical+Stack
2. https://confluence.keringapps.com/spaces/PKCUS/pages/939781850/How+to+create+a+java+microservice
3. https://confluence.keringapps.com/spaces/PKCUS/pages/883064287/Framework+Integration+Guide
4. https://confluence.keringapps.com/spaces/PKCUS/pages/873867638/1.4+STD_00+Technical+Standard
