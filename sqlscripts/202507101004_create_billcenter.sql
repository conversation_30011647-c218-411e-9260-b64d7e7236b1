/*==============================================================*/
/* Table: t_merchant_grant_config                               */
/*==============================================================*/
create table t_merchant_grant_config
(
   id                   bigint not null auto_increment comment 'ID',
   channel              varchar(100) not null comment '渠道',
   merchant_id          varchar(100) not null comment '商户ID',
   grant_type           varchar(50) not null comment '授权类型',
   api_version          varchar(50) comment '接口版本',
   kms_brand_id         varchar(100) comment 'KMS品牌ID',
   is_enabled           tinyint(1) not null comment '是否启用',
   created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
   created_by           varchar(100) comment '创建人',
   modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
   modified_by          varchar(100) comment '更新人',
   deleted              tinyint(1) not null comment '逻辑删除',
   tenant_id            varchar(100) comment '租户ID',
   version              int(10) not null comment '版本',
   primary key (id)
);

alter table t_merchant_grant_config comment '商户授权配置';

/*==============================================================*/
/* Index: idx_channel_merchant_id                               */
/*==============================================================*/
create unique index idx_channel_merchant_id on t_merchant_grant_config
(
   channel,
   merchant_id
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_merchant_grant_config
(
   merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_merchant_grant_config
(
   created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_merchant_grant_config
(
   modified_date
);

/*==============================================================*/
/* Table: t_oauth_access_token                                  */
/*==============================================================*/
create table t_oauth_access_token
(
   id                   bigint not null auto_increment comment 'ID',
   platform             varchar(100) not null comment '平台',
   client_id            varchar(200) not null comment '授权方client_id',
   user_id              varchar(200) comment '用户ID',
   open_id              varchar(200) comment 'open_id',
   merchant_id          varchar(200) comment '商户ID',
   access_token         varchar(500) not null comment '访问令牌',
   refresh_token        varchar(500) comment '刷新令牌',
   expires_at           datetime not null comment '访问令牌过期时间',
   re_expires_at        datetime not null comment '刷新令牌过期时间',
   error_count          smallint(5) not null comment '授权刷新失败次数',
   error_msg            varchar(500) comment '授权刷新失败原因',
   next_refresh_time    datetime comment '下次刷新时间',
   created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
   created_by           varchar(100) comment '创建人',
   modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
   modified_by          varchar(100) comment '更新人',
   deleted              tinyint(1) not null comment '逻辑删除',
   tenant_id            varchar(100) comment '租户ID',
   version              int(10) not null comment '版本',
   primary key (id)
);

alter table t_oauth_access_token comment '服务商应用授权';

/*==============================================================*/
/* Index: idx_platform_merchant_id                              */
/*==============================================================*/
create index idx_platform_merchant_id on t_oauth_access_token
(
   merchant_id,
   platform
);

/*==============================================================*/
/* Index: idx_error_count_next_refresh_time                     */
/*==============================================================*/
create index idx_error_count_next_refresh_time on t_oauth_access_token
(
   error_count,
   next_refresh_time
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_oauth_access_token
(
   created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_oauth_access_token
(
   modified_date
);


/*==============================================================*/
/* Table: t_merchant_config                                     */
/*==============================================================*/
create table t_merchant_config
(
   id                   bigint not null auto_increment comment 'ID',
   channel              varchar(100) not null comment '渠道',
   merchant_id          varchar(100) not null comment '商户',
   brand                varchar(100) not null comment '所属品牌',
   is_tmall             tinyint(1) not null comment '是否天猫店',
   is_wosaipay_merchant tinyint(1) comment '是否收钱吧(二级)商户',
   is_sync_to_blackline tinyint(1) not null comment '是否推送blackline',
   sap_gl_account1      varchar(100) comment 'SAP 总账科目',
   sap_gl_account2      varchar(100) comment 'SAP 清算科目',
   sap_profit_center    varchar(100) comment 'SAP 利润中心',
   sap_merchant_code    varchar(100) comment 'SAP 商户短号',
   is_sync_to_sap       tinyint(1) not null comment '是否推送SAP',
   note                 varchar(200) comment '备注',
   created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
   created_by           varchar(100) comment '创建人',
   modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
   modified_by          varchar(100) comment '更新人',
   deleted              tinyint(1) not null comment '逻辑删除',
   tenant_id            varchar(100) comment '租户ID',
   version              int(10) not null comment '版本',
   primary key (id)
);

alter table t_merchant_config comment '商户配置';

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_merchant_config
(
   channel,
   brand
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create unique index idx_merchant_id on t_merchant_config
(
   merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_merchant_config
(
   created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_merchant_config
(
   modified_date
);

/*==============================================================*/
/* Table: t_bill_task                                           */
/*==============================================================*/
create table t_bill_task
(
   id                   bigint not null auto_increment comment 'ID',
   brand                varchar(100) comment '品牌',
   channel              varchar(100) not null comment '渠道',
   merchant_id          varchar(200) comment '商户',
   grant_type           varchar(50) not null comment '类型(授权/自研/SFTP)',
   bill_type            varchar(50) not null comment '账单类型',
   bill_start_time      datetime comment '账单起始时间',
   bill_end_time        datetime comment '账单截止时间',
   state                varchar(50) not null comment '状态',
   extra_params         varchar(300),
   source               varchar(100) not null comment '任务来源',
   task_unique_key      varchar(300) comment '任务唯一键',
   origin_bill_url      varchar(300) comment '原始账单OSS地址',
   sap_archive_url      varchar(300) comment '推送SAP文件',
   blackline_archive_url varchar(300) comment '推送blackline文件',
   error_count          smallint(5) not null default 0 comment '错误次数',
   error_msg            varchar(500) comment '错误原因',
   next_run_time        datetime    not null comment '下次运行时间',
   sap_sync_state       varchar(50) comment 'SAP推送状态',
   sap_error_count      smallint(5) not null default 0 comment 'SAP错误次数',
   sap_error_msg        varchar(500) comment 'SAP错误原因',
   sap_next_run_time    datetime     comment 'SAP下次运行时间',
   blackline_sync_state varchar(50) comment 'blackline推送状态',
   blackline_error_count      smallint(5) not null default 0 comment 'BL错误次数',
   blackline_error_msg        varchar(500) comment 'BL错误原因',
   blackline_next_run_time    datetime     comment 'BL下次运行时间',
   is_tmall             tinyint(1) not null comment '是否天猫商户',
   is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
   is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
   trace_id             varchar(100) not null comment '任务日志追踪ID',
   note                 varchar(200) comment '备注',
   finished_date        datetime comment '完成时间',
   created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
   created_by           varchar(100) comment '创建人',
   modified_date        timestamp(0) not null default CURRENT_TIMESTAMP comment '更新时间',
   modified_by          varchar(100) comment '更新人',
   deleted              tinyint(1) not null comment '逻辑删除',
   tenant_id            varchar(100) comment '租户ID',
   version              int(10) not null comment '版本',
   primary key (id)
);

alter table t_bill_task comment '账单处理任务';

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_bill_task
(
   brand,
   channel
);

/*==============================================================*/
/* Index: idx_task_unique_key                                   */
/*==============================================================*/
create unique index idx_task_unique_key on t_bill_task
(
   task_unique_key
);

/*==============================================================*/
/* Index: idx_state                                             */
/*==============================================================*/
create index idx_state on t_bill_task
(
   state
);

/*==============================================================*/
/* Index: idx_sap_archive_state                                 */
/*==============================================================*/
create index idx_sap_archive_state on t_bill_task
(
   sap_sync_state
);

/*==============================================================*/
/* Index: idx_blackline_archive_state                           */
/*==============================================================*/
create index idx_blackline_archive_state on t_bill_task
(
   blackline_sync_state
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_bill_task
(
   created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_bill_task
(
   modified_date
);


/*==============================================================*/
/* Table: t_transit_log                                         */
/*==============================================================*/
create table t_transit_log
(
   id                   bigint not null auto_increment comment 'ID',
   trace_id             varchar(100) not null comment '追踪ID',
   title                varchar(200) not null comment '日志标题',
   message              varchar(500) not null comment '日志描述',
   created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
   created_by           varchar(100) comment '创建人',
   modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
   modified_by          varchar(100) comment '更新人',
   deleted              tinyint(1) not null comment '逻辑删除',
   tenant_id            varchar(100) comment '租户ID',
   version              int(10) not null comment '版本',
   primary key (id)
);

alter table t_transit_log comment '账单任务流转日志';

/*==============================================================*/
/* Index: idx_trace_id                                          */
/*==============================================================*/
create index idx_trace_id on t_transit_log
(
   trace_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_transit_log
(
   created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_transit_log
(
   modified_date
);


/*==============================================================*/
/* Table: t_alipay_bill_flow                                    */
/*==============================================================*/
create table t_alipay_bill_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    account_transaction_no varchar(100) comment '账务流水号',
    biz_transaction_no   varchar(100) comment '业务流水号',
    merchant_order_id    varchar(100) comment '商户订单号',
    sku_name             varchar(200) comment '商品名称',
    transaction_time     varchar(50) comment '发生时间',
    reciprocal_account   varchar(100) comment '对方账号',
    income               decimal(16, 6) comment '收入金额',
    expense              decimal(16, 6) comment '支出金额',
    balance              decimal(16, 6) comment '账户余额',
    transaction_channel  varchar(50) comment '交易渠道',
    transaction_type     varchar(50) comment '业务类型',
    note                 varchar(500) comment '备注',
    biz_desc             varchar(200) comment '业务描述',
    biz_order_id         varchar(100) comment '业务订单号',
    biz_base_order_id    varchar(100) comment '业务基础订单号',
    biz_bill_source      varchar(100) comment '业务账单来源',
    final_biz_base_order_id varchar(100) comment '【最终】业务基础订单号',
    is_tmall             tinyint(1) not null comment '是否天猫账单',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_alipay_bill_flow comment '支付宝账单流水';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_alipay_bill_flow
(
task_id
);

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_alipay_bill_flow
(
brand,
channel
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_alipay_bill_flow
(
merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_alipay_bill_flow
(
created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_alipay_bill_flow
(
modified_date
);

/*==============================================================*/
/* Table: t_jd_wallet_flow                                      */
/*==============================================================*/
create table t_jd_wallet_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    transaction_time     varchar(50) comment '日期',
    billing_time         varchar(50) comment '账单日期',
    transaction_no       varchar(100) comment '流水号',
    account_code         varchar(100) comment '账户代码',
    account_name         varchar(100) comment '账户名称',
    currency             varchar(10) comment '币种',
    payment_type         varchar(50) comment '收支类型',
    income               decimal(16,6) comment '收入金额',
    expense              decimal(16,6) comment '支出金额',
    balance              decimal(16,6) comment '账户余额',
    transaction_type     varchar(50) comment '交易类型',
    merchant_order_id    varchar(100) comment '商户订单号',
    origin_merchant_order_id varchar(100) comment '原商户订单号',
    transaction_note     varchar(200) comment '交易备注',
    voucher_no           varchar(100) comment '记账请求号',
    transaction_order_id varchar(100) comment '交易订单号',
    biz_order_id         varchar(100) comment '业务订单号',
    platform_order_id    varchar(100) comment '【平台订单号】',
    is_afs_order         tinyint(1) not null comment '【是否售后服务单】',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_jd_wallet_flow comment '京东钱包流水';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_jd_wallet_flow
(
task_id
);

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_jd_wallet_flow
(
brand,
channel
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_jd_wallet_flow
(
merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_jd_wallet_flow
(
created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_jd_wallet_flow
(
modified_date
);

/*==============================================================*/
/* Table: t_jd_trade_flow                                       */
/*==============================================================*/
create table t_jd_trade_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    order_id             varchar(100) comment '订单编号',
    transaction_no       varchar(100) comment '单据编号',
    transaction_type     varchar(50) comment '单据类型',
    sku_code             varchar(100) comment '商品编号',
    merchant_order_id    varchar(100) comment '商户订单号',
    sku_name             varchar(200) comment '商品名称',
    settlement_status    varchar(50) comment '结算状态',
    occur_time           varchar(50) comment '费用发生时间',
    chargeable_time      varchar(50) comment '费用计费时间',
    settlement_time      varchar(50) comment '费用结算时间',
    fee_item             varchar(100) comment '费用项',
    amount               decimal(16, 6) comment '金额',
    currency             varchar(10) comment '币种',
    merchant_payment_type varchar(50) comment '商家应收/应付',
    settlement_note      varchar(200) comment '钱包结算备注',
    shop_code            varchar(100) comment '店铺号',
    jd_store_code        varchar(100) comment '京东门店编号',
    brand_store_code     varchar(100) comment '品牌门店编号',
    store_name           varchar(100) comment '门店名称',
    note                 varchar(200) comment '备注',
    payment_type         varchar(50) comment '收支方向',
    sku_qty              int comment '商品数量',
    billing_date         varchar(50) comment '【账单日期】',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_jd_trade_flow comment '京东交易账单';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_jd_trade_flow
(
task_id
);

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_jd_trade_flow
(
brand,
channel
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_jd_trade_flow
(
merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_jd_trade_flow
(
created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_jd_trade_flow
(
modified_date
);
/*==============================================================*/
/* Table: t_wechat_fund_flow                                    */
/*==============================================================*/
create table t_wechat_fund_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    billing_date         varchar(100) comment '记账时间',
    biz_order_id         varchar(100) comment '微信支付业务单号',
    transaction_no       varchar(150) comment '资金流水单号',
    transaction_name     varchar(50) comment '业务名称',
    transaction_type     varchar(50) comment '业务类型',
    payment_type         varchar(50) comment '收支类型',
    amount               decimal(16, 6) comment '收支金额(元)',
    balance              decimal(16, 6) comment '账户结余(元)',
    apply_user           varchar(100) comment '资金变更提交申请人',
    note                 varchar(2500) comment '备注',
    biz_voucher_no       varchar(100) comment '业务凭证号',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_wechat_fund_flow comment '微信资金账单';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_wechat_fund_flow
    (
     task_id
        );

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_wechat_fund_flow
    (
     brand,
     channel
        );

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_wechat_fund_flow
    (
     merchant_id
        );

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_wechat_fund_flow
    (
     created_date
        );

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_wechat_fund_flow
    (
     modified_date
        );

/*==============================================================*/
/* Table: t_wechat_trade_flow                                   */
/*==============================================================*/
create table t_wechat_trade_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    transaction_time     varchar(50) comment '交易时间',
    app_id               varchar(100) comment '公众账号ID',
    main_merchant        varchar(100) comment '支付商户号',
    sub_merchant         varchar(100) comment '特约商户号',
    device_no            varchar(100) comment '设备号',
    wechat_order_id      varchar(100) comment '微信订单号',
    merchant_order_id    varchar(100) comment '商户订单号',
    open_id              varchar(200) comment '用户标识',
    transaction_type     varchar(50) comment '交易类型',
    trade_status         varchar(50) comment '交易状态',
    payment_bank         varchar(100) comment '付款银行',
    currency             varchar(50) comment '货币种类',
    settlement_amount    decimal(16, 6) comment '应结订单金额',
    voucher_amount       decimal(16, 6) comment '代金券金额',
    wechat_refund_id     varchar(100) comment '微信退款单号',
    merchant_refund_id   varchar(100) comment '商户退款单号',
    refund_amount        decimal(16, 6) comment '退款金额',
    recharge_refund_amount decimal(16, 6) comment '充值券退款金额',
    refund_type          varchar(50) comment '退款类型',
    refund_status        varchar(50) comment '退款状态',
    sku_name             varchar(200) comment '商品名称',
    merchant_data        varchar(500) comment '商户数据包',
    service_charge       decimal(16,6) comment '手续费',
    transaction_rate     varchar(100) comment '费率',
    order_amount         decimal(16, 6) comment '订单金额',
    apply_refund_amount  decimal(16, 6) comment '申请退款金额',
    transaction_rate_note varchar(200) comment '费率备注',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_wechat_trade_flow comment '微信交易账单';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_wechat_trade_flow
    (
     task_id
        );

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_wechat_trade_flow
    (
     brand,
     channel
        );

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_wechat_trade_flow
    (
     merchant_id
        );

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_wechat_trade_flow
    (
     created_date
        );

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_wechat_trade_flow
    (
     modified_date
        );

/*==============================================================*/
/* Table: t_wosaipay_trade_flow                                 */
/*==============================================================*/
create table t_wosaipay_trade_flow
(
    id                   bigint not null auto_increment comment 'ID',
    task_id              bigint not null comment '任务ID',
    brand                varchar(100) comment '品牌',
    channel              varchar(100) not null comment '渠道',
    merchant_id          varchar(100) not null comment '商户号',
    merchant_name        varchar(100) comment '商户名',
    store_name           varchar(100) comment '门店名',
    store_code           varchar(100) comment '门店号',
    merchant_store_code  varchar(100) comment '商户门店号',
    transaction_date     varchar(50) comment '交易日期',
    transaction_time     varchar(50) comment '交易时间',
    merchant_order_id    varchar(100) comment '商户订单号',
    merchant_transaction_no varchar(100) comment '商户流水号',
    pos_order_id         varchar(100) comment '轻POS订单号',
    pos_transaction_no   varchar(100) comment '轻POS流水号',
    pos_origin_order_id  varchar(100) comment '轻POS原始订单号',
    pos_origin_transaction_no varchar(100) comment '轻POS原始流水号',
    wosaypay_order_id    varchar(100) comment '收钱吧订单号',
    payment_channel      varchar(50) comment '收款通道',
    payment_channel_merchant_id varchar(100) comment '收款通道商户号',
    payment_channel_order_id varchar(100) comment '收款通道订单号',
    sku_name             varchar(100) comment '商品名',
    transaction_type     varchar(50) comment '交易类型',
    trade_mode           varchar(50) comment '交易模式',
    order_source         varchar(100) comment '订单来源',
    order_sence          varchar(100) comment '交易场景',
    trade_status         varchar(50) comment '交易状态',
    payment_account      varchar(100) comment '付款账户',
    currency             varchar(10) comment '币种/货币类型',
    transaction_amount   decimal(16,6) comment '交易金额',
    merchant_discount_amount decimal(16,6) comment '收钱吧商户优惠',
    merchant_discount_type varchar(50) comment '收钱吧商户优惠类型',
    subsidy_amount       decimal(16,6) comment '收钱吧补贴优惠',
    payment_channel_discount_amount decimal(16,6) comment '收款通道机构优惠',
    recharge_discount_amount decimal(16,6) comment '收款通道商户预充值优惠',
    non_recharge_discount_amount decimal(16,6) comment '收款通道商户免充值优惠',
    actual_paid_amount   decimal(16,6) comment '消费者实付金额',
    transaction_rate     decimal(16,6) comment '扣率%',
    service_charge       decimal(16,6) comment '手续费',
    actual_recv_amount   decimal(16,6) comment '实收金额',
    installment_num      int comment '分期期数',
    installment_fee_rate decimal(16,6) comment '分期手续费率',
    installment_fee      decimal(16,6) comment '分期手续费',
    split_amount         decimal(16,6) comment '分账金额',
    settlement_amount    decimal(16,6) comment '结算金额',
    term_no              varchar(100) comment '终端号',
    merchant_term_no     varchar(100) comment '商户终端号',
    term_name            varchar(100) comment '终端名称',
    term_type            varchar(100) comment '终端类型',
    device_no            varchar(100) comment '设备号',
    operator             varchar(100) comment '操作员',
    cashier              varchar(100) comment '收银员',
    note                 varchar(200) comment '备注',
    ext1                 varchar(200) comment '扩展一',
    ext2                 varchar(200) comment '扩展二',
    biz_order_id         varchar(100) comment '业务订单号',
    biz_note             varchar(200) comment '业务备注',
    deposit              decimal(16,6) comment '保证金',
    outer_origin_transaction_no varchar(100) comment '外部系统商户原交易流水号',
    reserve3             varchar(200) comment '预留字段3',
    is_sync_to_blackline tinyint(1) not null comment '是否需要推送blackline',
    is_sync_to_sap       tinyint(1) not null comment '是否需要推送SAP',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_wosaipay_trade_flow comment '收钱吧交易账单';

/*==============================================================*/
/* Index: idx_task_id                                           */
/*==============================================================*/
create index idx_task_id on t_wosaipay_trade_flow
(
task_id
);

/*==============================================================*/
/* Index: idx_brand_channel                                     */
/*==============================================================*/
create index idx_brand_channel on t_wosaipay_trade_flow
(
brand,
channel
);

/*==============================================================*/
/* Index: idx_merchant_id                                       */
/*==============================================================*/
create index idx_merchant_id on t_wosaipay_trade_flow
(
merchant_id
);

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_wosaipay_trade_flow
(
created_date
);

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_wosaipay_trade_flow
(
modified_date
);

/*==============================================================*/
/* Table: t_dictionary                                          */
/*==============================================================*/
create table t_dictionary
(
    id                   bigint not null auto_increment comment 'ID',
    dic_group            varchar(100) not null comment '分组',
    dic_key              varchar(100) not null comment 'key',
    dic_value            varchar(100) not null comment 'value',
    is_enabled           tinyint(1) not null comment '是否启用',
    created_date         timestamp not null default CURRENT_TIMESTAMP comment '创建时间',
    created_by           varchar(100) comment '创建人',
    modified_date        timestamp not null default CURRENT_TIMESTAMP comment '更新时间',
    modified_by          varchar(100) comment '更新人',
    deleted              tinyint(1) not null comment '逻辑删除',
    tenant_id            varchar(100) comment '租户ID',
    version              int(10) not null comment '版本',
    primary key (id)
);

alter table t_dictionary comment '数据字典';

/*==============================================================*/
/* Index: idx_dic_group                                         */
/*==============================================================*/
create index idx_dic_group on t_dictionary
    (
     dic_group
        );

/*==============================================================*/
/* Index: idx_create_time                                       */
/*==============================================================*/
create index idx_create_time on t_dictionary
    (
     created_date
        );

/*==============================================================*/
/* Index: idx_modified_date                                     */
/*==============================================================*/
create index idx_modified_date on t_dictionary
    (
     modified_date
        );
/*==============================================================*/
/* t_dictionary init data                                */
/*==============================================================*/

INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.channels', 'ALIPAY', '支付宝', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.channels', 'WECHAT', '微信', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.channels', 'JD', '京东', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.channels', 'WOSAIPAY', '收钱吧', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.channels', 'TM_WECHAT', '天猫微信', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);

INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'UNSET', '无', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'WAIT_ACQUIRE', '待拉取', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'ACQUIRE_FAILED', '拉取失败', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'WAIT_PROCESS', '待解析', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'PROCESS_FAILED', '解析失败', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'PROCESS_SUCCESS', '解析成功', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'SYNC_FINISHED', '推送完成', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'SYNC_FAILED', '推送失败', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.taskStatus', 'SYNC_SKIP', '无需推送', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);

INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'AMQ', 'AMQ', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'BAL', 'BAL', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'BOU', 'BOU', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'BRIONI', 'BRIONI', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'BV', 'BV', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'GUCCI', 'GUCCI', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'POM', 'POM', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'QEELIN', 'QEELIN', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.brands', 'YSL', 'YSL', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);

INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.billType', 'TRADE_FLOW', '交易账单', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);
INSERT INTO t_dictionary (dic_group, dic_key, dic_value, is_enabled, created_date, created_by, modified_date, modified_by, deleted, tenant_id, version) VALUES('bill.billType', 'FUND_FLOW', '资金账单', 1, '2025-07-24 14:49:25.0', 'System', '2025-07-24 14:49:35.0', 'System', 0, NULL, 1);

/*==============================================================*/
/* t_merchant_config, t_merchant_grant_config init data   */
/*==============================================================*/
/*-
 * Init merchant brand, SAP, blackline config.
 */
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('AMQ', 'ALIPAY', '20884316411048000156', 1, 0, 1, '********', '********', '**********', 'MQ4800', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('AMQ', 'ALIPAY', '20887417077071450156', 0, 1, 0, '********', '********', '**********', 'MQ7145', 1, '官网收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BAL', 'ALIPAY', '20887315509840640156', 1, 0, 1, '********', '********', '**********', 'BA4064', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BAL', 'ALIPAY', '20887418938062790156', 0, 1, 0, '********', '********', '**********', 'BA6279', 1, '官网和小程序收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BOU', 'ALIPAY', '20880417430413980156', 1, 0, 1, '********', '********', '**********', 'BO1398', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BOU', 'ALIPAY', '20886412057793130156', 0, 1, 0, '********', '********', '**********', 'BO9313', 1, '微信小程序收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Brioni', 'ALIPAY', '20886419410771020156', 0, 0, 1, '********', '********', '**********', 'BR7102', 1, '官网收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BV', 'ALIPAY', '20886417062559380156', 0, 1, 0, '********', '********', '**********', 'BV5938', 1, '官网*小程序*线上员工内卖收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BV', 'ALIPAY', '20882317731027050156', 1, 0, 1, '********', '********', '**********', 'BV2705', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20884319763004380156', 0, 0, 1, '********', '********', '**********', 'GG0438', 1, '员工内卖网址收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20885414287466140156', 0, 1, 0, '********', '********', '**********', 'GG6614', 1, '小程序收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20885215299844830156', 0, 1, 0, '********', '********', '**********', 'GG4483', 1, '官网收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20889311451299820156', 1, 0, 1, '********', '********', '**********', 'GG9982', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20882414257235140156', 0, 0, 1, '********', '********', '**********', 'GG3514', 1, '机场店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'ALIPAY', '20887414214695180156', 0, 1, 0, '********', '********', '**********', 'GG9518', 1, '官网-百度店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('POM', 'ALIPAY', '20880418608400140156', 1, 0, 1, '********', '********', '**********', 'PO0014', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Qeelin', 'ALIPAY', '20880317009967600156', 1, 0, 1, '********', '********', '**********', 'QE6760', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'ALIPAY', '20887316252298250156', 0, 0, 1, '', '********', '**********', 'YS9825', 1, '旧账号有余额', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'ALIPAY', '20889319705958240156', 0, 1, 0, '********', '********', '**********', 'YS5824', 1, '官网和微信小程序收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'ALIPAY', '20889319725038920156', 1, 0, 1, '********', '********', '**********', 'YS3892', 1, '天猫店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'ALIPAY', '20888408726087730156', 0, 0, 1, '********', '********', '**********', 'YS8773', 1, '机场店铺收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BAL', 'JD', '************', 0, 0, 1, '********', '********', '**********', 'BA4002', 1, '京东商场收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BV', 'JD', '************', 0, 0, 1, '********', '********', '**********', 'BV5002', 1, '京东商场收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'JD', '************', 0, 0, 1, '********', '********', '**********', 'GG8002', 1, '京东商场收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Qeelin', 'JD', '************', 0, 0, 1, '********', '********', '**********', 'QE9002', 1, '京东店收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'JD', '************', 0, 0, 1, '********', '********', '**********', 'YS3002', 1, '京东商场收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'TMWECHAT', '**********', 1, 0, 1, '', '', '', '', 0, '天猫店微信支付收款', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('AMQ', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '官网和收钱吧收款开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BAL', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '官网&小程序收款开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BOU', 'WOSAIPAY', '************* ', 0, 0, 1, '', '', '', '', 0, '小程序和企业购小程序开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('BV', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '官网*小程序*线上员工内卖都开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('Gucci', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '官网和小程序开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '官网收款开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (brand, channel, merchant_id, is_tmall, is_wosaipay_merchant, is_sync_to_blackline, sap_gl_account1, sap_gl_account2, sap_profit_center, sap_merchant_code, is_sync_to_sap, note, created_date, modified_date, deleted, version) VALUES ('YSL', 'WOSAIPAY', '*************', 0, 0, 1, '', '', '', '', 0, '小程序收款开启收钱吧', now(), now(), 0, 0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','AMQ',0,0,1,'','********','**********','MQ5081',1,'旧账号有余额','2025-07-17 13:51:25',null,'2025-07-17 13:51:25',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','AMQ',0,0,1,'','********','**********','MQ4524',1,'旧账号有余额','2025-08-01 15:02:07',null,'2025-07-17 13:51:25',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','AMQ',0,1,0,'********','********','**********','MQ8643',1,'官网和微信小程序收款','2025-08-01 14:49:32',null,'2025-07-17 13:51:25',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','BAL',0,1,0,'********','********','**********','BA8082',1,'官网&小程序收款共用','2025-07-17 13:51:25',null,'2025-07-17 13:51:25',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','BOU',0,0,1,'********','********','**********','BO8421',1,'企业购小程序','2025-08-01 14:50:10',null,'2025-07-17 13:51:25',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','BOU',0,1,0,'********','********','**********','BO9959',1,'微信小程序收款','2025-07-17 13:51:26',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','BRIONI',0,0,1,'********','********','**********','BR7111',1,'官网和小程序收款','2025-07-22 14:59:28',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','BV',0,1,0,'********','********','**********','BV6970',1,'官网*小程序*线上员工内卖收款','2025-07-17 13:51:26',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','GUCCI',0,1,0,'********','********','**********','GG7702',1,'官网收款','2025-08-01 14:51:06',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','GUCCI',0,0,1,'********','********','**********','GG4645',1,'机场店铺收款','2025-07-22 14:59:28',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','GUCCI',0,1,0,'********','********','**********','GG7197',1,'小程序收款','2025-07-22 14:59:28',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','GUCCI',0,0,1,'********','********','**********','GG5557',1,'官网百度-收款','2025-07-22 14:59:28',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','GUCCI',0,0,1,'********','********','**********','GG2337',1,'员工内购小程序收款','2025-07-22 14:59:28',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','Gucci',0,1,0,'********','********','**********','GG4587',1,'官网','2025-07-28 16:09:05',null,'2025-07-28 16:09:05',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','POM',0,0,1,'********','********','**********','PO9191',1,'小程序收款','2025-07-17 13:51:26',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','QEELIN',0,0,1,'********','********','**********','QE0402',1,'小程序收款','2025-08-01 14:50:30',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','YSL',0,0,1,'','********','**********','YS0402',1,'旧账号有余额','2025-07-17 13:51:26',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','YSL',0,0,1,'********','********','**********','YS0391',1,'小程序维修费收款','2025-07-17 13:51:26',null,'2025-07-17 13:51:26',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','YSL',0,1,0,'********','********','**********','YS2934',1,'官网和小程序收款','2025-07-17 13:51:27',null,'2025-07-17 13:51:27',null,0,null,0);
INSERT INTO t_merchant_config (channel,merchant_id,brand,is_tmall,is_wosaipay_merchant,is_sync_to_blackline,sap_gl_account1,sap_gl_account2,sap_profit_center,sap_merchant_code,is_sync_to_sap,note,created_date,created_by,modified_date,modified_by,deleted,tenant_id,version) VALUES ('WECHAT','**********','YSL',0,0,1,'********','********','**********','YS3318',1,'机场店铺收款','2025-07-17 13:51:27',null,'2025-07-17 13:51:27',null,0,null,0);

/*
SELECT brand '品牌', channel '渠道', merchant_id '商户号',
	is_tmall '是否TMALL', is_wosaipay_merchant '是否开启收钱吧', is_sync_to_blackline '是否需要同步Blackline',
	sap_gl_account1 'SAP总账科目', sap_gl_account2 'SAP清算科目', sap_profit_center 'SAP PC', sap_merchant_code 'SAP商户短号', is_sync_to_sap '是否需要同步SAP'
FROM t_merchant_config;
*/

/*-
 * Init JD & Wosaipay grant config.
 */
INSERT INTO t_merchant_grant_config(channel, merchant_id, grant_type, is_enabled, created_date, modified_date, deleted, version)
SELECT channel, merchant_id, 'SFTP', 1, now(), now(), 0, 0 FROM t_merchant_config where channel IN ('JD', 'WOSAIPAY');


/*-
 * Init Wechat v3 grant config.
 */
INSERT INTO t_merchant_grant_config(channel, merchant_id, grant_type, api_version, is_enabled, created_date, modified_date, deleted, version)
SELECT channel, merchant_id, 'MERCHANT', 'v3', 1, now(), now(), 0, 0
FROM t_merchant_config
WHERE channel = 'WECHAT'
  AND merchant_id in ('**********','**********');

/*-
 * Init Wechat v2 grant config.
 */
INSERT INTO t_merchant_grant_config(channel, merchant_id, grant_type, api_version, is_enabled, created_date, modified_date, deleted, version)
SELECT channel, merchant_id, 'MERCHANT', 'v2', 1, now(), now(), 0, 0
FROM t_merchant_config
WHERE channel = 'WECHAT'
  AND merchant_id not in ('**********','**********') /* SKIP v3 */
  AND merchant_id IN (
                      '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    , '**********'
    );

/*-
 * Update wechat kms brand id.
 */
UPDATE t_merchant_grant_config SET kms_brand_id='55' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='57' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='63' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='58' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='51' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='93' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='90' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='92' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='92' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='51' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='53' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='51' WHERE channel = 'WECHAT' AND merchant_id = '**********';
UPDATE t_merchant_grant_config SET kms_brand_id='51' WHERE channel = 'WECHAT' AND merchant_id = '**********';


ALTER TABLE t_bill_task ADD sap_archive_excel_url varchar(300) NULL COMMENT 'SAP文件url（xlsx）';
ALTER TABLE t_bill_task ADD blackline_archive_excel_url varchar(300) NULL COMMENT 'blackline文件url(xlsx)';

ALTER TABLE t_bill_task ADD business_date DATETIME NULL COMMENT '账单业务日期';

