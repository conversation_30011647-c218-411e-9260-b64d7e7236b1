sonar.issue.ignore.multicriteria=e1,e2
sonar.issue.ignore.multicriteria.e1.ruleKey=java:S110
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*
sonar.issue.ignore.multicriteria.e2.ruleKey=java:S6813
sonar.issue.ignore.multicriteria.e2.resourceKey=**/*
sonar.coverage.exclusions=/src/main/java/**/*Application.java,\
/src/main/java/**/dto/*.java,\
/src/main/java/**/dao/*.java,\
/src/main/java/**/mapper/*.java,\
/src/main/java/**/entity/*.java,\
/src/main/java/**/config/*.java,\
/src/main/java/**/config/**/*.java,\
/src/main/java/**/client/**/*.java,\
/src/main/java/**/converter/*.java,\
/src/main/java/**/exception/*.java,\
/src/main/java/**/constant/*.java
