<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.finance.billcenter.mapper.BillTaskMapper">
    <update id="updateBlacklineSyncStateByIdAndSyncStatus">
        UPDATE t_bill_task
        SET blackline_sync_state = #{newSyncState}
          , version = version + 1
          , modified_date = now()
        WHERE 1 = 1
        AND id IN <foreach item="taskId" collection="taskIds" open="(" close=")" separator=",">#{taskId}</foreach>
        AND blackline_sync_state IN <foreach item="st" collection="oldSyncStatus" open="(" close=")" separator=",">#{st}</foreach>
    </update>
    <update id="updateBlacklineSyncStateErrorAndArchiveUrlByIdAndSyncState" parameterType="com.kering.cus.finance.billcenter.dto.BillTaskDTO">
        UPDATE t_bill_task
        SET blackline_sync_state = #{newSyncState}
          , blackline_archive_url = #{archiveUrl}
          , blackline_archive_excel_url = #{archiveExcelUrl}
          , blackline_error_count = #{errorCount}
          , error_msg = #{errorMsg}
          , blackline_error_msg = #{errorMsg}
          , blackline_next_run_time = #{nextRunTime}
          , version = version + 1
          , modified_date = now()
        WHERE blackline_sync_state IN <foreach item="st" collection="syncStatus" open="(" close=")" separator=",">#{st}</foreach>
        AND id IN <foreach item="taskId" collection="taskIds" open="(" close=")" separator=",">#{taskId}</foreach>
    </update>
    <update id="updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime">
        UPDATE t_bill_task
        SET state = #{taskState}
          , finished_date = #{finishedDate}
          , version = version + 1
          , modified_date = now()
        WHERE created_date <![CDATA[ >= ]]> #{since}
        AND created_date <![CDATA[ < ]]> #{until}
        AND sap_sync_state IN <foreach item="sapSyncState" collection="sapSyncStatus" open="(" close=")" separator=",">#{sapSyncState}</foreach>
        AND blackline_sync_state IN <foreach item="blacklineSyncState" collection="blacklineSyncStatus" open="(" close=")" separator=",">#{blacklineSyncState}</foreach>
        AND state NOT IN <foreach item="taskState" collection="ignoreTaskStatus" open="(" close=")" separator=",">#{taskState}</foreach>
    </update>
    <update id="updateSapSyncStateAndNextRunTimeByIdAndSyncStatus">
        UPDATE t_bill_task
        SET sap_sync_state = #{newSapSyncState}
          , sap_next_run_time = #{sapNextRunTime}
          , version = version + 1
          , modified_date = now()
        WHERE id = #{taskId}
        AND sap_sync_state IN <foreach item="st" collection="sapSyncStatus" open="(" close=")" separator=",">#{st}</foreach>
    </update>


  <update id="updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus" parameterType="com.kering.cus.finance.billcenter.dto.BillTaskDTO">
    UPDATE t_bill_task
    SET sap_sync_state = #{newSyncState}
    , sap_next_run_time = #{nextRunTime}
    , sap_error_count = #{errorCount}
    , sap_error_msg = #{errorMsg}
    , sap_archive_url = #{archiveUrl}
    , sap_archive_excel_url = #{archiveExcelUrl}
    , version = version + 1
    , modified_date = now()
    WHERE id = #{taskId}
    AND sap_sync_state IN <foreach item="st" collection="syncStatus" open="(" close=")" separator=",">#{st}</foreach>
  </update>

    <select id="findTasksByBlacklineStatus" resultType="com.kering.cus.finance.billcenter.entity.BillTaskEntity">
        SELECT channel,
               is_tmall as isTmall,
               ANY_VALUE(error_count) as errorCount,
               ANY_VALUE(error_msg)   as errorMsg
        FROM t_bill_task
        WHERE created_date >= #{createDate}
          AND blackline_sync_state = #{blacklineSyncState}
          AND is_sync_to_blackline = 1
        GROUP BY channel,is_tmall
    </select>
</mapper>