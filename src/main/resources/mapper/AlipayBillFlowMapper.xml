<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.finance.billcenter.mapper.AlipayBillFlowMapper">


    <sql id="alipayBillFlowColumns">
	    <![CDATA[
        id as id,
	        	task_id as taskId,
	        	brand as brand,
	        	channel as channel,
	        	merchant_id as merchantId,
	        	account_transaction_no as accountTransactionNo,
	        	biz_transaction_no as bizTransactionNo,
	        	merchant_order_id as merchantOrderId,
	        	sku_name as skuName,
	        	transaction_time as transactionTime,
	        	reciprocal_account as reciprocalAccount,
	        	income as income,
	        	expense as expense,
	        	balance as balance,
	        	transaction_channel as transactionChannel,
	        	transaction_type as transactionType,
	        	note as note,
	        	biz_desc as bizDesc,
	        	biz_order_id as bizOrderId,
	        	biz_base_order_id as bizBaseOrderId,
	        	biz_bill_source as bizBillSource,
	        	final_biz_base_order_id as finalBizBaseOrderId,
	        	is_tmall as isTmall,
	        	is_sync_to_blackline as isSyncToBlackline,
	        	is_sync_to_sap as isSyncToSap,
	        	created_date as createdDate,
	        	created_by as createdBy,
	        	modified_date as modifiedDate,
	        	modified_by as modifiedBy,
	        	deleted as deleted,
	        	tenant_id as tenantId,
	        	version as version
        ]]>
	</sql>


</mapper>