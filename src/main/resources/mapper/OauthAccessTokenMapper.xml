<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.finance.billcenter.mapper.OauthAccessTokenMapper">
  <sql id="columns">
        id
      , version
      , created_date
      , created_by
      , modified_by
      , modified_date
      , tenant_id
      , deleted
      , platform
      , client_id
      , user_id
      , open_id
      , merchant_id
      , access_token
      , refresh_token
      , expires_at
      , re_expires_at
      , error_count
      , error_msg
      , next_refresh_time
  </sql>
  <select id="findByPlatformAndMerchantId" resultType="com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity">
    SELECT <include refid="columns"></include>
    FROM t_oauth_access_token
    WHERE platform = #{platform}
    AND merchant_id = #{merchantId}
    AND deleted = 0
  </select>
  <select id="findRefreshAccessTokenIds" resultType="java.lang.Long">
    SELECT ID
    FROM t_oauth_access_token
    WHERE next_refresh_time <![CDATA[ <= ]]> #{when}
    AND error_count <![CDATA[ < ]]> #{maxRetryTimes}
    AND deleted = 0
    ORDER BY next_refresh_time
    LIMIT #{limit}
  </select>
</mapper>