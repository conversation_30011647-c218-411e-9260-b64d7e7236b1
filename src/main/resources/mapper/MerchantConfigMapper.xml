<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kering.cus.finance.billcenter.mapper.MerchantConfigMapper">
    <select id="findByChannelAndBrandIgnoreCase"
            resultType="com.kering.cus.finance.billcenter.entity.MerchantConfigEntity">
        SELECT
            *
        FROM t_merchant_config
        WHERE deleted = 0
        AND channel = #{channel}
        AND UPPER(brand) = UPPER(#{brand})
    </select>
</mapper>