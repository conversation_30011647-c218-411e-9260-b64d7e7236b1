# disable CUS persistence tenant.
persistence.database.tenant.isolation.enabled=false

# Queue Message SDK Integration
queue.provider=kafka

# Storage SDK Integration
storage.provider=oss

# Secret Management SDK Integration
secret.provider=kms

# Scheduler SDK Integration
scheduler.provider=schedulerx

# Application core config
core.alipay.callback.url=https://qa-galassia-openapi.kering.cn/services/dev-cus-api/alipay-oauth2-notify
core.retry.backoff=false
core.retry.max-times=3
core.task.acquire.timeout-minutes=5
core.task.process.timeout-minutes=10
core.task.archive.timeout-minutes=15
# Application SFTP config
sftp.connect-timeout=0
# Application JD config
jd.trade.path.pattern=\\/cfs-billcenter-dev\\/jdpay\\/in\\/trade\\/.*
jd.trade.path.merchant-pattern=\\/([^/_]+)_\\d+.zip$
jd.trade.path.acquire-dir-name=in
jd.trade.path.acquired-dir-name=processed
jd.wallet.path.pattern=\\/cfs-billcenter-dev\\/jdpay\\/in\\/wallet\\/.*
jd.wallet.path.merchant-pattern=\\/([^/-]+)-\\d+.csv$
jd.wallet.path.acquire-dir-name=in
jd.wallet.path.acquired-dir-name=processed
# Application Wosaipay config
wosaipay.trade.path.pattern=\\/cfs-billcenter-dev\\/wosaipay\\/in\\/.*
wosaipay.trade.path.brand-pattern=_([^/_]+)_\\d+.xlsx$
wosaipay.trade.path.acquire-dir-name=in
wosaipay.trade.path.acquired-dir-name=processed
# SAP crush FTP config
sap.sftp.archive.jd.dir=/IFI091801/JD/in
sap.sftp.archive.wechat.dir=/IFI091801/Wechat/in
sap.sftp.archive.alipay.dir=/IFI091801/Alipay/in
# Blackline CFS config
blackline.sftp.archive.wechat.dir=/Blackline_CreditCard/acquirerfile/wechatpay_facade
blackline.sftp.archive.alipay.dir=/Blackline_CreditCard/acquirerfile/alipay_facade
blackline.sftp.archive.jd.dir=/Blackline_CreditCard/acquirerfile/JD
blackline.sftp.archive.wosaipay.dir=/Blackline_CreditCard/acquirerfile/Wosaipay


SYSTEM_ID=1941014799638802433
SENDER_ID=291
TENANT_ID=1