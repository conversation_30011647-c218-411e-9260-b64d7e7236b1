package com.kering.cus.finance.billcenter.service.wosaipay;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.WosaipayBillConverter;
import com.kering.cus.finance.billcenter.dao.WosaipayTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelReaderUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import com.kering.cus.finance.billcenter.vo.WosaipayTradeFlowVO;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import java.util.StringJoiner;


@Slf4j
@Service
public class WosaipayBillprocessHandler implements BillProcessHandler {

    @Resource
    private WosaipayTradeFlowDAO wosaipayTradeFlowDAO;

    @Resource
    private WosaipayBillConverter wosaipayBillConverter;

    @Override
    public boolean matches(Channel channel, GrantType grantType, BillType billType) {
        return Objects.equals(channel, Channel.WOSAIPAY) && Objects.equals(grantType, GrantType.SFTP)
                && Objects.equals(billType, BillType.TRADE_FLOW);
    }

    @Override
    @Transactional
    public List<String> process(File tempFile, BillTaskEntity task) {
        log.info("wosaipay  bill processing starts,brand :{}", task.getBrand());
        if (Objects.isNull(tempFile) || !tempFile.getName().endsWith(".xlsx")) {
            log.warn("wosaipay billing format is incorrect,brand : {}", task.getBrand());
            return Collections.emptyList();
        }

        List<WosaipayTradeFlowEntity> list = Lists.newArrayList();
        Set<String> merchantIds = Sets.newHashSet();
        Set<String> uniqueKeys = Sets.newHashSet();
        try (InputStream is = new FileInputStream(tempFile)) {
            ExcelReaderUtils.readInBatches(is, "明细", 7, 500, data -> {
                data.forEach(row -> {
                    WosaipayTradeFlowVO wosaipayTradeFlowVO = JsonUtil.convertValue(StringUtils.parseAndFormatMap(row), WosaipayTradeFlowVO.class);
                    WosaipayTradeFlowEntity entity = wosaipayBillConverter.toWosaipayBillEntity(task, wosaipayTradeFlowVO);

                    WosaipayTradeFlowEntity uniqueDetail = wosaipayTradeFlowDAO.getBillDetailByParams(entity);

                    String uniqueKey = getUniqueKey(wosaipayTradeFlowVO);

                    if (!uniqueKeys.contains(uniqueKey) && Objects.isNull(uniqueDetail)) {
                        list.add(entity);
                        uniqueKeys.add(uniqueKey);
                    }

                    merchantIds.add(wosaipayTradeFlowVO.getMerchantId());
                });
                if (!CollectionUtils.isEmpty(list)) {
                    wosaipayTradeFlowDAO.createBatch(list);
                    list.clear();
                }
            });

        } catch (Exception e) {
            log.error("reader wosaipay bill processing error", e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED, e);
        }
        log.info("wosaipay  bill processing ends,brand :{}", task.getBrand());
        return merchantIds.stream().toList();
    }



    @Override
    public void determineBusinessDate(BillTaskEntity task) {
        String dateStr = task.getExtraParams().substring(task.getExtraParams().lastIndexOf("_") + 1, task.getExtraParams().lastIndexOf("."));
        ZonedDateTime zonedDateTime;
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDD);
            LocalDate localDate = LocalDate.parse(dateStr, formatter);
            zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        }catch (Exception e){
            log.error("determineBusinessDate error,data : {}",dateStr);
            zonedDateTime=task.getBillStartTime();
        }
        task.setBusinessDate(zonedDateTime);
    }
    private String getUniqueKey(WosaipayTradeFlowVO wosaipayTradeFlowVO) {
        StringJoiner joiner = new StringJoiner("_");
        joiner.add(wosaipayTradeFlowVO.getMerchantId())
                .add(wosaipayTradeFlowVO.getPosTransactionNo())
                .add(wosaipayTradeFlowVO.getTransactionType())
                .add(wosaipayTradeFlowVO.getPaymentChannelOrderId())
                .add(wosaipayTradeFlowVO.getPaymentAccount());
        return joiner.toString();
    }
}