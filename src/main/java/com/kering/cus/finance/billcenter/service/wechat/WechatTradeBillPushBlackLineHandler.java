package com.kering.cus.finance.billcenter.service.wechat;


import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.SYMBOL_COMMA;
import static com.kering.cus.finance.billcenter.util.CsvParserUtil.appendCsvField;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.WechatTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.WechatTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractBlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @ClassName WechatTradeBillPushBlackLineHandler
 * @Description Transaction bill push task
 * @Date 2025-07-14 10:10
 * @Version V1.0
 **/
@Service
@Slf4j
public class WechatTradeBillPushBlackLineHandler extends AbstractBlacklineBillArchiveHandler implements BlacklineBillArchiveHandler {

  @Resource
  private WechatTradeFlowDAO wechatTradeFlowDAO;

  private static final byte[] UTF8_BOM = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};


  private static final String HEADERS = "Brand,Channel,Settlement Merchant ID,Transaction Time,Official Account ID,Merchant ID,Special Merchant ID,Device Number,WeChat Order Number,Merchant Order Number,User Identifier,Transaction Type,Transaction Status,Paying Bank,Currency Type,Settlement Order Amount,Voucher Amount,WeChat Refund Number,Merchant Refund Number,Refund Amount,Recharge Voucher Refund Amount,Refund Type,Refund Status,Product Name,Merchant Data Package,Handling Fee,Fee Rate,Order Amount,Applied Refund Amount,Fee Rate Remarks";


  public WechatTradeBillPushBlackLineHandler(
      SftpConfig sftpConfig) {
    super(Channel.WECHAT, sftpConfig);
  }


  @Override
  public boolean isGroupingByTmall() {
    return false;
  }

  @Override
  public Map<String, File> archive(Group group, long seqInGroup, List<Long> taskIds) throws IOException {
    long total = wechatTradeFlowDAO.selectTradeBillCount(taskIds);
    Map<String, File> mapFile = new HashMap<>();
    if (total == 0) {
      log.warn("WeChat trade bill push blackLine no data, param={}", taskIds);
      return mapFile;
    }
    mapFile.put(Constants.SUFFIX_CSV, writeCsvFile(total,taskIds));
    mapFile.put(Constants.SUFFIX_XLSX, writeExcelFile(taskIds));
    return mapFile;
  }


  private File writeCsvFile(long total,List<Long> taskIds) throws IOException {
    File targetFile = FileUtils2.createTempFile(Channel.WECHAT.name() + BillType.TRADE_FLOW.name(), Constants.SUFFIX_CSV).toFile();
    try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(targetFile), StandardCharsets.UTF_8))) {
      writer.write(new String(UTF8_BOM, StandardCharsets.UTF_8));
      writer.write(HEADERS+"\n");
      long pages = (total + BATCH_SIZE - 1) / BATCH_SIZE;

      for (long page = 1; page <= pages; page++) {
        List<WechatTradeFlowEntity> wechatTradeFlowEntityList = wechatTradeFlowDAO.selectTradeBillPage(new Page<>(page, BATCH_SIZE), taskIds)
            .getRecords();
        for (WechatTradeFlowEntity wechatTradeFlowEntity : wechatTradeFlowEntityList) {
          writer.write(formatCsvLine(wechatTradeFlowEntity));
        }
      }
    } catch (Exception e) {
      log.error("WechatTradeBillPushBlackLineHandler.archive blackLine execute fail", e);
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_TRADE_GENERATE_FAIL_BLACKLINE, e);
    }
    if (!targetFile.exists() || targetFile.length() == 0) {
      throw new IOException("WechatTradeBillPushBlackLineHandler CSV file is empty or does not exist");
    }
    return targetFile;
  }




  private File writeExcelFile(List<Long> taskIds) throws IOException {
    File targetFile = FileUtils2.createTempFile(Channel.WECHAT.name() + BillType.FUND_FLOW.name(), Constants.SUFFIX_XLSX).toFile();
    ExcelWriterUtils writer = new ExcelWriterUtils();
    writer.createSheet("Wechat_Blackline").writeHeader(HEADERS.split(SYMBOL_COMMA));
    int pageNum=1;
    Page<WechatTradeFlowEntity> pageData;
    do {
      pageData = wechatTradeFlowDAO.selectTradeBillPage(new Page<>(pageNum, BATCH_SIZE), taskIds);
      List<WechatTradeFlowEntity> records = Optional.ofNullable(pageData.getRecords()).orElse(Lists.newArrayList());
      records.forEach(wechatTradeFlowEntity -> writer.writeRow(getContent(wechatTradeFlowEntity)));
      pageNum++;
    } while (pageData.hasNext());
    writer.writeTo(targetFile);
    return targetFile;
  }

  private String[] getContent(WechatTradeFlowEntity wechatTradeFlowEntity){
    return new String[] {
        wechatTradeFlowEntity.getBrand(),
        wechatTradeFlowEntity.getChannel().toString(),
        wechatTradeFlowEntity.getMerchantId(),
        DateUtils.convertDateStr(wechatTradeFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD),
        wechatTradeFlowEntity.getAppId(),
        wechatTradeFlowEntity.getMerchantId(),
        wechatTradeFlowEntity.getSubMerchant(),
        wechatTradeFlowEntity.getDeviceNo(),
        wechatTradeFlowEntity.getWechatOrderId(),
        wechatTradeFlowEntity.getMerchantOrderId(),
        wechatTradeFlowEntity.getOpenId(),
        wechatTradeFlowEntity.getTransactionType(),
        wechatTradeFlowEntity.getTradeStatus(),
        wechatTradeFlowEntity.getPaymentBank(),
        wechatTradeFlowEntity.getCurrency(),
        wechatTradeFlowEntity.getSettlementAmount() != null ?
            wechatTradeFlowEntity.getSettlementAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getVoucherAmount() != null ?
            wechatTradeFlowEntity.getVoucherAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getWechatRefundId(),
        wechatTradeFlowEntity.getMerchantRefundId(),
        wechatTradeFlowEntity.getRefundAmount() != null ?
            wechatTradeFlowEntity.getRefundAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getRechargeRefundAmount() != null ?
            wechatTradeFlowEntity.getRechargeRefundAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getRefundType(),
        wechatTradeFlowEntity.getRefundStatus(),
        wechatTradeFlowEntity.getSkuName(),
        wechatTradeFlowEntity.getMerchantData(),
        wechatTradeFlowEntity.getServiceCharge() != null ?
            wechatTradeFlowEntity.getServiceCharge().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getTransactionRate(),
        wechatTradeFlowEntity.getOrderAmount() != null ?
            wechatTradeFlowEntity.getOrderAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getApplyRefundAmount() != null ?
            wechatTradeFlowEntity.getApplyRefundAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
        wechatTradeFlowEntity.getTransactionRateNote()
    };
  }

  /**
   * Build CSV line with header mapping
   *
   * @param wechatTradeFlowEntity Database record
   * @return Formatted CSV line
   */
  String formatCsvLine(WechatTradeFlowEntity wechatTradeFlowEntity) {
    StringBuilder line = new StringBuilder();
    appendCsvField(line, wechatTradeFlowEntity.getBrand());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getChannel().toString());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getMerchantId());
    line.append(",");
    appendCsvField(line, DateUtils.convertDateStr(wechatTradeFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD));
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getAppId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getMerchantId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getSubMerchant());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getDeviceNo());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getWechatOrderId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getMerchantOrderId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getOpenId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getTransactionType());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getTradeStatus());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getPaymentBank());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getCurrency());
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getSettlementAmount() != null ? wechatTradeFlowEntity.getSettlementAmount().setScale(2, RoundingMode.HALF_UP).toString()
            : null);
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getVoucherAmount() != null ? wechatTradeFlowEntity.getVoucherAmount().setScale(2, RoundingMode.HALF_UP).toString()
            : null);
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getWechatRefundId());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getMerchantRefundId());
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getRefundAmount() != null ? wechatTradeFlowEntity.getRefundAmount().setScale(2, RoundingMode.HALF_UP).toString()
            : null);
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getRechargeRefundAmount() != null ? wechatTradeFlowEntity.getRechargeRefundAmount().setScale(2, RoundingMode.HALF_UP)
            .toString() : null);
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getRefundType());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getRefundStatus());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getSkuName());
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getMerchantData());
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getServiceCharge() != null ? wechatTradeFlowEntity.getServiceCharge().setScale(2, RoundingMode.HALF_UP).toString()
            : null);
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getTransactionRate());
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getOrderAmount() != null ? wechatTradeFlowEntity.getOrderAmount().setScale(2, RoundingMode.HALF_UP).toString() : null);
    line.append(",");
    appendCsvField(line,
        wechatTradeFlowEntity.getApplyRefundAmount() != null ? wechatTradeFlowEntity.getApplyRefundAmount().setScale(2, RoundingMode.HALF_UP)
            .toString() : null);
    line.append(",");
    appendCsvField(line, wechatTradeFlowEntity.getTransactionRateNote());
    line.append("\n");
    return line.toString();
  }

  @Override
  protected String getBlacklineSftpTargetDirectory() {
    return sftpConfig.getBlacklineSftpWechatArchiveDirectory();
  }
}
