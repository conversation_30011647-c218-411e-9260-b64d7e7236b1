package com.kering.cus.finance.billcenter.constant;

public enum WechatAccountV2Type {
  BASIC("Basic"),
  OPERATION("Operation"),
  FEES("Fees");

  private final String value;

  WechatAccountV2Type(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public static WechatAccountV2Type fromValue(String value) {
    for (WechatAccountV2Type type : WechatAccountV2Type.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid WeChat v2 fund account type: " + value);
  }
}
