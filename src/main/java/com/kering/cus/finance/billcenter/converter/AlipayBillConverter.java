package com.kering.cus.finance.billcenter.converter;

import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.vo.AlipayBillFlowVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Optional;

@Mapper(componentModel = "spring", imports = {StringUtils.class, Date.class, Optional.class})
public abstract class AlipayBillConverter {

    @Mapping(target = "taskId", source = "task.id")
    @Mapping(target = "brand", source = "task.brand")
    @Mapping(target = "channel", source = "task.channel")
    @Mapping(target = "merchantId", source = "task.merchantId")
    @Mapping(target = "isSyncToBlackline", source = "task.isSyncToBlackline")
    @Mapping(target = "isSyncToSap", source = "task.isSyncToSap")
    @Mapping(target = "isTmall", source = "task.isTmall")
    @Mapping(target = "accountTransactionNo", source = "billFlow.accountTransactionNo")
    @Mapping(target = "bizTransactionNo", source = "billFlow.bizTransactionNo")
    @Mapping(target = "merchantOrderId", source = "billFlow.merchantOrderId")
    @Mapping(target = "skuName", source = "billFlow.skuName")
    @Mapping(target = "transactionTime", source = "billFlow.transactionTime")
    @Mapping(target = "reciprocalAccount", source = "billFlow.reciprocalAccount")
    @Mapping(target = "income", source = "billFlow.income")
    @Mapping(target = "expense", source = "billFlow.expense")
    @Mapping(target = "balance", source = "billFlow.balance")
    @Mapping(target = "transactionChannel", source = "billFlow.transactionChannel")
    @Mapping(target = "transactionType", source = "billFlow.transactionType")
    @Mapping(target = "note", source = "billFlow.note")
    @Mapping(target = "bizDesc", source = "billFlow.bizDesc")
    @Mapping(target = "bizOrderId", source = "billFlow.bizOrderId")
    @Mapping(target = "bizBaseOrderId", source = "billFlow.bizBaseOrderId")
    @Mapping(target = "bizBillSource", source = "billFlow.bizBillSource")
    @Mapping(target = "finalBizBaseOrderId", source = "billFlow.finalBizBaseOrderId")
    public abstract AlipayBillFlowEntity toAlipayBillFlowEntity(BillTaskEntity task, AlipayBillFlowVO billFlow);

}
