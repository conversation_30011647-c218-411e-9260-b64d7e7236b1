package com.kering.cus.finance.billcenter.service.jd;

import com.kering.cus.finance.billcenter.config.JdBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractSftpBillTaskCreateService;
import com.kering.cus.finance.billcenter.util.BizPreconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JD bill task create service.
 *
 * <AUTHOR>
 * @since 20250708
 */
@Slf4j
@Service
public class JdBillTaskCreateService extends AbstractSftpBillTaskCreateService {
    private final JdBillConfig jdBillConfig;

    public JdBillTaskCreateService(final JdBillConfig jdBillConfig,
                                   final BillTaskDAO billTaskDAO,
                                   final TransitLogDAO transitLogDAO,
                                   final MerchantConfigDAO merchantConfigDAO) {
        super(Channel.JD, billTaskDAO, transitLogDAO, merchantConfigDAO);
        this.jdBillConfig = jdBillConfig;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final String sftpPath) {
        return null != determineBillType(sftpPath);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public Long createTaskIfNecessary(final String sftpPath, final ZonedDateTime when, final String source) {
        final BillType billType = determineBillType(sftpPath);
        if (null == billType) {
            log.warn("[SKIP] illegal JD sftp path: {}", sftpPath);
            return null;
        }

        final String merchantId = determineMerchantId(billType, sftpPath);
        BizPreconditions.checkHasText(merchantId, BillCenterErrorCode.MERCHANT_CAN_NOT_DETERMINED);

        return doCreateTaskIfNecessary(null, merchantId, billType, sftpPath, when, source);
    }

    /**
     * Determine bill type by sftp path.
     *
     * @param sftpPath the SFTP path
     * @return the bill type
     */
    BillType determineBillType(final String sftpPath) {
        if (!StringUtils.hasText(sftpPath)) {
            return null;
        }
        if (sftpPath.matches(jdBillConfig.getTradePathPattern())) {
            return BillType.TRADE_FLOW;
        }
        if (sftpPath.matches(jdBillConfig.getWalletPathPattern())) {
            return BillType.FUND_FLOW;
        }
        return null;
    }

    /**
     * Determine merchant id by bill type and sftp path.
     *
     * @param billType the bill type
     * @param sftpPath the SFTP path
     * @return the merchant id
     */
    String determineMerchantId(final BillType billType, final String sftpPath) {
        if (!StringUtils.hasText(sftpPath)) {
            return null;
        }

        String pattern;
        if (BillType.TRADE_FLOW.equals(billType)) {
            pattern = jdBillConfig.getTradePathMerchantPattern();
        } else if (BillType.FUND_FLOW.equals(billType)) {
            pattern = jdBillConfig.getWalletPathMerchantPattern();
        } else {
            return null;
        }

        final Matcher matcher = Pattern.compile(pattern).matcher(sftpPath);
        return matcher.find() ? matcher.group(1) : null;
    }

    @Override
    protected boolean determineSyncToSap(BillType billType, boolean udfSyncToSap) {
        return BillType.FUND_FLOW.equals(billType) && udfSyncToSap;
    }

    @Override
    protected boolean determineSyncToBlackline(BillType billType, boolean udfSyncToBlackline) {
        final List<BillType> types = Arrays.asList(BillType.TRADE_FLOW, BillType.FUND_FLOW);
        return types.contains(billType) && udfSyncToBlackline;
    }

}