package com.kering.cus.finance.billcenter.service.wosaipay;

import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.config.WosaipayBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.service.support.AbstractSftpAcquireHandler;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import org.springframework.stereotype.Component;

@Component
public class WosaipayTradeFlowAcquireHandler extends AbstractSftpAcquireHandler {
    private static final String PATH_SEP = SftpUtils.PATH_SEPARATOR;

    private final WosaipayBillConfig wosaipayBillConfig;

    public WosaipayTradeFlowAcquireHandler(final SftpConfig sftpConfig, final WosaipayBillConfig wosaipayBillConfig) {
        super(sftpConfig, Channel.WOSAIPAY, BillType.TRADE_FLOW);
        this.wosaipayBillConfig = wosaipayBillConfig;
    }

    @Override
    protected String determineAcquiredSftpPath(String sftpPath) {
        return sftpPath.replace(
                PATH_SEP + wosaipayBillConfig.getTradePathAcquireDirName() + PATH_SEP,
                PATH_SEP + wosaipayBillConfig.getTradePathAcquiredDirName() + PATH_SEP
        );
    }
}
