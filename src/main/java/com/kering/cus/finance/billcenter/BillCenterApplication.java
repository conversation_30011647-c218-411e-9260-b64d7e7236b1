package com.kering.cus.finance.billcenter;

import com.kering.cus.lib.rest.consumer.exchange.autoinject.HttpExchangesScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration;

@SpringBootApplication(scanBasePackages = "com.kering.cus", exclude = SpringApplicationAdminJmxAutoConfiguration.class)
@HttpExchangesScan(basePackages = {"com.kering.cus.finance.billcenter.client"})
@MapperScan("com.kering.cus.finance.billcenter.mapper")
public class BillCenterApplication {

	public static void main(String[] args) {
		SpringApplication.run(BillCenterApplication.class, args);
	}
}
