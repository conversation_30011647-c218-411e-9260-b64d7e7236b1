package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.lib.storage.StorageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@RequiredArgsConstructor
@Service
public class AppStorageService {
    private final BillCenterConfig billCenterConfig;
    private final StorageService storageService;

    public String writeStream(final String path, final InputStream in) {
        return storageService.writeStream(in, getBucket(), path);
    }

    public InputStream readStream(final String path) {
        return storageService.readStream(getBucket(), path);
    }

    private String getBucket() {
        return billCenterConfig.getStorageBucket();
    }

}