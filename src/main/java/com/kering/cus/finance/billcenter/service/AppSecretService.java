package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.lib.secret.access.SecretAccessService;
import freework.net.Http;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AppSecretService {

    /**
     * ISV app secret KMS relative path.
     * <p>
     * kering/${channel}/isv-app-config.
     */
    private static final String ISV_APP_SECRET_PATH_PATTERN = "kering/%s/isv-app-config";

    /**
     * Merchant self dev app secret KMS relative path.
     * <p>
     * ${brandid}/${channel}/${merchant}/mch-app-config.
     */
    private static final String MCH_APP_SECRET_PATH_PATTERN = "%s/%s/%s/mch-app-config";

    private final BillCenterConfig config;
    private final SecretAccessService secretAccessService;

    public <T> T getIsvAppSecret(String channel, Class<T> propsType) {
        if(StringUtils.isBlank(channel)){
            return null;
        }
        return doGetSecret(String.format(ISV_APP_SECRET_PATH_PATTERN, channel.toLowerCase()), propsType);
    }

    public <T> T getMerchantAppSecret(String brand, String channel,
                                      String merchantId, Class<T> propsType) {
        if(StringUtils.isBlank(brand) || StringUtils.isBlank(channel) || StringUtils.isBlank(merchantId)){
            return null;
        }
        final String path = String.format(MCH_APP_SECRET_PATH_PATTERN, brand.toLowerCase(), channel.toLowerCase(), merchantId);
        return doGetSecret(path, propsType);
    }

    private <T> T doGetSecret(String relativePath, Class<T> propsType) {
        final String secretAbsPath = Http.resolveUrl(
                config.getSecretAccessRootPath(), relativePath
        );
        return secretAccessService.getSecretValue(secretAbsPath, propsType);
    }

}
