package com.kering.cus.finance.billcenter.dao;

import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import com.kering.cus.finance.billcenter.mapper.OauthAccessTokenMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public class OauthAccessTokenDAO extends MyBatisBaseDAO<OauthAccessTokenEntity, OauthAccessTokenMapper, Long> {

    public Optional<OauthAccessTokenEntity> findByPlatformAndMerchantId(Channel platform, String merchantId) {
        return entityMapper.findByPlatformAndMerchantId(platform.name(), merchantId);
    }

    public List<Long> findRefreshAccessTokenIds(ZonedDateTime when, int maxRetryTimes, int limit) {
        return entityMapper.findRefreshAccessTokenIds(when, maxRetryTimes, limit);
    }

}

