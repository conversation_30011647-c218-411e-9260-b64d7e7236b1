package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.BillAcquireService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Profile("local")
@Component
@RequiredArgsConstructor
public class JvmBillTaskAcquireEventConsumer implements ApplicationListener<JvmBillTaskEvent> {

    private final BillAcquireService billAcquireService;

    /**
     * {@inheritDoc}
     */
    @Override
    public void onApplicationEvent(final JvmBillTaskEvent event) {
        if (TaskState.WAIT_ACQUIRE.equals(event.getTaskState())
                || TaskState.ACQUIRE_FAILED.equals(event.getTaskState())) {
            billAcquireService.acquireIfNecessary(event.getTaskId());
        }
    }

}
