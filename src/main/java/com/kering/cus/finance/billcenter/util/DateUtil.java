package com.kering.cus.finance.billcenter.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @ClassName DateUtil
 * @Description Utility class for date and time operations focused on ZonedDateTime type
 * @Date 2025-07-08 15:03
 * @Version V1.0
 **/
public class DateUtil {

    private DateUtil() {
    }

    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    /**
     * Static import constant
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * Default date-time format: yyyy-MM-dd HH:mm:ss
     */
    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    /**
     * Date only format: yyyy-MM-dd
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * Date only format: yyyyMMdd
     */
    public static final DateTimeFormatter DATE_FORMATTER_FIRST = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * Time only format: HH:mm:ss
     */
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * ISO date-time format (with timezone): yyyy-MM-dd'T'HH:mm:ssXXX
     */
    public static final DateTimeFormatter ISO_DATETIME_WITH_ZONE_FORMATTER = DateTimeFormatter.ISO_ZONED_DATE_TIME;

    /**
     * Convert ZonedDateTime to string with specified format
     *
     * @param zonedDateTime Time object with timezone
     * @param formatter     Formatter to use
     * @return Formatted time string
     */
    public static String format(ZonedDateTime zonedDateTime, DateTimeFormatter formatter) {
        if (zonedDateTime == null || formatter == null) {
            return null;
        }
        return zonedDateTime.format(formatter);
    }

    /**
     * Convert ZonedDateTime to default format (yyyy-MM-dd HH:mm:ss) string
     *
     * @param zonedDateTime Time object with timezone
     * @return Formatted time string
     */
    public static String formatDefault(ZonedDateTime zonedDateTime) {
        return format(zonedDateTime, DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * Parse string to ZonedDateTime object
     *
     * @param timeStr   Time string
     * @param formatter Formatter to use
     * @param zoneId    Timezone ID
     * @return Parsed ZonedDateTime object
     */
    public static ZonedDateTime parse(String timeStr, DateTimeFormatter formatter, ZoneId zoneId) {
        if (timeStr == null || timeStr.isEmpty() || formatter == null || zoneId == null) {
            return null;
        }
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
            return ZonedDateTime.of(localDateTime, zoneId);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Time string parsing failed: " + timeStr, e);
        }
    }

    /**
     * Parse default format (yyyy-MM-dd HH:mm:ss) string to ZonedDateTime object
     *
     * @param timeStr Time string
     * @param zoneId  Timezone ID
     * @return Parsed ZonedDateTime object
     */
    public static ZonedDateTime parseDefault(String timeStr, ZoneId zoneId) {
        return parse(timeStr, DEFAULT_DATETIME_FORMATTER, zoneId);
    }

    /**
     * Convert ZonedDateTime to java.util.Date
     *
     * @param zonedDateTime Time object with timezone
     * @return Converted Date object
     */
    public static Date toDate(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * Convert java.util.Date to ZonedDateTime
     *
     * @param date   Date object
     * @param zoneId Timezone ID
     * @return Converted ZonedDateTime object
     */
    public static ZonedDateTime fromDate(Date date, ZoneId zoneId) {
        if (date == null || zoneId == null) {
            return null;
        }
        return ZonedDateTime.ofInstant(date.toInstant(), zoneId);
    }

    /**
     * Convert ZonedDateTime to ZonedDateTime in specified timezone
     *
     * @param zonedDateTime Original time object
     * @param targetZoneId  Target timezone ID
     * @return ZonedDateTime object in target timezone
     */
    public static ZonedDateTime convertTimeZone(ZonedDateTime zonedDateTime, ZoneId targetZoneId) {
        if (zonedDateTime == null || targetZoneId == null) {
            return null;
        }
        return zonedDateTime.withZoneSameInstant(targetZoneId);
    }

    /**
     * Get current time as ZonedDateTime object (default timezone)
     *
     * @return Current time as ZonedDateTime object
     */
    public static ZonedDateTime now() {
        return ZonedDateTime.now();
    }

    /**
     * Get current time in specified timezone
     *
     * @param zoneId Timezone ID
     * @return Current time in specified timezone
     */
    public static ZonedDateTime now(ZoneId zoneId) {
        if (zoneId == null) {
            return null;
        }
        return ZonedDateTime.now(zoneId);
    }

    /**
     * Check if one time is before another
     *
     * @param date1 First time
     * @param date2 Second time
     * @return True if date1 is before date2, false otherwise
     */
    public static boolean isBefore(ZonedDateTime date1, ZonedDateTime date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isBefore(date2);
    }

    /**
     * Check if one time is after another
     *
     * @param date1 First time
     * @param date2 Second time
     * @return True if date1 is after date2, false otherwise
     */
    public static boolean isAfter(ZonedDateTime date1, ZonedDateTime date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isAfter(date2);
    }

    /**
     * Check if two times are equal
     *
     * @param date1 First time
     * @param date2 Second time
     * @return True if times are equal, false otherwise
     */
    public static boolean isEqual(ZonedDateTime date1, ZonedDateTime date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isEqual(date2);
    }

    /**
     * Adjust time to start of day (00:00:00)
     *
     * @param zonedDateTime Original time object
     * @return Start of day time
     */
    public static ZonedDateTime atStartOfDay(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.toLocalDate().atStartOfDay(zonedDateTime.getZone());
    }

    /**
     * Adjust time to end of day (23:59:59.999)
     *
     * @param zonedDateTime Original time object
     * @return End of day time
     */
    public static ZonedDateTime atEndOfDay(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.toLocalDate().atTime(23, 59, 59, 999_000_000)
                .atZone(zonedDateTime.getZone());
    }

    /**
     * List of supported date-time formats
     */
    private static final List<DateTimeFormatter> DATE_TIME_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    );

    /**
     * Flexible parse date-time string
     *
     * @param dateTimeStr Date-time string
     * @return Parsed ZonedDateTime object
     * @throws DateTimeParseException If parsing fails
     */
    public static ZonedDateTime parseFlexibleDateTime(String dateTimeStr) throws DateTimeParseException {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        for (DateTimeFormatter formatter : DATE_TIME_FORMATTERS) {
            try {
                // Parse as LocalDateTime
                LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, formatter);

                // Apply system default timezone
                return localDateTime.atZone(ZoneId.systemDefault());
            } catch (DateTimeParseException e) {
                log.error("DateUtil parseFlexibleDateTime parsing failed", e);
            }
        }

        // Throw exception if all formats fail
        throw new DateTimeParseException(
                "Cannot parse date-time string, supported formats: " + DATE_TIME_FORMATTERS,
                dateTimeStr, 0);
    }

    /**
     * Format date-time to string (default format)
     *
     * @param dateTime ZonedDateTime object
     * @return Formatted string
     */
    public static String formatDateTime(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return DATE_TIME_FORMATTERS.get(0).format(dateTime);
    }
}
