package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.client.email.EmailCenterClient;
import com.kering.cus.finance.billcenter.client.email.EmailSendDTO;
import com.kering.cus.finance.billcenter.config.EmailCenterConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.kering.cus.finance.billcenter.constant.Constants.SYMBOL_COMMA;

@Slf4j
@Service
public class EmailService {
    @Resource
    private EmailCenterConfig emailCenterConfig;
    @Resource
    private EmailCenterClient emailCenterClient;

    public void send(final String subject, final String content) {
        String[] toEmails = emailCenterConfig.getEmailCenterToEmail().split(SYMBOL_COMMA);
        String[] ccEmails = emailCenterConfig.getEmailCenterCCEmail().split(SYMBOL_COMMA);
        send(toEmails, ccEmails, subject, content);
    }

    public void send(final String[] toEmails, final String[] ccEmails,
                     final String subject, final String content) {
        final EmailSendDTO emailSendDto = EmailSendDTO.builder()
                .toEmails(toEmails)
                .ccEmails(ccEmails)
                .subject(subject)
                .content(content)
                .build();

        final String messageId = emailCenterClient.send(emailSendDto);
        log.info("send email messageId: {}", messageId);
    }

}