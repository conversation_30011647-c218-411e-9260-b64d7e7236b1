package com.kering.cus.finance.billcenter.service.jd;

import com.kering.cus.finance.billcenter.config.JdBillConfig;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.service.support.AbstractSftpAcquireHandler;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import org.springframework.stereotype.Component;

@Component
public class JdTradeFlowAcquireHandler extends AbstractSftpAcquireHandler {
    private static final String PATH_SEP = SftpUtils.PATH_SEPARATOR;

    private final JdBillConfig jdBillConfig;

    public JdTradeFlowAcquireHandler(final SftpConfig sftpConfig, final JdBillConfig jdBillConfig) {
        super(sftpConfig, Channel.JD, BillType.TRADE_FLOW);
        this.jdBillConfig = jdBillConfig;
    }

    @Override
    protected String determineAcquiredSftpPath(final String sftpPath) {
        return sftpPath.replace(
                PATH_SEP + jdBillConfig.getTradePathAcquireDirName() + PATH_SEP,
                PATH_SEP + jdBillConfig.getTradePathAcquiredDirName() + PATH_SEP
        );
    }

}
