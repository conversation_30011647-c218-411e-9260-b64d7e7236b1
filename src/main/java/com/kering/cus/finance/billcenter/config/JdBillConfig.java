package com.kering.cus.finance.billcenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
public class JdBillConfig {

    @Value("${jd.trace.path.pattern:.*\\/trade\\/.*}")
    private String tradePathPattern;

    @Value("${jd.trade.path.merchant-pattern:\\/([^/_]+)_\\d+.zip$}")
    private String tradePathMerchantPattern;

    @Value("${jd.wallet.path.pattern:.*\\/wallet\\/.*}")
    private String walletPathPattern;

    @Value("${jd.wallet.path.merchant-pattern:\\/([^/-]+)-\\d+.csv$}")
    private String walletPathMerchantPattern;

}