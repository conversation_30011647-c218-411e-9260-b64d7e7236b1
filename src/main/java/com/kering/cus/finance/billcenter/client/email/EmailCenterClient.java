package com.kering.cus.finance.billcenter.client.email;

import com.google.common.base.Preconditions;
import com.kering.cus.finance.billcenter.config.EmailCenterConfig;
import com.kering.cus.lib.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;

/**
 * Email center client.
 *
 * <AUTHOR>
 * @see <a href="https://confluence.keringapps.com/spaces/PKCUS/pages/1036601716/Email%20Center%20API%20Document">Email Center</a>
 * @since 20250719
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailCenterClient {
    private static final MediaType APPLICATION_JSON = MediaType.parse("application/json");
    private static final MediaType APPLICATION_OCTET_STREAM = MediaType.parse("application/octet-stream");

    private static final String SEND_API_PATH = "api/sender/send";
    private static final String SEND_INFO = "send";
    private static final String FILE_INFO = "file";
    private static final String TENANT_ID = "X-CUS-Tenant-Id";
    private static final String SYSTEM_ID = "SYSTEM-ID";

    private static final String POST = "POST";

    private final EmailCenterConfig emailCenterConfig;

    public String send(final EmailSendDTO send, final File... attachments) {
        log.info("start send email: {}", send);
        final HttpUrl serverUrl = getRequiredApiUrl(SEND_API_PATH);
        final String tenantId = getRequiredTenantId();
        final String systemId = getRequiredSystemId();
        final long senderId = getRequiredSenderId();
        if (0 == send.getSenderId() && 0 != senderId) {
            send.setSenderId(senderId);
        }

        final String sendInfo = JsonUtil.convertToString(send);

        final MultipartBody.Builder multipart = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(SEND_INFO, null, RequestBody.create(sendInfo, APPLICATION_JSON));
        for (final File attachment : attachments) {
            multipart.addFormDataPart(FILE_INFO, attachment.getName(), RequestBody.create(attachment, APPLICATION_OCTET_STREAM));
        }

        final MultipartBody requestBody = multipart.build();
        final OkHttpClient client = client();
        final Request request = new Request.Builder()
                .url(serverUrl)
                .method(POST, requestBody)
                .addHeader(TENANT_ID, tenantId)
                .addHeader(SYSTEM_ID, systemId)
                .build();

        try (final Response response = client.newCall(request).execute()) {
            final boolean success = response.isSuccessful();
            final String message = response.message();
            String format = String.format("%s:%s", response.code(), message);
            Preconditions.checkState(success,format);
            final EmailSendResponse responseBody = JsonUtil.convertToObject(message, EmailSendResponse.class);
            Preconditions.checkState(200 != responseBody.getCode(), responseBody.getMessage());

            final EmailSendApiResultVO data = responseBody.getData();
            return null != data ? data.getMessageId() : null;
        } catch (final IOException e) {
            log.error("send email error: {}", e.getMessage(), e);
            throw new IllegalStateException(e);
        }
    }

    private HttpUrl getRequiredApiUrl(final String api) {
        final String endpoint = emailCenterConfig.getEmailCenterRestEndpoint();
        final HttpUrl endpointUrl = Preconditions.checkNotNull(HttpUrl.parse(endpoint), "email center endpoint is null");
        return Preconditions.checkNotNull(endpointUrl.resolve(api));
    }

    private String getRequiredSystemId() {
        final String systemId = emailCenterConfig.getEmailCenterSystemId();
        Preconditions.checkState(StringUtils.hasText(systemId), "email center system id not configure?");
        return systemId;
    }

    private String getRequiredTenantId() {
        final String tenantId = emailCenterConfig.getEmailCenterTenantId();
        Preconditions.checkState(StringUtils.hasText(tenantId), "email center tenant id not configure?");
        return tenantId;
    }

    private long getRequiredSenderId() {
        return emailCenterConfig.getEmailCenterSenderId();
    }

    private OkHttpClient client() {
        return new OkHttpClient().newBuilder().build();
    }

}