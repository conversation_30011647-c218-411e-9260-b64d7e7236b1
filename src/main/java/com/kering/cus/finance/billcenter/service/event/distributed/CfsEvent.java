package com.kering.cus.finance.billcenter.service.event.distributed;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * CFS event data.
 *
 * <AUTHOR>
 * @see <a href="https://confluence.keringapps.com/spaces/PKCUS/pages/974195886/How+to+use+CFS">How to use CFS?</a>
 * @since 20250711
 */
@Data
public class CfsEvent {

    private String timestamp;
    private String action;
    private String username;
    @JsonProperty("fs_path")
    private String fsPath;
    @JsonProperty("virtual_path")
    private String virtualPath;
    @JsonProperty("file_size")
    private long fileSize;
    private long elapsed;
    private int status;
    private String protocol;
    private String ip;
    @JsonProperty("session_id")
    private String sessionId;
    @JsonProperty("fs_provider")
    private int fsProvider;
    private String bucket;
    private String endpoint;

}
