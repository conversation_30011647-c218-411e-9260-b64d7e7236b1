package com.kering.cus.finance.billcenter.service.alipay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayDataDataserviceBillDownloadurlQueryModel;
import com.alipay.api.request.AlipayDataDataserviceBillDownloadurlQueryRequest;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.kering.cus.finance.billcenter.config.AlipayIsvAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.OauthAccessTokenDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import com.kering.cus.finance.billcenter.service.support.BillAcquireHandler;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.Objects;


@Slf4j
@Service
public class AlipayBillAcquireHandler implements BillAcquireHandler {

    @Resource
    private AppSecretService appSecretService;

    @Resource
    private OauthAccessTokenDAO oauthAccessTokenDAO;

    private static final String BILL_TYPE = "signcustomer";

    private static final String DATE_FORMAT_SHORT = "yyyy-MM-dd";
    private static final String API_SUCCESS_CODE = "10000";

    private static final String APP_AUTH_TOKEN = "app_auth_token";

    @Resource
    private  HttpClient httpClient;



    @Override
    public boolean matches(Channel channel, GrantType grantType, BillType billType, String apiVersion) {
        return Objects.equals(channel.getName(), Channel.ALIPAY.getName()) && Objects.equals(grantType, GrantType.ISV);
    }


    @Override
    public String acquireTo(BillTaskEntity task, File targetFile) {
        if (!Objects.equals(task.getChannel().getName(), Channel.ALIPAY.getName())) {
            return null;
        }
        log.info("start down alipay bill,merchantId : {}", task.getMerchantId());
        try {
            AlipayIsvAppProperties config = appSecretService.getIsvAppSecret(task.getChannel().getName(), AlipayIsvAppProperties.class);
            Assert.notNull(config, "alipay bill config is null");

            OauthAccessTokenEntity accessTokenEntity = oauthAccessTokenDAO.findByPlatformAndMerchantId(task.getChannel(), task.getMerchantId()).orElse(null);
            Assert.notNull(accessTokenEntity, "alipay bill oauthAccessToken is null");
            String alipayBillUrl = downloadBillUrl(config, task,accessTokenEntity.getAccessToken());

            return extractFileExtension(send(alipayBillUrl,targetFile));
        } catch (Exception e) {
            log.error("alipay bill acquireTo failed,merchantId:{},error:{}", task.getMerchantId(), e.getMessage(), e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_ACQUIRE_FAILED);
        }
    }


    @SneakyThrows
    private String send(String alipayBillUrl, File targetFile ) {
        URI uri = URI.create(alipayBillUrl);
        HttpRequest request = HttpRequest.newBuilder().uri(uri).GET().build();
        HttpResponse<Path> send = httpClient.send(request, HttpResponse.BodyHandlers.ofFile(targetFile.toPath()));
        log.info("Download completed with status: {}", send.statusCode());
        return uri.getPath();
    }

    private String downloadBillUrl(AlipayIsvAppProperties config, BillTaskEntity task,String accessToken) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig(config));
        AlipayDataDataserviceBillDownloadurlQueryRequest request = new AlipayDataDataserviceBillDownloadurlQueryRequest();
        request.setBizModel(getAlipayDataQueryModel(task));
        request.putOtherTextParam(APP_AUTH_TOKEN, accessToken);

        AlipayDataDataserviceBillDownloadurlQueryResponse response = alipayClient.execute(request);
        log.info("alipay bill downloadurl execute success,merchantId : {},response : {}", task.getMerchantId(), response.toString());
        Assert.isTrue(API_SUCCESS_CODE.equals(response.getCode()),"alipay bill downloadurl  failed");
        return response.getBillDownloadUrl();

    }

    private AlipayDataDataserviceBillDownloadurlQueryModel getAlipayDataQueryModel(BillTaskEntity task) {
        AlipayDataDataserviceBillDownloadurlQueryModel model = new AlipayDataDataserviceBillDownloadurlQueryModel();
        model.setBillType(BILL_TYPE);
        model.setBillDate(task.getBillStartTime().format(DateTimeFormatter.ofPattern(DATE_FORMAT_SHORT)));
        return model;
    }


    private AlipayConfig getAlipayConfig(AlipayIsvAppProperties config) {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(config.getAppId());
        alipayConfig.setAlipayPublicKey(config.getAlipayPublicKey());
        alipayConfig.setPrivateKey(config.getAppPrivateKey());
        return alipayConfig;
    }


    private String extractFileExtension(String path){
        int lastDotIndex = path.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return path.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
}


