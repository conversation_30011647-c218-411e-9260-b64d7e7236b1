package com.kering.cus.finance.billcenter.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;


@Slf4j
public class CsvWriterUtils implements AutoCloseable {

    private static final char DEFAULT_DELIMITER = ',';
    private static final char DEFAULT_QUOTE = '"';
    private static final String LINE_SEPARATOR = "\r\n";

    private final BufferedWriter writer;
    private final char delimiter;
    private final char quote;
    private static final byte[] UTF8_BOM = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};

    public CsvWriterUtils(File file) throws IOException {
        this(file, StandardCharsets.UTF_8, DEFAULT_DELIMITER, DEFAULT_QUOTE);
    }

    public CsvWriterUtils(File file, java.nio.charset.Charset charset, char delimiter, char quote) throws IOException {
        this.writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), charset));
        this.delimiter = delimiter;
        this.quote = quote;
    }


    public void writeHeader(String[] headers) throws IOException {
        writer.write(new String(UTF8_BOM, StandardCharsets.UTF_8));
        writeLine(headers);
    }

    public void writeBatch(List<String[]> lines) throws IOException {
        StringBuilder sb = new StringBuilder();
        for (String[] fields : lines) {
            for (int i = 0; i < fields.length; i++) {
                if (i > 0) {
                    sb.append(delimiter);
                }
                sb.append(quote).append(fields[i]).append(quote);
            }
            sb.append(LINE_SEPARATOR);
        }
        writer.write(sb.toString());
        writer.flush();
    }

    public void writeLine(String[] fields) throws IOException {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.length; i++) {
            if (i > 0) {
                sb.append(delimiter);
            }
            sb.append(quote).append(fields[i]).append(quote);
        }
        sb.append(LINE_SEPARATOR);
        writer.write(sb.toString());
    }



    public void close() throws IOException {
        writer.close();
    }

}
