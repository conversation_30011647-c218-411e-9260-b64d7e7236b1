package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.scheduler.dynamic.SchedulerTarget;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;

@Slf4j
@Component
@RequiredArgsConstructor
public class ScheduleService {
    private final BillCenterConfig billCenterConfig;
    private final OauthGrantService oauthGrantService;
    private final ApiBillTaskCreateProxyService apiBillTaskCreateProxyService;
    private final SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService;
    private final BillAcquireService billAcquireService;
    private final BillProcessService billProcessService;
    private final SapBillArchiveService sapBillArchiveService;
    private final BlacklineBillArchiveService blacklineBillArchiveService;

    private final BillAlertService billAlertService;

    @Probe(event = "refreshOauth2AccessToken", group = "oauth2")
    @SchedulerTarget(name = "REFRESH_OAUTH2_ACCESS_TOKEN")
    public void refreshOauth2AccessToken() {
        log.info("Refresh OAUTH2 access token starting");
        oauthGrantService.refreshAccessTokenIfNecessary();
        log.info("Refresh OAUTH2 access token completed");
    }

    @Probe(event = "initializeApiTask", group = "billTask")
    @SchedulerTarget(name = "CRON_INIT_API_BILL_TASK")
    public void initializeApiTaskIfNecessary(final String channel) {
        log.info("API bill task initialize, channel: {}", channel);
        final boolean created = apiBillTaskCreateProxyService.createTaskIfNecessary(channel);
        log.info("API bill task initialize result: {}", created);
    }

    @Probe(event = "initializeSftpTask", group = "billTask")
    @SchedulerTarget(name = "CRON_INIT_SFTP_BILL_TASK")
    public void initializeSftpTaskIfNecessary(final String sftpPath) {
        log.info("SFTP bill task initialize, path: {}", sftpPath);
        final boolean created = sftpBillTaskCreateProxyService.createTaskIfNecessary(sftpPath, ZonedDateTime.now(), "SCHEDULER");
        log.info("SFTP bill task initialize result: {}", created);
    }

    @Probe(event = "billAcquire", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_ACQUIRE_TASK")
    public void acquireBillIfNecessary(final Long taskId) {
        log.info("execute bill acquire, task id: {}", taskId);
        final boolean executed = billAcquireService.acquireIfNecessary(taskId);
        log.info("execute bill acquire result: {}", executed);
    }

    @Probe(event = "billAcquireRetry", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_ACQUIRE_RETRY_TASK")
    public void acquireBillRetryIfNecessary() {
        log.info("execute bill acquire retry");
        billAcquireService.acquireRetryIfNecessary();
        log.info("execute bill acquire retry completed");
    }

    @Probe(event = "billProcess", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_PROCESS_TASK")
    public void processBillIfNecessary(final Long taskId) {
        log.info("execute bill process, task id: {}", taskId);
        final boolean processed = billProcessService.processIfNecessary(taskId);
        log.info("execute bill process result: {}", processed);
    }

    @Probe(event = "billProcessRetry", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_PROCESS_RETRY_TASK")
    public void processBillRetryIfNecessary() {
        log.info("execute bill process retry");
        billProcessService.processRetryIfNecessary();
        log.info("execute bill process retry completed");
    }

    @Probe(event = "billPushToSap", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_PUSH_TO_SAP_TASK")
    public void billPushToSapIfNecessary(final String channel) {
        log.info("execute push to SAP, channel: {}", channel);
        sapBillArchiveService.archiveAllIfNecessary(Channel.valueOf(channel));
        log.info("execute push to SAP completed, channel: {}", channel);
    }

    @Probe(event = "billPushToBlackline", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_PUSH_TO_BLACKLINE_TASK")
    public void billPushToBlacklineIfNecessary(final String channel) {
        log.info("execute push to blackline, channel: {}", channel);
        blacklineBillArchiveService.archiveIfNecessary(Channel.valueOf(channel));
        log.info("execute push to blackline completed, channel: {}", channel);
    }



    @Probe(event = "billAcquireAlert", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_ACQUIRE_ALERT_TASK")
    public void billAcquireAlert() {
        log.info("execute bill acquire alert");
        billAlertService.billAcquireAlert();
        log.info("execute bill acquire alert completed");
    }


    @Probe(event = "billProcessAlert", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_PROCESS_ALERT_TASK")
    public void billProcessAlert() {
        log.info("execute bill process alert");
        billAlertService.billProcessAlert();
        log.info("execute bill process alert completed");
    }



    @Probe(event = "billSapPushFailAlert", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_SAP_PUSH_FAILED_ALERT_TASK")
    public void billSapPushFailAlert() {
        log.info("execute bill sap push failed alert");
        billAlertService.billSapPushFailAlert();
        log.info("execute bill sap push failed alert completed");

    }



    @Probe(event = "billBlacklinePushFailAlert", group = "billTask")
    @SchedulerTarget(name = "CRON_BILL_BLACKLINE_PUSH_FAILED_ALERT_TASK")
    public void billBlacklinePushFailAlert() {
        log.info("execute bill blackline push failed alert");
        billAlertService.billBlacklinePushFailAlert();
        log.info("execute bill blackline push failed alert completed");
    }
}
