package com.kering.cus.finance.billcenter.converter;

import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.vo.WosaipayTradeFlowVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Mapper(componentModel = "spring", imports = {StringUtils.class, Date.class, Optional.class, BigDecimal.class, Objects.class})
public abstract class WosaipayBillConverter {

    @Mapping(target = "taskId", source = "task.id")
    @Mapping(target = "brand", source = "task.brand")
    @Mapping(target = "channel", source = "task.channel")
    @Mapping(target = "merchantId", source = "billFlow.merchantId")
    @Mapping(target = "isSyncToBlackline", constant = "true")
    @Mapping(target = "isSyncToSap", constant = "false")
    @Mapping(target = "transactionTime", source = "billFlow.transactionTime")
    @Mapping(target = "merchantName", source = "billFlow.merchantName")
    @Mapping(target = "storeName", source = "billFlow.storeName")
    @Mapping(target = "storeCode", source = "billFlow.storeCode")
    @Mapping(target = "merchantStoreCode", source = "billFlow.merchantStoreCode")
    @Mapping(target = "transactionDate", source = "billFlow.transactionDate")
    @Mapping(target = "merchantOrderId", source = "billFlow.merchantOrderId")
    @Mapping(target = "merchantTransactionNo", source = "billFlow.merchantTransactionNo")
    @Mapping(target = "posOrderId", source = "billFlow.posOrderId")
    @Mapping(target = "posTransactionNo", source = "billFlow.posTransactionNo")
    @Mapping(target = "posOriginOrderId", source = "billFlow.posOriginOrderId")
    @Mapping(target = "posOriginTransactionNo", source = "billFlow.posOriginTransactionNo")
    @Mapping(target = "wosaypayOrderId", source = "billFlow.wosaypayOrderId")
    @Mapping(target = "paymentChannel", source = "billFlow.paymentChannel")
    @Mapping(target = "paymentChannelOrderId", source = "billFlow.paymentChannelOrderId")
    @Mapping(target = "paymentChannelMerchantId", source = "billFlow.paymentChannelMerchantId")
    @Mapping(target = "skuName", source = "billFlow.skuName")
    @Mapping(target = "transactionType", source = "billFlow.transactionType")
    @Mapping(target = "tradeMode", source = "billFlow.tradeMode")
    @Mapping(target = "orderSource", source = "billFlow.orderSource")
    @Mapping(target = "orderSence", source = "billFlow.orderSence")
    @Mapping(target = "tradeStatus", source = "billFlow.tradeStatus")
    @Mapping(target = "paymentAccount", source = "billFlow.paymentAccount")
    @Mapping(target = "currency", source = "billFlow.currency")
    @Mapping(target = "transactionAmount", source = "billFlow.transactionAmount")
    @Mapping(target = "merchantDiscountAmount", source = "billFlow.merchantDiscountAmount")
    @Mapping(target = "merchantDiscountType", source = "billFlow.merchantDiscountType")
    @Mapping(target = "subsidyAmount", source = "billFlow.subsidyAmount")
    @Mapping(target = "paymentChannelDiscountAmount", source = "billFlow.paymentChannelDiscountAmount")
    @Mapping(target = "rechargeDiscountAmount", source = "billFlow.rechargeDiscountAmount")
    @Mapping(target = "nonRechargeDiscountAmount", source = "billFlow.nonRechargeDiscountAmount")
    @Mapping(target = "actualPaidAmount", source = "billFlow.actualPaidAmount")
    @Mapping(target = "transactionRate", source = "billFlow.transactionRate")
    @Mapping(target = "serviceCharge", expression = "java(Optional.ofNullable(billFlow).map(WosaipayTradeFlowVO::getServiceCharge).map(BigDecimal::negate).orElse(null))")
    @Mapping(target = "actualRecvAmount", source = "billFlow.actualRecvAmount")
    @Mapping(target = "installmentNum", source = "billFlow.installmentNum")
    @Mapping(target = "installmentFeeRate", source = "billFlow.installmentFeeRate")
    @Mapping(target = "installmentFee", expression = "java(Optional.ofNullable(billFlow).map(WosaipayTradeFlowVO::getInstallmentFee).map(BigDecimal::negate).orElse(null))")
    @Mapping(target = "splitAmount", expression = "java(Optional.ofNullable(billFlow).map(WosaipayTradeFlowVO::getSplitAmount).map(BigDecimal::negate).orElse(null))")
    @Mapping(target = "settlementAmount", source = "billFlow.settlementAmount")
    @Mapping(target = "termNo", source = "billFlow.termNo")
    @Mapping(target = "merchantTermNo", source = "billFlow.merchantTermNo")
    @Mapping(target = "termName", source = "billFlow.termName")
    @Mapping(target = "termType", source = "billFlow.termType")
    @Mapping(target = "deviceNo", source = "billFlow.deviceNo")
    @Mapping(target = "operator", source = "billFlow.operator")
    @Mapping(target = "cashier", source = "billFlow.cashier")
    @Mapping(target = "note", source = "billFlow.note")
    @Mapping(target = "ext1", source = "billFlow.ext1")
    @Mapping(target = "ext2", source = "billFlow.ext2")
    @Mapping(target = "bizOrderId", source = "billFlow.bizOrderId")
    @Mapping(target = "bizNote", source = "billFlow.bizNote")
    @Mapping(target = "deposit", source = "billFlow.deposit")
    @Mapping(target = "outerOriginTransactionNo", source = "billFlow.outerOriginTransactionNo")
    @Mapping(target = "reserve3", source = "billFlow.reserve3")
    public abstract WosaipayTradeFlowEntity toWosaipayBillEntity(BillTaskEntity task, WosaipayTradeFlowVO billFlow);

}
