package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.service.BillProcessService;
import com.kering.cus.lib.common.util.JsonUtil;
import com.kering.cus.lib.message.queue.consumer.MessageConsumer;
import com.kering.cus.lib.message.queue.consumer.annotation.MessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * Bill process task kafka consumer.
 * <p>
 * The @DependsOn here is to skip the Spring(&le 6.2.1) BUG,
 * BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean.
 *
 * <AUTHOR>
 * @see <a href="https://github.com/spring-projects/spring-kafka/pull/3716">spring-kafka: spring-framework-bom from 6.2.1 to 6.2.2</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/34186">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean #34186</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/33972">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create a FactoryBean #33972</a>
 * @see org.springframework.kafka.listener.KafkaMessageListenerContainer#doStart
 * @since 20250711
 */
@Slf4j
@Profile("!local")
@Component
@DependsOn({"refreshEventListener"})
@MessageListener(topic = "${KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_PROCESS_TASK}", groupId = "${KAFKA_GROUPID_CUS_CG_CUS_BILL_CENTER_RSVP_EVENT_GROUP}")
@RequiredArgsConstructor
public class DistributedBillTaskProcessEventConsumer implements MessageConsumer<DistributedBillTaskEvent> {

    private final BillProcessService billProcessService;

    /**
     * {@inheritDoc}
     */
    @Override
    public void consume(final DistributedBillTaskEvent event) {
        final String stringify = JsonUtil.convertToString(event);
        log.info("receive bill process event: {}", stringify);
        if (DistributedBillTaskEvent.PROCESS.equals(event.getTaskType())) {
            billProcessService.processIfNecessary(event.getTaskId());
        }
    }

}
