package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * JD trade flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_jd_trade_flow")
public class JdTradeFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private String channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("order_id")
    private String orderId;

    @TableField("transaction_no")
    private String transactionNo;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("sku_code")
    private String skuCode;

    @TableField("merchant_order_id")
    private String merchantOrderId;

    @TableField("sku_name")
    private String skuName;

    @TableField("settlement_status")
    private String settlementStatus;

    @TableField("occur_time")
    private String occurTime;

    @TableField("chargeable_time")
    private String chargeableTime;

    @TableField("settlement_time")
    private String settlementTime;

    @TableField("fee_item")
    private String feeItem;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("currency")
    private String currency;

    @TableField("merchant_payment_type")
    private String merchantPaymentType;

    @TableField("settlement_note")
    private String settlementNote;

    @TableField("shop_code")
    private String shopCode;

    @TableField("jd_store_code")
    private String jdStoreCode;

    @TableField("brand_store_code")
    private String brandStoreCode;

    @TableField("store_name")
    private String storeName;

    @TableField("note")
    private String note;

    @TableField("payment_type")
    private String paymentType;

    @TableField("sku_qty")
    private Integer skuQty;

    @TableField("billing_date")
    private String billingDate;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
