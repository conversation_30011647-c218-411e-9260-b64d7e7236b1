package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Wechat funds flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_wechat_fund_flow")
public class WechatFundFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private Channel channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("billing_date")
    private String billingDate;

    @TableField("biz_order_id")
    private String bizOrderId;

    @TableField("transaction_no")
    private String transactionNo;

    @TableField("transaction_name")
    private String transactionName;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("payment_type")
    private String paymentType;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("balance")
    private BigDecimal balance;

    @TableField("apply_user")
    private String applyUser;

    @TableField("note")
    private String note;

    @TableField("biz_voucher_no")
    private String bizVoucherNo;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
