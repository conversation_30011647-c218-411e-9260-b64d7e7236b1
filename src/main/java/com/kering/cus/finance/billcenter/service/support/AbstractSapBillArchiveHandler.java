package com.kering.cus.finance.billcenter.service.support;

import com.google.common.base.Preconditions;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import freework.net.Http;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@RequiredArgsConstructor
public abstract class AbstractSapBillArchiveHandler implements SapBillArchiveHandler {

    protected final Channel channel;
    protected final BillType billType;
    protected final SftpConfig sftpConfig;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final Channel channel, final BillType billType) {
        return Objects.equals(channel, this.channel) && Objects.equals(billType, this.billType);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void sendToSap(final String filename, final File targetFile, final MerchantConfigEntity config) throws IOException {
        Preconditions.checkArgument(null != targetFile, "targetFile cannot be null");

        final String hostname = getSapSftpHostname();
        final int port = getSapSftpPort();
        final String username = getSapSftpUsername();
        final String password = getSapSftpPassword();
        final String privateKeyStr = getSapSftpPrivateKey();
        final String passphraseStr = getSapSftpPrivateKeyPassphrase();
        final String targetDirectory = getSapSftpTargetDirectory();
        final String targetPath = Http.resolveUrl(targetDirectory, filename);
        final int timeout = sftpConfig.getSftpConnectTimeout();
        final byte[] privateKey = !StringUtils.hasText(privateKeyStr) ? null : privateKeyStr.getBytes(StandardCharsets.UTF_8);
        final byte[] passphrase = !StringUtils.hasText(passphraseStr) ? null : passphraseStr.getBytes(StandardCharsets.UTF_8);

        final ChannelSftp sftp = SftpUtils.connect(hostname, port, username, password, privateKey, passphrase, timeout);
        try {
            if (!SftpUtils.mkdirs(sftp, targetDirectory)) {
                throw new IOException(String.format("Failed to mkdirs %s", targetDirectory));
            }
            try (final InputStream in = new FileInputStream(targetFile)) {
                sftp.put(in, targetPath);
            }
        } catch (final SftpException e) {
            throw new IOException(String.format("Failed to upload file to SAP SFTP server: %s", e.getMessage()), e);
        } finally {
            SftpUtils.disconnect(sftp);
        }
    }

    protected String getSapSftpHostname() {
        return sftpConfig.getSapCrushSftpHostname();
    }

    protected int getSapSftpPort() {
        return sftpConfig.getSapCrushSftpPort();
    }

    protected abstract String getSapSftpUsername();

    protected abstract String getSapSftpPassword();

    protected String getSapSftpPrivateKey() {
        return null;
    }

    protected String getSapSftpPrivateKeyPassphrase() {
        return null;
    }

    protected abstract String getSapSftpTargetDirectory();

}
