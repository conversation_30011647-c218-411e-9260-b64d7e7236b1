package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.BillAcquireService;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("api")
public class BillTaskController {
    @Autowired
    private BillAcquireService billAcquireService;

    @PostMapping("tasks/{task-id}/acquire")
    @Operation(summary = "execute acquire task by task id")
    @Probe(event = "acquireTaskById", group = "controller")
    public ResponseEntity<Void> acquireTaskById(@PathVariable("task-id") @ProbeDimension(name = "task-id") @Valid Long taskId) {
        if (billAcquireService.acquireIfNecessary(taskId)) {
            return ResponseEntity.status(HttpStatus.CREATED).build();
        }
        return ResponseEntity.status(HttpStatus.NOT_MODIFIED).build();
    }

}
