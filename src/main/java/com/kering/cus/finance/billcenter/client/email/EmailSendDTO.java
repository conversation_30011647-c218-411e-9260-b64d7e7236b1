package com.kering.cus.finance.billcenter.client.email;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmailSendDTO {
    private static final String ENCODING_BASE64 = "base64";

    private long senderId;
    private String[] toEmails;
    private String[] ccEmails;
    private String subject;
    private String content;
    private String contentEncoding;

    public String[] getCcEmails() {
        return ccEmails == null ? null : ccEmails.clone();
    }

    public String[] getToEmails() {
        return toEmails == null ? null : toEmails.clone();
    }

}