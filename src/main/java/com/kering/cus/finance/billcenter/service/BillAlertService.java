package com.kering.cus.finance.billcenter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.HtmlUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;

@Slf4j
@RequiredArgsConstructor
@Lazy
@Service
public class BillAlertService {


    private final BillTaskDAO billTaskDAO;
    private final EmailService emailService;


    private static final String ROW_TEMPLATE_START = "<tr>" +
            "<td>";
    private static final String ROW_TEMPLATE_MIDDLE = "</td>" +
            "<td>";
    private static final String ROW_TEMPLATE_END = "</td></tr>";

    private static final String ROW_TABLE_END = "</table></body></html>";


    private static final String BILL_ACQUIRE_FAILED = "账单获取失败";
    private static final String BILL_PROCESS_FAILED = "账单解析失败";
    private static final String BILL_PUSH_FAILED = "账单推送SAP失败";


    public void billAcquireAlert() {
        billAlert(BILL_ACQUIRE_FAILED, TaskState.ACQUIRE_FAILED, null);
    }

    public void billProcessAlert() {
        billAlert(BILL_PROCESS_FAILED, TaskState.PROCESS_FAILED, null);

    }

    public void billAlert(String type, TaskState taskState, SyncState syncState) {
        log.info("billAlert start,type:{},taskState:{},syncState :{}", type, taskState, syncState);
        LocalDate date = LocalDate.now();
        int pageNum = 1;
        Page<BillTaskEntity> billtaskList;
        StringBuilder builder = generateBillAlertHeader(date, type);
        try {
            do {
                billtaskList = billTaskDAO.findTasksByStatus(date, taskState, syncState, BATCH_SIZE, pageNum);
                if (!CollectionUtils.isEmpty(billtaskList.getRecords())) {
                    setEmailContent(billtaskList.getRecords(), builder, type);
                }
                pageNum++;
            } while (billtaskList.hasNext());

            log.info("billAlert end,type:{},total:{}", type,  billtaskList.getTotal());
            if (billtaskList.getTotal() > 0) {
                builder.append(ROW_TABLE_END);
                String subject = String.format("【异常预警】对账中心-%s-%s", date, type);
                emailService.send(subject, builder.toString());
            }
        } catch (Exception e) {
            log.error("billAlert error,type:{},taskState:{},syncState :{}", type, taskState, syncState, e);
        }
    }


    public void billSapPushFailAlert() {
        billAlert(BILL_PUSH_FAILED, null, SyncState.FAILED);
    }


    public void billBlacklinePushFailAlert() {
        LocalDate date = LocalDate.now();
        try {
            List<BillTaskEntity> billtaskList = billTaskDAO.findTasksByBlacklineStatus(date, SyncState.FAILED);
            log.info("billBlacklinePushFailAlert start,total:{}", billtaskList.size());
            if (!CollectionUtils.isEmpty(billtaskList)) {
                StringBuilder builder = generatePushBlacklineAlertHeader(date);
                setPushBlacklineEmailContent(billtaskList, builder);

                String subject = String.format("【异常预警】对账中心-%s-账单推送blackline失败", date);
                emailService.send(subject, builder.toString());
            }
        } catch (Exception e) {
            log.error("billBlacklinePushFailAlert error", e);
        }

    }

    private void setEmailContent(List<BillTaskEntity> taskWarnList, StringBuilder builder, String type) {
        for (BillTaskEntity task : taskWarnList) {
            String brand = StringUtils.hasText(task.getBrand())?  HtmlUtils.htmlEscape(task.getBrand()): "";
            String channel = task.getChannel() != null ? HtmlUtils.htmlEscape(task.getChannel().name()) : "";
            String merchantId = HtmlUtils.htmlEscape(task.getMerchantId());
            String billType = HtmlUtils.htmlEscape(getBillTypeName(task.getBillType()));
            String errorCount = String.valueOf(task.getErrorCount());
            String errorMsg =StringUtils.hasText(task.getErrorMsg()) ? HtmlUtils.htmlEscape(task.getErrorMsg()): "";

            builder.append(ROW_TEMPLATE_START)
                    .append(brand)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(channel)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(merchantId);

            if (!type.equals(BILL_PUSH_FAILED)) {
                builder.append(ROW_TEMPLATE_MIDDLE).append(billType);
            }

            builder.append(ROW_TEMPLATE_MIDDLE)
                    .append(errorCount)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(errorMsg)
                    .append(ROW_TEMPLATE_END);
        }

    }

    public StringBuilder generateBillAlertHeader(LocalDate date, String type) {
        StringBuilder builder = new StringBuilder();
        builder.append("<html><body>")
                .append("<div class=\"ctnBox\">")
                .append("    <div>DEAR ALL：</div>")
                .append("    <div>&nbsp;&nbsp;在").append(date.toString()).append(type).append("，涉及的品牌和商户如下：</div>")
                .append("</div>")
                .append("<br>")
                .append("<table border=\"1\" cellspacing=\"0\" style=\" border-collapse: collapse; text-align: center; width: 80%;table-layout: fixed; color: #666; font-weight: normal;\" >")
                .append("<tr>")
                .append("<th>品牌</th>")
                .append("<th>渠道</th>")
                .append("<th>商户号</th>");

        if (!type.equals(BILL_PUSH_FAILED)) {
            builder.append("<th>账单类型</th>");
        }

        builder.append("<th>重试次数</th>")
                .append("<th>失败原因</th>")
                .append("</tr>");
        return builder;
    }

    public StringBuilder generatePushBlacklineAlertHeader(LocalDate date) {
        StringBuilder builder = new StringBuilder();
        builder.append("<html><body>")
                .append("<div class=\"ctnBox\">")
                .append("    <div>DEAR ALL：</div>")
                .append("    <div>&nbsp;&nbsp;在").append(date.toString()).append("获取推送失败，涉及的品牌如下：</div>")
                .append("</div>")
                .append("<br>")
                .append("<table border=\"1\" cellspacing=\"0\" style=\" border-collapse: collapse;text-align: center; width: 80%;table-layout: fixed; color: #666; font-weight: normal;\" >")
                .append("<tr>")
                .append("<th>渠道</th>")
                .append("<th>类型</th>")
                .append("<th>重试次数</th>")
                .append("<th>失败原因</th>")
                .append("</tr>");
        return builder;
    }

    private void setPushBlacklineEmailContent(List<BillTaskEntity> taskWarnList, StringBuilder builder) {
        for (BillTaskEntity task : taskWarnList) {
            String channel = task.getChannel() != null ? HtmlUtils.htmlEscape(task.getChannel().name()) : "";
            String type = task.getChannel() != null ? HtmlUtils.htmlEscape(task.getChannel().name()) : "";
            if (Objects.equals(task.getChannel(), Channel.ALIPAY)) {
                type = Boolean.TRUE.equals(task.getIsTmall()) ? "TMALL" : "非TMALL";
            }
            String errorCount = String.valueOf(task.getErrorCount());
            String errorMsg = HtmlUtils.htmlEscape(task.getErrorMsg());

            builder.append(ROW_TEMPLATE_START)
                    .append(channel)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(type)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(errorCount)
                    .append(ROW_TEMPLATE_MIDDLE)
                    .append(errorMsg)
                    .append(ROW_TEMPLATE_END);
        }
        builder.append(ROW_TABLE_END);
    }


    private String getBillTypeName(BillType billType){
        if(Objects.isNull(billType)){
            return "";
        }
        return  Objects.equals(billType,BillType.TRADE_FLOW)? "交易账单":"资金账单";
    }


}