package com.kering.cus.finance.billcenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
public class EmailCenterConfig {
    @Value("${EMAIL_CENTER_REST_ENDPOINT}")
    private String emailCenterRestEndpoint;
    @Value("${SYSTEM_ID}")
    private String emailCenterSystemId;
    @Value("${SENDER_ID}")
    private long emailCenterSenderId;
    @Value("${TENANT_ID}")
    private String emailCenterTenantId;

    @Value("${to.Emails:<EMAIL>,<EMAIL>}")
    private String emailCenterToEmail;

    @Value("${cc.Emails:<EMAIL>}")
    private String emailCenterCCEmail;

}