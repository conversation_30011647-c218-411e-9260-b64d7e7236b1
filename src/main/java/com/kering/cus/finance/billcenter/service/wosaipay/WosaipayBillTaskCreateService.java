package com.kering.cus.finance.billcenter.service.wosaipay;

import com.kering.cus.finance.billcenter.config.WosaipayBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.service.support.AbstractSftpBillTaskCreateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Wosaipay bill task create service.
 *
 * <AUTHOR>
 * @since 20250708
 */
@Slf4j
@Service
public class WosaipayBillTaskCreateService extends AbstractSftpBillTaskCreateService {
    /**
     * Only trade flow.
     */
    private static final BillType WOSAIPAY_BILL_TYPE = BillType.TRADE_FLOW;

    private final WosaipayBillConfig wosaipayBillConfig;

    public WosaipayBillTaskCreateService(final WosaipayBillConfig wosaipayBillConfig,
                                         final BillTaskDAO billTaskDAO,
                                         final TransitLogDAO transitLogDAO,
                                         final MerchantConfigDAO merchantConfigDAO) {
        super(Channel.WOSAIPAY, billTaskDAO, transitLogDAO, merchantConfigDAO);
        this.wosaipayBillConfig = wosaipayBillConfig;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final String sftpPath) {
        return StringUtils.hasText(sftpPath) && sftpPath.matches(wosaipayBillConfig.getTradePathPattern());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long createTaskIfNecessary(final String sftpPath, final ZonedDateTime when, final String source) {
        if (!matches(sftpPath)) {
            log.warn("[SKIP] illegal Wosaipay sftp path: {}", sftpPath);
            return null;
        }

        final String brand = determineBrand(sftpPath);
        if (!StringUtils.hasText(brand)) {
            log.warn("[SKIP] illegal Wosaipay sftp path brand not found: {}", sftpPath);
            return null;
        }
        return doCreateTaskIfNecessary(brand, null, WOSAIPAY_BILL_TYPE, sftpPath, when, source);
    }

    private String determineBrand(final String sftpPath) {
        final String pattern = wosaipayBillConfig.getTradePathBrandPattern();
        final Matcher matcher = Pattern.compile(pattern).matcher(sftpPath);
        return matcher.find() ? matcher.group(1) : null;
    }

    @Override
    protected boolean determineSyncToSap(BillType billType, boolean udfSyncToSap) {
        return false;
    }

    @Override
    protected boolean determineSyncToBlackline(BillType billType, boolean udfSyncToBlackline) {
        return BillType.TRADE_FLOW.equals(billType) && udfSyncToBlackline;
    }
}