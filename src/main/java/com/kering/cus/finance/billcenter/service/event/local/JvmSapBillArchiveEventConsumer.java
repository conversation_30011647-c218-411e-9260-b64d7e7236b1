package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.service.SapBillArchiveService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Profile("local")
@Component
@RequiredArgsConstructor
public class JvmSapBillArchiveEventConsumer implements ApplicationListener<JvmBillTaskEvent> {

    private final SapBillArchiveService sapBillArchiveService;

    /**
     * {@inheritDoc}
     */
    @Override
    public void onApplicationEvent(final JvmBillTaskEvent event) {
        if (JvmBillTaskEvent.PUSH_TO_SAP.equals(event.getTaskType())) {
            sapBillArchiveService.archiveIfNecessary(event.getTaskId());
        }
    }

}
