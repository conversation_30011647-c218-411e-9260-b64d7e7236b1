package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Grant API task create service.
 *
 * <AUTHOR>
 * @since 20250701
 */
public interface ApiBillTaskCreateService {

    /**
     * matches given channel.
     *
     * @param channel the channel
     * @return matches
     */
    boolean matches(final Channel channel);

    /**
     * If it is necessary to create billing tasks for all
     * authorized merchants under the current channel.
     *
     * @param when   the trigger time
     * @param source the task source
     * @return the created task id list
     */
    List<Long> createTaskIfNecessary(final ZonedDateTime when, final String source);

    /**
     * If a billing task needs to be created based on the given merchant and billing type.
     *
     * @param merchantId the merchant id
     * @param billTypes  the bill types
     * @param when       the trigger time
     * @param source     the task source
     * @return the created task id list
     */
    List<Long> createTaskIfNecessary(final String merchantId,
                                     final List<BillType> billTypes,
                                     final ZonedDateTime when, final String source);

}