package com.kering.cus.finance.billcenter.service.event.distributed;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DistributedBillTaskEvent {
    private Long taskId;
    private String taskState;

    public DistributedBillTaskEvent(final Long taskId, final String taskState) {
        this.taskId = taskId;
        this.taskState = taskState;
    }

}
