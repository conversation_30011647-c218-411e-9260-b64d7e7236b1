package com.kering.cus.finance.billcenter.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName TaskOperationLogVO
 * @Description Value Object for task operation log response
 * @Date 2025-07-24 15:30:00
 * @Version V1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TaskOperationLogVO {
    private Long id;
    private String title;
    private String message;
    private String createdDate;
    private String traceId;
}