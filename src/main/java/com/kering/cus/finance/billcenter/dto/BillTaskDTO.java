package com.kering.cus.finance.billcenter.dto;


import com.kering.cus.finance.billcenter.constant.SyncState;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BillTaskDTO {


  private String archiveUrl;


  private String archiveExcelUrl;


  private Integer errorCount;

  private String errorMsg;

  private ZonedDateTime nextRunTime;

  private SyncState newSyncState;


  private long taskId;

  private List<Long> taskIds;


  private List<SyncState> syncStatus;




  public List<Long> getTaskIds() {
    if (taskIds == null) {
      return Collections.emptyList();
    }
    return Collections.unmodifiableList(taskIds);
  }


  public List<SyncState> getSyncStatus() {
    if (syncStatus == null) {
      return Collections.emptyList();
    }
    return Collections.unmodifiableList(syncStatus);
  }

}
