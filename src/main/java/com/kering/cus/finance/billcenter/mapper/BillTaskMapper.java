package com.kering.cus.finance.billcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dto.BillTaskDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.lib.persistence.mybatis.mapper.BatchOperationMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

@Mapper
public interface BillTaskMapper extends BaseMapper<BillTaskEntity>, BatchOperationMapper<BillTaskEntity> {

    int updateBlacklineSyncStateByIdAndSyncStatus(@Param("newSyncState") SyncState newSyncState,
                                                  @Param("taskIds") List<Long> taskIds, @Param("oldSyncStatus") List<SyncState> oldSyncStatus);



    int updateBlacklineSyncStateErrorAndArchiveUrlByIdAndSyncState(BillTaskDTO billTaskDTO);


    int updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(@Param("taskState") TaskState taskState,
                                                                                      @Param("finishedDate") ZonedDateTime finishedDate,
                                                                                      @Param("sapSyncStatus") List<SyncState> sapSyncStatus,
                                                                                      @Param("blacklineSyncStatus") List<SyncState> blacklineSyncStatus,
                                                                                      @Param("since") ZonedDateTime since,
                                                                                      @Param("until") ZonedDateTime until,
                                                                                      @Param("ignoreTaskStatus") List<TaskState> ignoreTaskStatus);

    List<BillTaskEntity> findTasksByBlacklineStatus(@Param("createDate") LocalDate createDate,
                                           @Param("blacklineSyncState") SyncState blacklineSyncState);

    int updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(@Param("newSapSyncState") SyncState syncState,
                                                          @Param("sapNextRunTime") ZonedDateTime nextRunTime,
                                                          @Param("taskId") Long taskId, @Param("sapSyncStatus") List<SyncState> syncStatus);





    int updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus(BillTaskDTO billTaskDTO);
}
