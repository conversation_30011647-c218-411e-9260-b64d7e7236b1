package com.kering.cus.finance.billcenter.service.jd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.JdTradeFlowDAO;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractBlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.CsvWriterUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.DEFAULT_VALUE;
import static com.kering.cus.finance.billcenter.constant.Constants.JD_WALLET_FLOW_TYPE;

@Slf4j
@Service
public class JdBlacklineBillArchiveHandler extends AbstractBlacklineBillArchiveHandler implements BlacklineBillArchiveHandler {

    @Resource
    private JdTradeFlowDAO jdTradeFlowDAO;

    @Resource
    private JdWalletFlowDAO jdWalletFlowDAO;


    public JdBlacklineBillArchiveHandler(SftpConfig sftpConfig) {
        super(Channel.JD, sftpConfig);
    }

    @Override
    public boolean matches(Channel channel) {
        return Objects.equals(channel, Channel.JD);
    }

    @Override
    public boolean isGroupingByTmall() {
        return false;
    }


    @Override
    public Map<String,File> archive(Group group, long seqInGroup, List<Long> taskIds) throws IOException {
        Map<String,File> fileMap= new HashMap<>();
        log.info("Archiving JD Blackline Bill, group: {}, seq: {}, taskIds: {}", group, seqInGroup, taskIds);

        List<String> fileTypes = Lists.newArrayList(Constants.SUFFIX_XLSX, Constants.SUFFIX_CSV);
        for (String fileType : fileTypes) {
            File targetFile = FileUtils2.createTempFile(String.format("jd_blackline_%s", seqInGroup), fileType).toFile();
            try {
              boolean hasData;
              if(Objects.equals(fileType, Constants.SUFFIX_XLSX)){
                ExcelWriterUtils writer = new ExcelWriterUtils();
                writer.createSheet("JD_Blackline").writeHeader(getHeaders());
                boolean tradeHasData = writeJdTradeFlow(null, writer,taskIds);
                boolean walletHasData = writeJdWalletFlow(null,writer, taskIds, seqInGroup);
                writer.writeTo(targetFile);
                hasData = tradeHasData || walletHasData;
              }else{
                try (CsvWriterUtils writer = new CsvWriterUtils(targetFile)) {
                  writer.writeHeader(getHeaders());
                  boolean tradeHasData = writeJdTradeFlow(writer, null,taskIds);
                  boolean walletHasData = writeJdWalletFlow(writer,null, taskIds, seqInGroup);
                  hasData = tradeHasData || walletHasData;
                }
              }
              if (!hasData) {
                log.warn("No JD bill, taskIds: {}", taskIds);
                FileUtils.deleteQuietly(targetFile);
              }else{
                fileMap.put(fileType, targetFile);
              }
            }catch (Exception e) {
              log.error("Archive JD Blackline Bill failed, group: {}, seq: {}, taskIds: {}", group, seqInGroup, taskIds, e);
              FileUtils.deleteQuietly(targetFile);
              throw new BillCenterBusinessException(BillCenterErrorCode.BILL_BLACKLINE_ARCHIVE_FAILED, e);
            }

        }

        return fileMap;
    }



    private boolean writeJdTradeFlow(CsvWriterUtils csvWriterUtils,ExcelWriterUtils excelWriterUtils, List<Long> taskIds) throws IOException {
        AtomicInteger tradePageNum = new AtomicInteger(1);
        Page<JdTradeFlowEntity> jdTradePage;
        boolean hasData = false;
        do {
            jdTradePage = jdTradeFlowDAO.findJdTradeBillPageByParams(taskIds, BATCH_SIZE, tradePageNum.getAndIncrement());
            if (Objects.nonNull(jdTradePage) && !CollectionUtils.isEmpty(jdTradePage.getRecords())) {
                List<String[]> list = jdTradePage.getRecords().stream().map(this::getLines).toList();
                if(Objects.nonNull(csvWriterUtils)){
                    csvWriterUtils.writeBatch(list);
                }else{
                    excelWriterUtils.writeRows(list);
                }
                hasData = true;
            }
        } while (Objects.nonNull(jdTradePage) && jdTradePage.hasNext());

        return hasData;
    }

    private boolean writeJdWalletFlow(CsvWriterUtils csvWriterUtils,ExcelWriterUtils excelWriterUtils, List<Long> taskIds, long seqInGroup) throws IOException {
        if (seqInGroup != 1) {
            log.warn("JD wallet bill is not supported for grouping by tmall, seq: {}", seqInGroup);
            return false;
        }
        AtomicInteger walletPageNum = new AtomicInteger(1);
        Page<JdWalletFlowEntity> jdWalletPage;
        boolean hasData = false;
        do {
            jdWalletPage = jdWalletFlowDAO.findJdWalletBillPageByParams(taskIds, JD_WALLET_FLOW_TYPE, BATCH_SIZE, walletPageNum.getAndIncrement());
            if (Objects.nonNull(jdWalletPage) && !CollectionUtils.isEmpty(jdWalletPage.getRecords())) {
                List<String[]> list = jdWalletPage.getRecords().stream().map(this::getLines).toList();
               if(Objects.nonNull(csvWriterUtils)){
                 csvWriterUtils.writeBatch(list);
               }else {
                   excelWriterUtils.writeRows(list);
               }
                hasData = true;
            }
        } while (Objects.nonNull(jdWalletPage) && jdWalletPage.hasNext());
        return hasData;
    }

    private String[] getLines(JdTradeFlowEntity jdTradeFlowEntity) {
        return new String[]{
                StringUtils.defaultString(jdTradeFlowEntity.getBrand()),
                StringUtils.defaultString(jdTradeFlowEntity.getChannel()),
                StringUtils.defaultString(jdTradeFlowEntity.getMerchantId()),
                StringUtils.defaultString(jdTradeFlowEntity.getOrderId()),
                StringUtils.defaultString(jdTradeFlowEntity.getTransactionNo()),
                StringUtils.defaultString(jdTradeFlowEntity.getTransactionType()),
                StringUtils.defaultString(jdTradeFlowEntity.getSkuCode()),
                StringUtils.defaultString(jdTradeFlowEntity.getMerchantOrderId()),
                StringUtils.defaultString(jdTradeFlowEntity.getSkuName()),
                StringUtils.defaultString(jdTradeFlowEntity.getSettlementStatus()),
                DateUtils.convertDateStr(jdTradeFlowEntity.getOccurTime(), DateUtils.YYYYMMDD),
                DateUtils.convertDateStr(jdTradeFlowEntity.getChargeableTime(), DateUtils.YYYYMMDD),
                DateUtils.convertDateStr(jdTradeFlowEntity.getSettlementTime(), DateUtils.YYYYMMDD),
                StringUtils.defaultString(jdTradeFlowEntity.getFeeItem()),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(jdTradeFlowEntity.getAmount(), DEFAULT_VALUE),
                StringUtils.defaultString(jdTradeFlowEntity.getCurrency()),
                StringUtils.defaultString(jdTradeFlowEntity.getMerchantPaymentType()),
                StringUtils.defaultString(jdTradeFlowEntity.getSettlementNote()),
                StringUtils.defaultString(jdTradeFlowEntity.getShopCode()),
                StringUtils.defaultString(jdTradeFlowEntity.getJdStoreCode()),
                StringUtils.defaultString(jdTradeFlowEntity.getBrandStoreCode()),
                StringUtils.defaultString(jdTradeFlowEntity.getStoreName()),
                StringUtils.defaultString(jdTradeFlowEntity.getNote()),
                StringUtils.defaultString(jdTradeFlowEntity.getPaymentType()),
                StringUtils.defaultString(jdTradeFlowEntity.getBillingDate()),
                String.valueOf(jdTradeFlowEntity.getSkuQty())
        };


    }


    private String[] getLines(JdWalletFlowEntity jdWalletFlowEntity) {
        return new String[]{
                StringUtils.defaultString(jdWalletFlowEntity.getBrand()),
                StringUtils.defaultString(jdWalletFlowEntity.getChannel()),
                StringUtils.defaultString(jdWalletFlowEntity.getMerchantId()),
                StringUtils.defaultString(jdWalletFlowEntity.getPlatformOrderId()),
                "", "", "", "", "", "", "","", DateUtils.convertDateStr(jdWalletFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD),
                "货款", com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(jdWalletFlowEntity.getExpense(), DEFAULT_VALUE),
                "CNY", "应付", extractNotInfo(jdWalletFlowEntity.getTransactionNote()), "", "", "", "", "", "支出",
                DateUtils.minusDay(jdWalletFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD, 1), ""
        };


    }

    private String[] getHeaders() {
        return new String[]{"Brand", "Channel", "Settlement Merchant ID", "Order Number", "Document Number", "Document Type",
                "Product Number", "Merchant Order Number", "Product Name", "Settlement Status", "Expense Occurrence Time", "Expense Billing Time",
                "Expense Settlement Time", "Expense Item",
                "Amount", "Currency", "Merchant Receivable/Payable", "Wallet Settlement Remarks", "Store Number", "JD Store ID", "Brand Store ID",
                "Store Name", "Remarks", "Revenue and Expense Direction", "Billing Date", "Product Quantity"};
    }


    public static String extractNotInfo(String note) {
        if (StringUtils.isNotEmpty(note)) {
            String pattern = "(售后服务单号\\d+，对应订单号\\d+)";
            Pattern regex = Pattern.compile(pattern);
            Matcher matcher = regex.matcher(note);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        return "";
    }

    @Override
    protected String getBlacklineSftpTargetDirectory() {
        return sftpConfig.getBlacklineSftpJdArchiveDirectory();
    }
}
