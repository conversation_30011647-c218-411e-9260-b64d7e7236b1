package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.dto.BillTaskDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterConfigurationException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.Errors;
import com.kering.cus.finance.billcenter.util.Nulls;
import freework.util.Throwables;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import static com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler.Group;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklineBillArchiveService {

  private static final String OSS_PATH_FORMAT = "blackline/%s/%s/%s/%s";

  private static final DateTimeFormatter SFTP_FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
  private static final String SFTP_FILENAME_GROUP_FORMAT = "CNTRADE%s%s%02d_%s.%s";
  private static final String SFTP_FILENAME_NO_GROUP_FORMAT = "CNTRADE%s%02d_%s.%s";

  private static final String TMALL_GROUP = "TMALL";
  private static final String NON_TMALL_GROUP = "OTHER";

  private final BillCenterConfig billCenterConfig;
  private final AppStorageService appStorageService;

  private final BillTaskDAO billTaskDAO;
  private final BillTaskService billTaskService;
  private final TransitLogDAO transitLogDAO;
  private final List<BlacklineBillArchiveHandler> blacklineBillArchiveHandlers;

  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public boolean archiveIfNecessary(final Channel channel) {
    final ZonedDateTime now = ZonedDateTime.now();
    final ZonedDateTime since = now.toLocalDate().atStartOfDay(now.getZone());
    final List<BillTaskEntity> allTasks = billTaskDAO.findBlacklineTasksByChannelAndCreateTime(
        channel, since, now
    );
    if (allTasks.isEmpty()) {
      log.warn("[SKIP] blackline waiting archive task not found, channel: {}", channel);
      return false;
    }

    /*-
     * Check running tasks.
     */
    final List<Long> runningTaskIds = allTasks.stream().filter(this::isSyncRunning).map(BillTaskEntity::getId).toList();
    if (!CollectionUtils.isEmpty(runningTaskIds)) {
      log.warn(
          "[SKIP] blackline archive task includes {} tasks that are running, channel: {}, task id: {}",
          runningTaskIds.size(), channel, runningTaskIds
      );
      return false;
    }

    /*-
     * Check not ready tasks.
     */
    final List<Long> notReadyTaskIds = allTasks.stream().filter(this::isSyncNotReady).map(BillTaskEntity::getId).toList();
    if (!CollectionUtils.isEmpty(notReadyTaskIds)) {
      log.warn(
          "[SKIP] blackline archive task includes {} tasks that are not ready, channel: {}, task id: {}",
          notReadyTaskIds.size(), channel, notReadyTaskIds
      );
      return false;
    }

    /*-
     * Check ready tasks.
     */
    final List<BillTaskEntity> readyTasks = allTasks.stream().filter(this::isSyncReady).toList();
    if (CollectionUtils.isEmpty(readyTasks)) {
      log.warn("[SKIP] ready blackline archive task not found, channel: {}", channel);
      return false;
    }

    final List<Long> readyTaskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();
    final List<String> readyTraceIds = readyTasks.stream().map(BillTaskEntity::getTraceId).toList();

    billTaskService.updateSyncBlacklineReadyTaskToRunning(readyTaskIds);

    try {
      log.info("blackline archive starting, channel: {}, task id list: {}", channel, readyTaskIds);
      logTransitLog(readyTraceIds, "开始推送Blackline", "开始推送Blackline");

      final BlacklineBillArchiveHandler archiveHandler = determineBlacklineArchiveHandler(channel);
      if (null == archiveHandler) {
        throw new BillCenterConfigurationException(
            String.format("No %s found, channel: %s", BlacklineBillArchiveHandler.class.getSimpleName(), channel)
        );
      }

      final List<BillTaskEntity> successTasks = allTasks.stream().filter(this::isSyncSuccess).toList();
      if (!archiveHandler.isGroupingByTmall()) {
        return archiveNonGroupTasks(archiveHandler, channel, since, readyTasks, successTasks);
      } else {
        return archiveGroupByTmallTasks(archiveHandler, channel, since, readyTasks, successTasks);
      }
    } catch (final Exception ex) {
      return onArchiveError(channel, since, readyTasks, ex);
    }
  }

  private void onArchiveSuccess(final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final String archivedUrl,String blacklineArchiveExcelUrl) {
    final List<Long> readyTaskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();
    final List<String> readyTraceIds = readyTasks.stream().map(BillTaskEntity::getTraceId).toList();
    final SyncState archiveState = StringUtils.hasText(archivedUrl) ? SyncState.SUCCESS : SyncState.SKIP;

    BillTaskDTO parmas=BillTaskDTO.builder().newSyncState(archiveState).errorCount(0).errorMsg("OK").
            taskIds(readyTaskIds).syncStatus(Collections.singletonList(SyncState.RUNNING))
        .nextRunTime(ZonedDateTime.now()).archiveUrl(archivedUrl).archiveExcelUrl(blacklineArchiveExcelUrl).build();

    billTaskDAO.updateBlacklineSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncState(parmas);

    log.info("blackline archive successful, channel: {}, task id list: {}", channel, readyTaskIds);

    final String message = SyncState.SUCCESS.equals(archiveState) ? "推送Blackline成功" : "无需推送Blackline";
    logTransitLog(readyTraceIds, "推送Blackline完成", message);

    billTaskService.updateTaskStateBySapAndBlacklineSyncState(since, ZonedDateTime.now());
  }

  private boolean onArchiveError(final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final Throwable ex) {
    final List<Long> readyTaskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();
    final List<String> readyTraceIds = readyTasks.stream().map(BillTaskEntity::getTraceId).toList();

    log.warn("blackline archive error: {}, channel: {}, task id list: {}", ex.getMessage(), channel, readyTaskIds, ex);

    final boolean shouldRetry = !Throwables.causedBy(ex, BillCenterConfigurationException.class);
    final int errorCountIncr = shouldRetry ? 1 : billCenterConfig.getMaxRetryTimes() + 1;

    final BillTaskEntity tpl = readyTasks.iterator().next();
    final Integer prevErrorCount = Nulls.nvl(tpl.getBlacklineErrorCount());
    final int errorCount = Optional.ofNullable(prevErrorCount).map(c -> c + errorCountIncr).orElse(errorCountIncr);

    final long delay = billCenterConfig.isBackoffRetry() ? (long) Math.pow(2, errorCount) : 0;
    final ZonedDateTime nextRetryTime = ZonedDateTime.now().plusMinutes(delay);
    final String message = Errors.format(ex.getMessage(), 200);


    BillTaskDTO billTaskDTO=BillTaskDTO.builder().newSyncState(SyncState.FAILED).errorCount(errorCount).errorMsg(message).
        taskIds(readyTaskIds).syncStatus(Collections.singletonList(SyncState.RUNNING))
        .nextRunTime(nextRetryTime).build();

    billTaskDAO.updateBlacklineSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncState(billTaskDTO);
    logTransitLog(readyTraceIds, "推送Blackline失败", message);

    billTaskService.updateTaskStateBySapAndBlacklineSyncState(since, ZonedDateTime.now());

    if (!shouldRetry) {
      return false;
    }
    if (ex instanceof BillCenterBusinessException bizThrown) {
      throw bizThrown;
    }
    throw new BillCenterBusinessException(BillCenterErrorCode.BILL_BLACKLINE_ARCHIVE_FAILED, ex);
  }

  private boolean archiveNonGroupTasks(final BlacklineBillArchiveHandler archiveHandler,
      final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final List<BillTaskEntity> successTasks) {
    final StopWatch sw = new StopWatch(String.format("Blackline-archive-NON-GROUP-%s", channel));
    final long seq = calcTaskBatchSeq(successTasks, t -> true);
    final List<Long> taskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();

    Map<String, File> archiveMap = null;
    try {
      sw.start("generate NON-GROUP archive file");
      archiveMap = archiveHandler.archive(Group.NONE, seq, taskIds);
      sw.stop();

      Map<String, String> map = new HashMap<>();
      if(!CollectionUtils.isEmpty(archiveMap)){
        for (Entry<String, File> entry : archiveMap.entrySet()) {
          String fileType = entry.getKey();
          File archive = entry.getValue();
          final String suffix = FilenameUtils.getExtension(archive.getName());
          final String filename = generateBlacklineFilename(channel, null, seq, suffix);

          sw.start("upload NON-GROUP archive file to OSS");
          final String ossPath = uploadToOss(filename, archive);
          sw.stop();

          if (Objects.equals(fileType, Constants.SUFFIX_CSV)) {
            sw.start("send NON-GROUP archive file to blackline");
            archiveHandler.sendToBlackline(Collections.singletonMap(filename, archive));
            sw.stop();
          }
          map.put(fileType, ossPath);
        }
        final String archivedUrl = map.get(Constants.SUFFIX_CSV);
        final String archivedExcelUrl = map.get(Constants.SUFFIX_XLSX);
        onArchiveSuccess(channel, since, readyTasks, archivedUrl,archivedExcelUrl);
      }else{
        onArchiveSuccess(channel, since, readyTasks, null,null);
      }
      return true;
    } catch (Exception e) {
      return onArchiveError(channel, since, readyTasks, e);
    } finally {
      if (sw.isRunning()) {
        sw.stop();
      }
      log.info("blackline NON-GROUP archive elapsed: {}", sw.prettyPrint());
      if (archiveMap != null) {
         archiveMap.forEach((filename, archive) -> FileUtils.deleteQuietly(archive));
      }
    }
  }

  private boolean archiveGroupByTmallTasks(final BlacklineBillArchiveHandler archiveHandler,
      final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final List<BillTaskEntity> successTasks) {
    final List<BillTaskEntity> tmallTasks = readyTasks.stream().filter(t -> Boolean.TRUE.equals(t.getIsTmall())).toList();
    final List<BillTaskEntity> nonTmallTasks = readyTasks.stream().filter(t -> !Boolean.TRUE.equals(t.getIsTmall())).toList();

    boolean tmallArchived = true;
    boolean nonTmallArchived = true;
    if (!CollectionUtils.isEmpty(tmallTasks)) {
      tmallArchived = doArchiveTmallTasks(archiveHandler, channel, since, tmallTasks, successTasks);
    }
    if (!CollectionUtils.isEmpty(nonTmallTasks)) {
      nonTmallArchived = doArchiveNonTmallGroupTasks(archiveHandler, channel, since, nonTmallTasks, successTasks);
    }
    return tmallArchived || nonTmallArchived;
  }

  private boolean doArchiveTmallTasks(final BlacklineBillArchiveHandler archiveHandler,
      final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final List<BillTaskEntity> successTasks) {
    /*-
     * Genreate TMALL file.
     */
    final StopWatch sw = new StopWatch(String.format("Blackline-archive-TMALL-%s", channel));
    final List<Long> taskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();
    final long tmallSeq = calcTaskBatchSeq(successTasks, t -> Boolean.TRUE.equals(t.getIsTmall()));
    Map<String,File> tmallArchiveMap = null;
    try {
      sw.start("generate TMALL archive file");
      tmallArchiveMap = archiveHandler.archive(Group.TMALL, tmallSeq, taskIds);
      sw.stop();

      Map<String, String> map = new HashMap<>();
      if(!CollectionUtils.isEmpty(tmallArchiveMap)){
        for (Entry<String, File> entry : tmallArchiveMap.entrySet()) {
          String fileType = entry.getKey();
          File tmallArchive = entry.getValue();
          final String suffix = FilenameUtils.getExtension(tmallArchive.getName());
          final String filename = generateBlacklineFilename(channel, TMALL_GROUP, tmallSeq, suffix);

          sw.start("upload TMALL archive file to OSS");
          final String ossPath = uploadToOss(filename, tmallArchive);
          sw.stop();

          if(Objects.equals(fileType, Constants.SUFFIX_CSV)){
            sw.start("send TMALL archive file to blackline");
            archiveHandler.sendToBlackline(Collections.singletonMap(filename, tmallArchive));
            sw.stop();
          }
          map.put(fileType, ossPath);
        }
        final String archivedUrl = map.get(Constants.SUFFIX_CSV);
        final String archivedExcelUrl = map.get(Constants.SUFFIX_XLSX);
        onArchiveSuccess(channel, since, readyTasks, archivedUrl,archivedExcelUrl);
      }else {
        onArchiveSuccess(channel, since, readyTasks, null,null);
      }
      return true;
    } catch (Exception e) {
      return onArchiveError(channel, since, readyTasks, e);
    } finally {
      if (sw.isRunning()) {
        sw.stop();
      }
      log.info("blackline TMALL archive elapsed: {}", sw.prettyPrint());
      if (tmallArchiveMap != null) {
        tmallArchiveMap.forEach((fileType, archive) -> FileUtils.deleteQuietly(archive));
      }
    }
  }

  private boolean doArchiveNonTmallGroupTasks(final BlacklineBillArchiveHandler archiveHandler,
      final Channel channel, final ZonedDateTime since,
      final List<BillTaskEntity> readyTasks, final List<BillTaskEntity> successTasks) {
    /*-
     * Genreate NON-TMALL file.
     */
    final StopWatch sw = new StopWatch(String.format("Blackline-archive-NON-TMALL-%s", channel));
    final List<Long> taskIds = readyTasks.stream().map(BillTaskEntity::getId).toList();
    final long tmallSeq = calcTaskBatchSeq(successTasks, t -> !Boolean.TRUE.equals(t.getIsTmall()));
    Map<String,File> archiveFileMap = null;
    try {
      sw.start("generate NON-TMALL archive file");
      archiveFileMap = archiveHandler.archive(Group.NON_TMALL, tmallSeq, taskIds);
      sw.stop();

      if(CollectionUtils.isEmpty(archiveFileMap)){
        onArchiveSuccess(channel, since, readyTasks, null,null);
      }else{
        Map<String, String> map = new HashMap<>();
        for (Entry<String, File> entry : archiveFileMap.entrySet()) {
          String fileType = entry.getKey();
          File archiveFile = entry.getValue();
          final String suffix = FilenameUtils.getExtension(archiveFile.getName());
          final String filename = generateBlacklineFilename(channel, NON_TMALL_GROUP, tmallSeq, suffix);

          sw.start("upload NON-TMALL archive file to OSS");
          final String ossPath = uploadToOss(filename, archiveFile);
          sw.stop();

          if(Objects.equals(fileType, Constants.SUFFIX_CSV)){
            sw.start("send NON-TMALL archive file to blackline");
            archiveHandler.sendToBlackline(Collections.singletonMap(filename, archiveFile));
            sw.stop();
          }
          map.put(fileType, ossPath);
        }
        final String archivedUrl = map.get(Constants.SUFFIX_CSV);
        final String archivedExcelUrl = map.get(Constants.SUFFIX_XLSX);
        onArchiveSuccess(channel, since, readyTasks, archivedUrl,archivedExcelUrl);
      }


      return true;
    } catch (Exception e) {
      return onArchiveError(channel, since, readyTasks, e);
    } finally {
      if (sw.isRunning()) {
        sw.stop();
      }
      log.info("blackline NON-TMALL archive elapsed: {}", sw.prettyPrint());
      if (archiveFileMap != null) {
        archiveFileMap.forEach((fileType, archive) -> FileUtils.deleteQuietly(archive));
      }
    }
  }

  private long calcTaskBatchSeq(final List<BillTaskEntity> successTasks, final Predicate<BillTaskEntity> predicate) {
    return successTasks.stream()
        .filter(predicate)
        .map(BillTaskEntity::getBlacklineArchiveUrl)
        .distinct()
        .count() + 1;
  }


  private void logTransitLog(final List<String> traceIds, final String title, final String message) {
    final List<TransitLogEntity> logs = traceIds.stream().map(traceId -> {
      final TransitLogEntity transitLog = TransitLogEntity.builder()
          .traceId(traceId)
          .title(title)
          .message(Errors.format(message, 200))
          .build();
      EntityUtils.fill(transitLog, ZonedDateTime.now());
      return transitLog;
    }).toList();
    transitLogDAO.createBatch(logs);
  }

  BlacklineBillArchiveHandler determineBlacklineArchiveHandler(final Channel channel) {
    return blacklineBillArchiveHandlers.stream()
        .filter(handler -> handler.matches(channel))
        .findFirst()
        .orElse(null);
  }

  private String uploadToOss(final String filename, final File source) throws IOException {
    final String ossPath = generateBlacklineOssPath(filename);
    try (final InputStream in = new FileInputStream(source)) {
      appStorageService.writeStream(ossPath, in);
    }
    return ossPath;
  }

  private String generateBlacklineFilename(final Channel channel,
      final String group,
      final long seq, final String suffix) {
    // update 20250725 timestamp use prev day.
    final String timestamp = ZonedDateTime.now().minusDays(1).format(SFTP_FILENAME_DATE_FORMATTER);
    return StringUtils.hasText(group)
        ? String.format(SFTP_FILENAME_GROUP_FORMAT, group, channel, seq, timestamp, suffix)
        : String.format(SFTP_FILENAME_NO_GROUP_FORMAT, channel, seq, timestamp, suffix);
  }

  private String generateBlacklineOssPath(final String filename) {
    /*-
     * blackline/${year}/${month}/${day}/${filename}.
     */
    final ZonedDateTime now = ZonedDateTime.now();
    return String.format(
        OSS_PATH_FORMAT,
        now.getYear(), now.getMonth().getValue(), now.getDayOfMonth(),
        filename
    );
  }

  private boolean isSyncReady(final BillTaskEntity task) {
    final SyncState state = task.getBlacklineSyncState();
    return SyncState.WAITING.equals(state) || SyncState.FAILED.equals(state);
  }

  private boolean isSyncNotReady(final BillTaskEntity task) {
    return SyncState.UNSET.equals(task.getBlacklineSyncState());
  }

  private boolean isSyncRunning(final BillTaskEntity task) {
    return SyncState.RUNNING.equals(task.getBlacklineSyncState());
  }

  private boolean isSyncSuccess(final BillTaskEntity task) {
    return SyncState.SUCCESS.equals(task.getBlacklineSyncState());
  }

}