package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.EmailService;
import com.kering.cus.lib.log.annotation.Probe;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping
public class EmailTestController {
    @Autowired
    private EmailService emailService;

    @Operation(summary = "redirect to oauth2 connector")
    @Probe(event = "redirectToOauth2Connector", group = "controller", retVal = true, error = true)
    @GetMapping("api/email/send")
    public ResponseEntity<Void> send() {
        emailService.send("Bill Center-Test", "TEST");
        return ResponseEntity.ok().build();
    }
}
