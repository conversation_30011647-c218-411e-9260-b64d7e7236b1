package com.kering.cus.finance.billcenter.service;

import com.google.common.base.Preconditions;
import com.kering.cus.finance.billcenter.client.oauth2.AuthorizationInfo;
import com.kering.cus.finance.billcenter.client.oauth2.Oauth2Client;
import com.kering.cus.finance.billcenter.client.oauth2.alipay.AlipayOauth2Client;
import com.kering.cus.finance.billcenter.config.AlipayIsvAppProperties;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.OauthAccessTokenDAO;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import com.kering.cus.finance.billcenter.util.Errors;
import com.kering.cus.finance.billcenter.util.Nulls;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class OauthGrantService {

    /**
     * for mock;
     */
    protected Oauth2Client mockClient;

    private final BillCenterConfig config;
    private final AppSecretService appSecretService;
    private final OauthAccessTokenDAO oauthAccessTokenDAO;
    private final MerchantGrantConfigDAO merchantGrantConfigDAO;


    public String getAuthorizeUrl(Channel channel, String redirectUri) {
        final Oauth2Client client = determineOauth2Client(channel);
        return null != client ? client.getAuthorizeUrl(redirectUri, null) : null;
    }

    @Transactional
    public AuthorizationInfo doAuthorizeCallback(Channel channel, Map<String, String> callbackParams) {
        final Oauth2Client client = determineOauth2Client(channel);
        if (null == client) {
            return null;
        }

        final ZonedDateTime when = ZonedDateTime.now();
        final AuthorizationInfo authorizationInfo = client.getAuthorizationInfo(callbackParams);

        final String merchantId = authorizationInfo.getMerchantId();
        Preconditions.checkState(StringUtils.hasText(merchantId), "Merchant id not found");

        final OauthAccessTokenEntity tokenToRefresh = oauthAccessTokenDAO.findByPlatformAndMerchantId(channel, merchantId).orElseGet(() -> {
            final OauthAccessTokenEntity tokenToCreate = new OauthAccessTokenEntity();
            tokenToCreate.setErrorCount(0);
            tokenToCreate.setVersion(0L);
            tokenToCreate.setCreatedDate(when);
            tokenToCreate.setDeleted(Boolean.FALSE);
            return tokenToCreate;
        });

        return this.onAuthorizationRefresh(channel, tokenToRefresh, authorizationInfo, when);
    }

    public void refreshAccessTokenIfNecessary() {
        final List<Long> ids = getRefreshAccessTokenIds(100);
        ids.forEach(this::refreshAccessTokenIfNecessary);
    }

    public List<Long> getRefreshAccessTokenIds(final int limit) {
        final ZonedDateTime now = ZonedDateTime.now();
        final int maxRetryTimes = config.getMaxRetryTimes();
        return oauthAccessTokenDAO.findRefreshAccessTokenIds(now, maxRetryTimes, limit);
    }

    public void refreshAccessTokenIfNecessary(final Long id) {
        final ZonedDateTime when = ZonedDateTime.now();
        final Optional<OauthAccessTokenEntity> tokenToRefreshOpt = oauthAccessTokenDAO.findById(id);
        if (tokenToRefreshOpt.isEmpty()) {
            return;
        }

        final OauthAccessTokenEntity tokenToRefresh = tokenToRefreshOpt.get();
        final ZonedDateTime refreshTime = tokenToRefresh.getNextRefreshTime();
        final String refreshToken = tokenToRefresh.getRefreshToken();
        if (null == refreshTime || refreshTime.isAfter(when) || !StringUtils.hasText(refreshToken)) {
            return;
        }

        final Channel channel = tokenToRefresh.getPlatform();
        try {
            final Oauth2Client client = determineOauth2Client(channel);
            if (null == client) {
                return;
            }
            final AuthorizationInfo authorizationInfo = client.refreshAccessToken(refreshToken, tokenToRefresh.getAccessToken());
            this.onAuthorizationRefresh(channel, tokenToRefresh, authorizationInfo, when);
        } catch (final RuntimeException ex) {
            log.warn("Refresh access token error, channel = {}, merchant = {}, error: {}", channel, tokenToRefresh.getMerchantId(), ex.getMessage(), ex);

            final String error = Errors.format(null, ex.getMessage());
            final int errorCount = Nulls.nvl(tokenToRefresh.getErrorCount()) + 1;
            tokenToRefresh.setErrorMsg(error);
            tokenToRefresh.setErrorCount(errorCount);
            tokenToRefresh.setNextRefreshTime(when.plusMinutes((long) Math.pow(2, errorCount)));
            oauthAccessTokenDAO.update(tokenToRefresh);
        }
    }

    private AuthorizationInfo onAuthorizationRefresh(final Channel channel, final OauthAccessTokenEntity tokenToRefresh,
                                                     final AuthorizationInfo authorizationInfo, final ZonedDateTime when) {
        final String merchantId = authorizationInfo.getMerchantId();
        Preconditions.checkState(StringUtils.hasText(merchantId));

        final long expiresIn = authorizationInfo.getExpiresIn();
        final long reExpiresIn = authorizationInfo.getReExpiresIn();

        final long expiresInToUse = expiresIn > 0 ? expiresIn : reExpiresIn;
        final long reExpiresInToUse = reExpiresIn > 0 ? reExpiresIn : expiresIn;
        final ZonedDateTime expiresAt = when.plusSeconds(expiresInToUse);
        final ZonedDateTime reExpiresAt = when.plusSeconds(reExpiresInToUse);

        final long latestThreshold = TimeUnit.MINUTES.toSeconds(1);
        final long earliestThreshold = TimeUnit.MINUTES.toSeconds(30);
        final long refreshThreshold = Math.max(Math.min(expiresInToUse / 3, earliestThreshold), latestThreshold);
        final ZonedDateTime refreshAt = expiresAt.minusSeconds(refreshThreshold);

        tokenToRefresh.setPlatform(channel);
        tokenToRefresh.setUserId(authorizationInfo.getUserId());
        tokenToRefresh.setOpenId(authorizationInfo.getOpenId());
        tokenToRefresh.setMerchantId(merchantId);
        tokenToRefresh.setClientId(authorizationInfo.getClientId());

        tokenToRefresh.setAccessToken(authorizationInfo.getAccessToken());
        tokenToRefresh.setRefreshToken(authorizationInfo.getRefreshToken());
        tokenToRefresh.setExpiresAt(expiresAt);
        tokenToRefresh.setReExpiresAt(reExpiresAt);
        tokenToRefresh.setNextRefreshTime(refreshAt);

        tokenToRefresh.setErrorCount(0);
        tokenToRefresh.setErrorMsg("OK");

        tokenToRefresh.setModifiedDate(ZonedDateTime.now());

        if (null == tokenToRefresh.getId()) {
            final MerchantGrantConfigEntity grantConfig = MerchantGrantConfigEntity.builder()
                    .merchantId(merchantId)
                    .channel(channel)
                    .grantType(GrantType.ISV)
                    .kmsBrandId(Constants.ISV_KMS_BRAND_ID)
                    .isEnabled(Boolean.FALSE)
                    .build();
            grantConfig.setDeleted(Boolean.FALSE);
            grantConfig.setCreatedDate(ZonedDateTime.now());
            grantConfig.setModifiedDate(grantConfig.getCreatedDate());
            grantConfig.setVersion(0L);

            oauthAccessTokenDAO.create(tokenToRefresh);
            merchantGrantConfigDAO.create(grantConfig);
        } else {
            oauthAccessTokenDAO.update(tokenToRefresh);
        }
        return authorizationInfo;
    }

    private Oauth2Client determineOauth2Client(Channel channel) {
        if (Channel.ALIPAY.equals(channel)) {
            final AlipayIsvAppProperties alipayProps = appSecretService.getIsvAppSecret(channel.name(), AlipayIsvAppProperties.class);
            return createAlipayOauth2Client(
                    alipayProps.getAppId(),
                    alipayProps.getAppPrivateKey(),
                    alipayProps.getAlipayPublicKey()
            );
        }
        return null;
    }

    protected Oauth2Client createAlipayOauth2Client(String appId, String appPrivateKey, String alipayPublicKey) {
        return null != mockClient ? mockClient : new AlipayOauth2Client(appId, appPrivateKey, alipayPublicKey);
    }

}
