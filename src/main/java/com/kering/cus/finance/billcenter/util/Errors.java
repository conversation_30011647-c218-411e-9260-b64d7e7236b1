package com.kering.cus.finance.billcenter.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class Errors {

    public static final int DEFAULT_MAX_LENGTH = 200;

    public static String format(final String errorCode, final String errorMsg) {
        return format(null, errorCode, errorMsg, DEFAULT_MAX_LENGTH);
    }

    public static String format(final String prefix, final String errorCode, final String errorMsg) {
        return format(prefix, errorCode, errorMsg, -1);
    }

    public static String format(final String errorCode, final String errorMsg, final int maxLength) {
        return format(null, errorCode, errorMsg, maxLength);
    }

    public static String format(final String prefix, final String errorCode, final String errorMsg, final int maxLength) {
        final StringBuilder buff = new StringBuilder();
        if (StringUtils.hasText(prefix)) {
            buff.append(prefix).append(": ");
        }
        buff.append(errorMsg);
        if (StringUtils.hasText(errorCode)) {
            buff.append("(").append(errorCode).append(")");
        }
        final int expectedLength = 0 >= maxLength ? buff.length() : maxLength;
        return buff.substring(0, Math.min(expectedLength, buff.length()));
    }

    public static String format(final String errorMsg, final int maxLength) {
        String errorMsgFormat = StringUtils.hasText(errorMsg) ? errorMsg : "(null)";
        final int expectedLength = 0 >= maxLength ? errorMsgFormat.length() : maxLength;
        return errorMsgFormat.substring(0, Math.min(expectedLength, errorMsgFormat.length()));
    }
}
