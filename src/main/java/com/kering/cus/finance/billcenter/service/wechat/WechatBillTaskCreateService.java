package com.kering.cus.finance.billcenter.service.wechat;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.service.support.AbstractApiBillTaskCreateService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Wechat bill task create service.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Service
public class WechatBillTaskCreateService extends AbstractApiBillTaskCreateService {

    public WechatBillTaskCreateService(final BillTaskDAO billTaskDAO,
                                       final TransitLogDAO transitLogDAO,
                                       final MerchantConfigDAO merchantConfigDAO,
                                       final MerchantGrantConfigDAO merchantGrantConfigDAO) {
        super(Channel.WECHAT, billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected List<BillType> getSupportedBillTypes() {
        return Arrays.asList(
                BillType.TRADE_FLOW,
                BillType.FUND_FLOW
        );
    }

    @Override
    protected boolean determineSyncToSap(BillType billType, boolean udfSyncToSap) {
        return BillType.FUND_FLOW.equals(billType) && udfSyncToSap;
    }

    @Override
    protected boolean determineSyncToBlackline(BillType billType, boolean udfSyncToBlackline) {
        return BillType.TRADE_FLOW.equals(billType) && udfSyncToBlackline;
    }

    @Override
    protected List<BillType> determineBillTypes(List<BillType> billTypes, boolean isWosaipayMerchant) {
        if (!isWosaipayMerchant || !billTypes.contains(BillType.TRADE_FLOW)) {
            return billTypes;
        }
        final List<BillType> types = Lists.newArrayList(billTypes);
        types.remove(BillType.TRADE_FLOW);
        return types;
    }

}