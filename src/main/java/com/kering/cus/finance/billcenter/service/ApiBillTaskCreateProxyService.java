package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.scheduler.dynamic.SchedulerTarget;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;

@Lazy
@Service
@Slf4j
@RequiredArgsConstructor
public class ApiBillTaskCreateProxyService {
    private static final String JOB_SOURCE = "SCHEDULER";

    private final BillTaskEventPublisher billTaskEventPublisher;
    private final List<ApiBillTaskCreateService> billTaskCreateServices;

    /**
     * Creates initialize bill task from JOB.
     *
     * @param channel payment channel
     */
    @Probe(event = "createInitBillTasks", group = "billTask")
    @SchedulerTarget(name = "CRON_CREATE_INIT_BILL_TASK")
    public boolean createTaskIfNecessary(final String channel) {
        return createTaskIfNecessary(
                Channel.valueOf(channel),
                ZonedDateTime.now(),
                JOB_SOURCE
        );
    }

    /**
     * Create all grant merchant bill task if necessary.
     *
     * @param channel the channel
     * @param when    the trigger time
     * @param source  the task source
     * @return the created task id list
     */
    public boolean createTaskIfNecessary(final Channel channel, final ZonedDateTime when, final String source) {
        final List<Long> taskIds = getRequiredApiTaskCreateService(channel).createTaskIfNecessary(when, source);
        return !CollectionUtils.isEmpty(taskIds) && dispatchWaitAcquireTasks(taskIds);
    }

    /**
     * Create given grant merchant bill task if necessary.
     *
     * @param channel    the channel
     * @param merchantId the merchant id
     * @param billType   the bill type
     * @param when       the trigger time
     * @param source     the task source
     * @return the created task id list
     */
    public boolean createTaskIfNecessary(final Channel channel,
                                         final String merchantId, final BillType billType,
                                         final ZonedDateTime when, final String source) {
        final List<Long> taskIds = getRequiredApiTaskCreateService(channel).createTaskIfNecessary(
                merchantId, Collections.singletonList(billType), when, source
        );
        return !CollectionUtils.isEmpty(taskIds) && dispatchWaitAcquireTasks(taskIds);
    }

    private boolean dispatchWaitAcquireTasks(final List<Long> taskIds) {
        log.info("dispatch wait acquire tasks: {}", taskIds);
        billTaskEventPublisher.publishWaitAcquireEvent(taskIds);
        return true;
    }

    private ApiBillTaskCreateService getRequiredApiTaskCreateService(final Channel channel) {
        return billTaskCreateServices.stream()
                .filter(service -> service.matches(channel))
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException("No service found for channel: " + channel));
    }
}