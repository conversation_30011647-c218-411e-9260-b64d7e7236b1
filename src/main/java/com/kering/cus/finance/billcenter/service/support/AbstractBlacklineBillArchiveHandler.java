package com.kering.cus.finance.billcenter.service.support;

import com.google.common.base.Preconditions;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import freework.net.Http;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

@RequiredArgsConstructor
public abstract class AbstractBlacklineBillArchiveHandler implements BlacklineBillArchiveHandler {
    protected final Channel channel;
    protected final SftpConfig sftpConfig;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final Channel channel) {
        return Objects.equals(channel, this.channel);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void sendToBlackline(final Map<String, File> filenameToFile) throws IOException {
        Preconditions.checkArgument(null != filenameToFile && !filenameToFile.isEmpty(), "filenameToFile cannot be null");

        final int timeout = sftpConfig.getSftpConnectTimeout();
        final String hostname = getBlacklineSftpHostname();
        final int port = getBlacklineSftpPort();
        final String username = getBlacklineSftpUsername();
        final String password = getBlacklineSftpPassword();
        final String privateKeyStr = getBlacklineSftpPrivateKey();
        final String passphraseStr = getBlacklineSftpPrivateKeyPassphrase();
        final byte[] privateKey = !StringUtils.hasText(privateKeyStr) ? null : privateKeyStr.getBytes(StandardCharsets.UTF_8);
        final byte[] passphrase = !StringUtils.hasText(passphraseStr) ? null : passphraseStr.getBytes(StandardCharsets.UTF_8);

        final String targetDirectory = getBlacklineSftpTargetDirectory();
        final ChannelSftp sftp = SftpUtils.connect(hostname, port, username, password, privateKey, passphrase, timeout);
        try {
            if (!SftpUtils.mkdirs(sftp, targetDirectory)) {
                throw new IOException(String.format("Failed to mkdirs %s", targetDirectory));
            }

            for (final Map.Entry<String, File> entry : filenameToFile.entrySet()) {
                final String filename = entry.getKey();
                final File source = entry.getValue();
                final String targetPath = Http.resolveUrl(targetDirectory, filename);
                try (final InputStream in = new FileInputStream(source)) {
                    sftp.put(in, targetPath);
                }
            }

        } catch (final SftpException e) {
            throw new IOException(String.format("Failed to upload file to blackline SFTP server: %s", e.getMessage()), e);
        } finally {
            SftpUtils.disconnect(sftp);
        }
    }

    protected String getBlacklineSftpHostname() {
        return sftpConfig.getBlacklineSftpHostname();
    }

    protected int getBlacklineSftpPort() {
        return sftpConfig.getBlacklineSftpPort();
    }

    protected String getBlacklineSftpUsername() {
        return sftpConfig.getBlacklineSftpUsername();
    }

    protected String getBlacklineSftpPassword() {
        return sftpConfig.getBlacklineSftpPassword();
    }

    protected String getBlacklineSftpPrivateKey() {
        return sftpConfig.getBlacklineSftpPrivateKey();
    }

    protected String getBlacklineSftpPrivateKeyPassphrase() {
        return sftpConfig.getBlacklineSftpPrivateKeyPassphrase();
    }

    protected abstract String getBlacklineSftpTargetDirectory();

}
