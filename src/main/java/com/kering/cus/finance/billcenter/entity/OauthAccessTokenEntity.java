package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.time.ZonedDateTime;

/**
 * Oauth access token.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_oauth_access_token")
public class OauthAccessTokenEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("platform")
    private Channel platform;

    @TableField("client_id")
    private String clientId;

    @TableField("user_id")
    private String userId;

    @TableField("open_id")
    private String openId;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("access_token")
    private String accessToken;

    @TableField("refresh_token")
    private String refreshToken;

    @TableField("expires_at")
    private ZonedDateTime expiresAt;

    @TableField("re_expires_at")
    private ZonedDateTime reExpiresAt;

    @TableField("error_count")
    private Integer errorCount;

    @TableField("error_msg")
    private String errorMsg;

    @TableField("next_refresh_time")
    private ZonedDateTime nextRefreshTime;

}
