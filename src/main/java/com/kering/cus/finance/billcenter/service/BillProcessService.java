package com.kering.cus.finance.billcenter.service;

import com.google.common.base.Preconditions;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.*;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterConfigurationException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.Errors;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import freework.io.IOUtils;
import freework.util.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.*;
import java.nio.file.Path;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Bill file parse&process service.
 *
 * <AUTHOR>
 * @since 20250708
 */
@Slf4j
@RequiredArgsConstructor
@Lazy
@Service
public class BillProcessService {
    protected static final int BATCH_SIZE = 1000;
    protected static final int MAX_BATCH_COUNT = 100;
    private static final String MERCHANT_DIV = ",";

    private static final List<TaskState> ALLOWED_STATUS = Arrays.asList(
            TaskState.WAIT_PROCESS, TaskState.PROCESS_FAILED
    );

    private final BillCenterConfig billCenterConfig;
    private final BillTaskDAO billTaskDAO;
    private final TransitLogDAO transitLogDAO;
    private final AppStorageService appStorageService;
    private final List<BillProcessHandler> billProcessHandlers;
    private final BillTaskEventPublisher billTaskEventPublisher;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void processRetryIfNecessary() {
        final ZonedDateTime now = ZonedDateTime.now();
        final ZonedDateTime since = now.toLocalDate().atStartOfDay(now.getZone());
        final int maxRetryTimes = billCenterConfig.getMaxRetryTimes();
        final int timeoutMinutes = billCenterConfig.getProcessTaskTimeoutMinutes();

        int batchCount = 0;
        List<BillTaskEntity> waitProcessTasks;
        do {
            batchCount++;
            waitProcessTasks = billTaskDAO.findWaitProcessTasks(since, now, maxRetryTimes, BATCH_SIZE);
            for (final BillTaskEntity waitProcessTask : waitProcessTasks) {
                final int updated = billTaskDAO.updateNextRunTimeByIdAndVersion(
                        now.plusMinutes(timeoutMinutes),
                        waitProcessTask.getId(), waitProcessTask.getVersion()
                );
                if (updated == 1) {
                    log.info("dispatch retry process task, id: {}", waitProcessTask.getId());
                    billTaskEventPublisher.publishWaitProcessEvent(waitProcessTask.getId());
                } else {
                    log.warn("[SKIP] retry process task already running, id: {}", waitProcessTask.getId());
                }
            }
        } while (waitProcessTasks.size() >= BATCH_SIZE && batchCount < MAX_BATCH_COUNT);
    }

    /**
     * Try parse and process bill task.
     *
     * @param taskId the task id
     * @return return true if parse and process successful otherwise false
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean processIfNecessary(final Long taskId) {
        final BillTaskEntity task = billTaskDAO.findById(taskId).orElse(null);
        if (null == task) {
            log.warn("[SKIP] bill process task not found, id = {}", taskId);
            return false;
        }

        final ZonedDateTime now = ZonedDateTime.now();
        final String traceId = task.getTraceId();
        final TaskState taskState = task.getState();
        if (!ALLOWED_STATUS.contains(taskState)) {
            log.warn("[SKIP] bill process task state({}) mismatch, id = {}", taskState, taskId);
            return false;
        }

        final ZonedDateTime nextRunTimeToUse = now.plusMinutes(billCenterConfig.getProcessTaskTimeoutMinutes());
        final int updated = billTaskDAO.updateNextRunTimeByIdAndVersion(nextRunTimeToUse, taskId, task.getVersion());
        if (updated <= 0) {
            log.warn("[SKIP] failed to update the version, the process task may have already been executed, id = {}", taskId);
            return false;
        }

        try {
            log.info("bill process starting, task id: {}", taskId);
            logTransitLog(traceId, "开始解析账单", "开始解析账单");

            final BillProcessHandler billProcessHandler = determineBillProcessHandler(
                    task.getChannel(), task.getGrantType(), task.getBillType()
            );
            if (null == billProcessHandler) {
                throw new BillCenterConfigurationException(
                        String.format("No %s found, task id: %s", BillProcessHandler.class.getSimpleName(), taskId)
                );
            }

            final String originBillUrl = task.getOriginBillUrl();
            final String suffix = FilenameUtils.getExtension(originBillUrl);
            final InputStream in = appStorageService.readStream(task.getOriginBillUrl());
            if (null == in) {
                throw new IOException("can't read data from url: " + originBillUrl);
            }

            File tempFile = createTempFile(task.getBillType(), suffix).toFile();
            tempFile.deleteOnExit();
            try (final OutputStream out = new FileOutputStream(tempFile)) {
                IOUtils.flow(in, out, true, true);

                final StopWatch sw = new StopWatch("bill-process-" + taskId);
                sw.start();

                final List<String> merchantIds = billProcessHandler.process(tempFile, task);
                sw.stop();
                log.info("bill process elapsed: {}", sw.prettyPrint());

                final boolean shouldSync = Boolean.TRUE.equals(task.getIsSyncToSap()) || Boolean.TRUE.equals(task.getIsSyncToBlackline());
                final TaskState nextState = shouldSync ? TaskState.PROCESS_SUCCESS : TaskState.SYNC_SKIP;
                final SyncState sapSyncState = Boolean.TRUE.equals(task.getIsSyncToSap()) ? SyncState.WAITING : SyncState.SKIP;
                final SyncState blacklineSyncState = Boolean.TRUE.equals(task.getIsSyncToBlackline()) ? SyncState.WAITING : SyncState.SKIP;
                final String merchantIdToUse = CollectionUtils.isEmpty(merchantIds) ? null : String.join(MERCHANT_DIV, merchantIds);

                billProcessHandler.determineBusinessDate(task);
                task.setVersion(task.getVersion() + 1);
                final int rows = billTaskDAO.updateMerchantIdTaskStateAndSyncStateByIdAndVersion(
                        merchantIdToUse, nextState, sapSyncState, blacklineSyncState,
                        ZonedDateTime.now(),task
                );
                Preconditions.checkState(rows == 1, "failed to update the task to success, id = %s", taskId);

                log.info("bill process successful, task id: {}", taskId);
                logTransitLog(traceId, "账单解析完成", shouldSync ? "状态更新为解析完成" : "无需推送SAP & Blackline");

                dispatchProcessDoneTask(taskId);
                return true;
            } finally {
                if (!FileUtils.deleteQuietly(tempFile)) {   // NOSONAR
                    log.warn("failed to cleanup temporary file: {}", tempFile.getAbsolutePath());
                }
                IOUtils.close(in);
            }
        } catch (final RuntimeException | IOException thrown) {
            log.warn("bill process error: {}, task id: {}", thrown.getMessage(), taskId, thrown);
            return onProcessError(task, thrown);
        }
    }

    private boolean onProcessError(final BillTaskEntity task, final Throwable thrown) {
        final boolean shouldRetry = !Throwables.causedBy(thrown, BillCenterConfigurationException.class);
        final int errorCountIncr = shouldRetry ? 1 : billCenterConfig.getMaxRetryTimes() + 1;
        final int errorCount = Optional.ofNullable(task.getErrorCount()).map(c -> c + errorCountIncr).orElse(errorCountIncr);

        final long delay = billCenterConfig.isBackoffRetry() ? (long) Math.pow(2, errorCount) : 0;
        final ZonedDateTime nextRetryTime = ZonedDateTime.now().plusMinutes(delay);
        final String message = Errors.format(Throwables.getRootCause(thrown).getMessage(), 200);

        final Long taskId = task.getId();
        final String traceId = task.getTraceId();
        billTaskDAO.updateToFailedByIdAndVersion(
                TaskState.PROCESS_FAILED, errorCount, message,
                nextRetryTime,
                taskId, task.getVersion() + 1
        );
        logTransitLog(traceId, "账单解析失败", message);

        if (!shouldRetry) {
            return false;
        }
        if (thrown instanceof BillCenterBusinessException bizThrown) {
            throw bizThrown;
        }
        throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED, thrown);
    }

    private void dispatchProcessDoneTask(final Long taskId) {
        log.info("bill process done, task id: {}", taskId);
    }

    private void logTransitLog(final String traceId, final String title, final String message) {
        final TransitLogEntity startAcquireLog = TransitLogEntity.builder()
                .traceId(traceId)
                .title(title)
                .message(Errors.format(message, 200))
                .build();
        EntityUtils.fill(startAcquireLog, ZonedDateTime.now());
        transitLogDAO.create(startAcquireLog);
    }

    BillProcessHandler determineBillProcessHandler(final Channel channel,
                                                   final GrantType grantType, final BillType billType) {
        return billProcessHandlers.stream()
                .filter(handler -> handler.matches(channel, grantType, billType))
                .findFirst()
                .orElse(null);
    }

    private Path createTempFile(final BillType type, final String suffix) throws IOException {
        return FileUtils2.createTempFile(type.name(), "." + suffix);
    }

}