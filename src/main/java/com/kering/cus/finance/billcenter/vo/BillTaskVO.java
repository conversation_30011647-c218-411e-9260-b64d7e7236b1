package com.kering.cus.finance.billcenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName BillTaskVO
 * @Description Value Object for bill task data transfer to frontend
 * @Date 2025-07-24 15:30:00
 * @Version V1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude
@Accessors(chain = true)
public class BillTaskVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8739294376811835163L;

    private Long id;
    private String brand;
    private String channel;
    private String channelName;
    private String merchantId;
    private String grantType;
    private String billType;
    private String billTypeName;
    private String billStartTime;
    private String billEndTime;
    private String state;
    private String extraParams;
    private String source;
    private String taskUniqueKey;
    private String originBillUrl;
    private String sapArchiveUrl;
    private String blacklineArchiveUrl;
    private Integer errorCount;
    private String errorMsg;
    private String nextRunTime;
    private String sapSyncState;
    private String blacklineSyncState;
    private Boolean isTmall;
    private Boolean isSyncToBlackline;
    private Boolean isSyncToSap;
    private String traceId;
    private String note;
    private String finishedDate;
    private String createdDate;
    private String createdBy;
    private String modifiedDate;
    private String modifiedBy;
    private Boolean deleted;
    private String tenantId;
    private Integer version;
    private String statusName;
    private String blacklineArchiveExcelUrl;
    private String sapArchiveExcelUrl;
    private String businessDate;

}
