package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

/**
 * Dictionary.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_dictionary")
public class DictionaryEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("dic_group")
    private String dicGroup;

    @TableField("dic_key")
    private String dicKey;

    @TableField("dic_value")
    private String dicValue;

    @TableField("is_enabled")
    private Boolean isEnabled;

}
