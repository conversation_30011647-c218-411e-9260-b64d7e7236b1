package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * Wosaipay trade flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_wosaipay_trade_flow")
public class WosaipayTradeFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private String channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("merchant_name")
    private String merchantName;

    @TableField("store_name")
    private String storeName;

    @TableField("store_code")
    private String storeCode;

    @TableField("merchant_store_code")
    private String merchantStoreCode;

    @TableField("transaction_date")
    private String transactionDate;

    @TableField("transaction_time")
    private String transactionTime;

    @TableField("merchant_order_id")
    private String merchantOrderId;

    @TableField("merchant_transaction_no")
    private String merchantTransactionNo;

    @TableField("pos_order_id")
    private String posOrderId;

    @TableField("pos_transaction_no")
    private String posTransactionNo;

    @TableField("pos_origin_order_id")
    private String posOriginOrderId;

    @TableField("pos_origin_transaction_no")
    private String posOriginTransactionNo;

    @TableField("wosaypay_order_id")
    private String wosaypayOrderId;

    @TableField("payment_channel")
    private String paymentChannel;

    @TableField("payment_channel_merchant_id")
    private String paymentChannelMerchantId;

    @TableField("payment_channel_order_id")
    private String paymentChannelOrderId;

    @TableField("sku_name")
    private String skuName;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("trade_mode")
    private String tradeMode;

    @TableField("order_source")
    private String orderSource;

    @TableField("order_sence")
    private String orderSence;

    @TableField("trade_status")
    private String tradeStatus;

    @TableField("payment_account")
    private String paymentAccount;

    @TableField("currency")
    private String currency;

    @TableField("transaction_amount")
    private BigDecimal transactionAmount;

    @TableField("merchant_discount_amount")
    private BigDecimal merchantDiscountAmount;

    @TableField("merchant_discount_type")
    private String merchantDiscountType;

    @TableField("subsidy_amount")
    private BigDecimal subsidyAmount;

    @TableField("payment_channel_discount_amount")
    private BigDecimal paymentChannelDiscountAmount;

    @TableField("recharge_discount_amount")
    private BigDecimal rechargeDiscountAmount;

    @TableField("non_recharge_discount_amount")
    private BigDecimal nonRechargeDiscountAmount;

    @TableField("actual_paid_amount")
    private BigDecimal actualPaidAmount;

    @TableField("transaction_rate")
    private BigDecimal transactionRate;

    @TableField("service_charge")
    private BigDecimal serviceCharge;

    @TableField("actual_recv_amount")
    private BigDecimal actualRecvAmount;

    @TableField("installment_num")
    private Integer installmentNum;

    @TableField("installment_fee_rate")
    private BigDecimal installmentFeeRate;

    @TableField("Installment_fee")
    private BigDecimal installmentFee;

    @TableField("split_amount")
    private BigDecimal splitAmount;

    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    @TableField("term_no")
    private String termNo;

    @TableField("merchant_term_no")
    private String merchantTermNo;

    @TableField("term_name")
    private String termName;

    @TableField("term_type")
    private String termType;

    @TableField("device_no")
    private String deviceNo;

    @TableField("operator")
    private String operator;

    @TableField("cashier")
    private String cashier;

    @TableField("note")
    private String note;

    @TableField("ext1")
    private String ext1;

    @TableField("ext2")
    private String ext2;

    @TableField("biz_order_id")
    private String bizOrderId;

    @TableField("biz_note")
    private String bizNote;

    @TableField("deposit")
    private BigDecimal deposit;

    @TableField("outer_origin_transaction_no")
    private String outerOriginTransactionNo;

    @TableField("reserve3")
    private String reserve3;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
