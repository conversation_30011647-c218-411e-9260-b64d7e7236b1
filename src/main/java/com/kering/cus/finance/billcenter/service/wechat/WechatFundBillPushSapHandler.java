package com.kering.cus.finance.billcenter.service.wechat;


import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.SYMBOL_COMMA;
import static com.kering.cus.finance.billcenter.util.CsvParserUtil.appendCsvField;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.WechatFundFlowDAO;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.WechatFundFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractSapBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.SapBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @ClassName WechatFundBillPushSapHandler
 * @Description Fund bill push sap task
 * @Date 2025-07-14 10:10
 * @Version V1.0
 **/
@Service
@Slf4j
public class WechatFundBillPushSapHandler extends AbstractSapBillArchiveHandler implements SapBillArchiveHandler {

  @Resource
  private WechatFundFlowDAO wechatFundFlowDAO;

  private static final byte[] UTF8_BOM = new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};

  private static final String HEADER = "Serial Number,Brand,Channel,Settlement Merchant ID,Profit Center,General Ledger Account,Clearing Account,Accounting Time,WeChat Pay Business Order Number,Capital Flow Order Number,Business Name,Business Type,Revenue and Expense Amount,Account Balance,Applicant for Capital Change Submission,Remarks,Business Voucher Number";


  public WechatFundBillPushSapHandler(SftpConfig sftpConfig) {
    super(Channel.WECHAT, BillType.FUND_FLOW, sftpConfig);
  }

  @Override
  protected String getSapSftpUsername() {
    return sftpConfig.getSapCrushSftpWechatUsername();
  }

  @Override
  protected String getSapSftpPassword() {
    return sftpConfig.getSapCrushSftpWechatPassword();
  }

  @Override
  protected String getSapSftpTargetDirectory() {
    return sftpConfig.getSapCrushSftpWechatArchiveDirectory();
  }

  @Override
  protected String getSapSftpPrivateKey() {
    return sftpConfig.getSapCrushSftpWechatPrivateKey();
  }

  @Override
  protected String getSapSftpPrivateKeyPassphrase() {
    return sftpConfig.getSapCrushSftpWechatPrivateKeyPassphrase();
  }

  @Override
  public Map<String,File> archive(Long taskId, MerchantConfigEntity config) throws IOException {
    Map<String,File> mapFile=new HashMap<>();

    if (config == null || Boolean.FALSE.equals(config.getIsSyncToSap())) {
      log.warn("wechat fund push task {} is not sync to sap", taskId);
      return mapFile;
    }
    long total = wechatFundFlowDAO.selectFundBillCount(taskId);
    if (total == 0) {
      log.warn("WeChat fund bill push no data, taskId={}", taskId);
      return mapFile;
    }
    mapFile.put(Constants.SUFFIX_CSV, writeCsvFile(total,taskId, config));
    mapFile.put(Constants.SUFFIX_XLSX, writeExcelFile(taskId, config));
    return mapFile;
  }

  private File writeCsvFile(long total,Long taskId, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile(Channel.WECHAT.name() + BillType.FUND_FLOW.name(), Constants.SUFFIX_CSV).toFile();
    try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(targetFile), StandardCharsets.UTF_8))) {
      writer.write(new String(UTF8_BOM, StandardCharsets.UTF_8));
      writer.write(HEADER+"\n");
      long pages = (total + BATCH_SIZE - 1) / BATCH_SIZE;
      int serialNumber = 1;

      for (long page = 1; page <= pages; page++) {
        Page<WechatFundFlowEntity> pageRequest = new Page<>(page, BATCH_SIZE);
        List<WechatFundFlowEntity> records = wechatFundFlowDAO.selectFundBillPage(pageRequest, taskId).getRecords();

        for (WechatFundFlowEntity wechatFundFlowEntity : records) {
          writer.write(formatCsvLine(wechatFundFlowEntity, serialNumber++, config));
        }
      }
    } catch (Exception e) {
      log.error("WechatFundBillPushSapHandler.billPushDeal execute fail", e);
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_FUND_GENERATE_FAIL_SAP, e);
    }
    if (!targetFile.exists() || targetFile.length() == 0) {
      throw new IOException("WechatFundBillPushSapHandler CSV file is empty or does not exist");
    }
    return targetFile;
  }


  private File writeExcelFile(Long taskId, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile(Channel.WECHAT.name() + BillType.FUND_FLOW.name(), Constants.SUFFIX_XLSX).toFile();
    AtomicInteger serialNum = new AtomicInteger(1);
    ExcelWriterUtils writer = new ExcelWriterUtils();
    writer.createSheet("Wechat_SAP").writeHeader(HEADER.split(SYMBOL_COMMA));
    int pageNum=1;
    Page<WechatFundFlowEntity> pageData;
    do {
      pageData = wechatFundFlowDAO.selectFundBillPage(new Page<>(pageNum, BATCH_SIZE), taskId);
      List<WechatFundFlowEntity> records = pageData.getRecords();
      if (!CollectionUtils.isEmpty(records)) {
        records.forEach(wechatFundFlowEntity -> {
           String[] content=new String[]{
               String.valueOf(serialNum.getAndIncrement()),
               wechatFundFlowEntity.getBrand(),
               wechatFundFlowEntity.getChannel().toString(),
               wechatFundFlowEntity.getMerchantId(),
               config.getSapProfitCenter(),
               config.getSapGlAccount1(),
               config.getSapGlAccount2(),
               DateUtils.convertDateStr(wechatFundFlowEntity.getBillingDate(), DateUtils.YYYYMMDD),
               wechatFundFlowEntity.getBizOrderId(),
               wechatFundFlowEntity.getTransactionNo(),
               wechatFundFlowEntity.getTransactionName(),
               wechatFundFlowEntity.getTransactionType(),
               wechatFundFlowEntity.getAmount() != null ? wechatFundFlowEntity.getAmount().setScale(2, RoundingMode.HALF_UP).toString() : null,
               wechatFundFlowEntity.getBalance() != null ? wechatFundFlowEntity.getBalance().setScale(2, RoundingMode.HALF_UP).toString() : null,
               wechatFundFlowEntity.getApplyUser(),
               wechatFundFlowEntity.getNote(),
               wechatFundFlowEntity.getBizVoucherNo()
           };
           writer.writeRow(content);
        });
      }
      pageNum++;
    } while (pageData.hasNext());
    writer.writeTo(targetFile);
    return targetFile;
  }


  /**
   * Build CSV line with header mapping
   *
   * @param wechatFundFlowEntity Database record
   * @param serialNumber         Sequence number
   * @param merchantConfig       Merchant configuration
   * @return Formatted CSV line
   */
  String formatCsvLine(WechatFundFlowEntity wechatFundFlowEntity, int serialNumber, MerchantConfigEntity merchantConfig) {
    StringBuilder line = new StringBuilder();
    appendCsvField(line, String.valueOf(serialNumber));
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getBrand());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getChannel().toString());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getMerchantId());
    line.append(",");
    appendCsvField(line, merchantConfig.getSapProfitCenter());
    line.append(",");
    appendCsvField(line, merchantConfig.getSapGlAccount1());
    line.append(",");
    appendCsvField(line, merchantConfig.getSapGlAccount2());
    line.append(",");
    appendCsvField(line, DateUtils.convertDateStr(wechatFundFlowEntity.getBillingDate(), DateUtils.YYYYMMDD));
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getBizOrderId());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getTransactionNo());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getTransactionName());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getTransactionType());
    line.append(",");
    appendCsvField(line,
        wechatFundFlowEntity.getAmount() != null ? wechatFundFlowEntity.getAmount().setScale(2, RoundingMode.HALF_UP).toString() : null);
    line.append(",");
    appendCsvField(line,
        wechatFundFlowEntity.getBalance() != null ? wechatFundFlowEntity.getBalance().setScale(2, RoundingMode.HALF_UP).toString() : null);
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getApplyUser());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getNote());
    line.append(",");
    appendCsvField(line, wechatFundFlowEntity.getBizVoucherNo());
    line.append("\n");
    return line.toString();
  }


}
