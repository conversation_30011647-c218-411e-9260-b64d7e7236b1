package com.kering.cus.finance.billcenter.util;


import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class StringUtils {

    public static final String SYMBOL_EQ = "=";


    private StringUtils() {}
    public static String trimAndRemoveSpecialChars(String str) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(str)) {
            return str;
        }
        String trim = str.trim().replace("\"", "").replace("`", "").replace("\\t", "")
                .replace("\\r", "").replace("\\n", "").replace("\\\\", "")
                .replace("\uFEFF", "").trim();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(trim) && trim.startsWith(SYMBOL_EQ)) {
            trim = trim.replaceFirst(SYMBOL_EQ, "");

        }
        return trim;
    }




    public static Map<String, String> parseAndFormatMap(Map<String, String> map) {
        Map<String, String> headMap = Maps.newHashMap();
        map.forEach((k, v) -> headMap.put(trimAndRemoveSpecialChars(k), trimAndRemoveSpecialChars(v)));
        return headMap;

    }

    public static String bigDecimalToString(BigDecimal value,String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        return value.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

}
