package com.kering.cus.finance.billcenter.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import java.util.List;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Slf4j
public class ExcelWriterUtils {

  private Workbook workbook;
  private Sheet currentSheet;
  private int currentRowIndex = 0;

  public ExcelWriterUtils() {
    this.workbook = new XSSFWorkbook();
  }

  public ExcelWriterUtils createSheet(String sheetName) {
    this.currentSheet = workbook.createSheet(sheetName);
    this.currentRowIndex = 0;
    return this;
  }

  public ExcelWriterUtils writeHeader(String[] headers) {
    return writeRow(headers, null);
  }

  public ExcelWriterUtils writeRow(String[] rowData) {
    return writeRow(rowData, null);
  }

  public ExcelWriterUtils writeRow(String[] rowData, CellStyle style) {
    Row row = currentSheet.createRow(currentRowIndex++);
    for (int i = 0; i < rowData.length; i++) {
      Cell cell = row.createCell(i);
      cell.setCellValue(rowData[i]);
      if (style != null) {
        cell.setCellStyle(style);
      }
    }
    return this;
  }

  public ExcelWriterUtils writeRows(List<String[]> rowsData) {
    for (String[] rowData : rowsData) {
      writeRow(rowData);
    }
    return this;
  }


  public void writeTo(File file) throws IOException {
    try (FileOutputStream outputStream = new FileOutputStream(file)) {
      workbook.write(outputStream);
      outputStream.flush();
    } finally {
      workbook.close();
    }
  }


}
