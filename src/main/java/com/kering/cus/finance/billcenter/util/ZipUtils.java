package com.kering.cus.finance.billcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.compress.utils.IOUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Enumeration;
import java.util.Map;


@Slf4j
public class ZipUtils {


    private ZipUtils() {
    }

    public static void unzip(File zipFile, Path path) throws IOException{
        unzip(zipFile, path, StandardCharsets.UTF_8);
    }

    public static void unzip(File zipFile, Path path, Charset charset) throws IOException {
        try {
            validateParameters(zipFile, path);
            try (ZipFile zip = new ZipFile(zipFile, charset.name())) {
                Enumeration<ZipArchiveEntry> entries = zip.getEntries();

                while (entries.hasMoreElements()) {
                    ZipArchiveEntry entry = entries.nextElement();
                    Path entryPath = path.resolve(entry.getName()).normalize();
                    if (entry.isDirectory()) {
                        Files.createDirectories(entryPath);
                    } else {
                        extractEntry(zip, entry, entryPath);
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException(String.format("unzip error,message: %s,", e.getMessage()), e);
        }
    }

    private static void extractEntry(ZipFile zip, ZipArchiveEntry entry, Path targetPath) throws IOException {
        try (InputStream inputStream = zip.getInputStream(entry);
             OutputStream outputStream = Files.newOutputStream(targetPath)) {
            IOUtils.copy(inputStream, outputStream);
        }
    }


    private static void validateParameters(File zipFile, Path destDir) throws IOException {
        if (zipFile == null || destDir == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }

        if (!zipFile.exists()) {
            throw new FileNotFoundException("ZIP file not found: " + zipFile.getAbsolutePath());
        }

        if (!zipFile.isFile()) {
            throw new IllegalArgumentException("Not a file: " + zipFile.getAbsolutePath());
        }

        if (Files.exists(destDir) && !Files.isDirectory(destDir)) {
            throw new IllegalArgumentException("Destination is not a directory: " + destDir);
        }
    }


    public static boolean isZipFile(File file) {
        try (ZipFile ignored = new ZipFile(file)) {
            log.info("Zip file is valid");
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    public static void zipTo(final Map<String, File> zipEntries, final File zipFile) throws IOException {
        try (final ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(zipFile)) {
            for (final Map.Entry<String, File> entry : zipEntries.entrySet()) {
                final String key = entry.getKey();
                final File value = entry.getValue();
                if (null != value && value.exists()) {
                    zipOut.putArchiveEntry(new ZipArchiveEntry(value, key));
                    zipOut.closeArchiveEntry();
                }
            }
            zipOut.flush();
        }
    }

}
