package com.kering.cus.finance.billcenter.service.support;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * SFTP 账单获取.
 *
 * <AUTHOR>
 * @since 20250709
 */
@RequiredArgsConstructor
public abstract class AbstractSftpAcquireHandler implements BillAcquireHandler {

    protected final SftpConfig sftpConfig;
    protected final Channel channel;
    protected final BillType billType;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final Channel channel, final GrantType grantType,
                           final BillType billType, final String apiVersion) {
        return Objects.equals(channel, this.channel)
                && Objects.equals(grantType, GrantType.SFTP)
                && Objects.equals(billType, this.billType);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String acquireTo(final BillTaskEntity task, final File targetFile) {
        final String sftpPath = task.getExtraParams();
        try {
            acquireTo(sftpPath, targetFile);
        } catch (final IOException e) {
            throw new IllegalStateException(e);
        }
        return FilenameUtils.getExtension(sftpPath);
    }

    protected void acquireTo(final String sftpPath, final File targetFile) throws IOException {
        final String hostname = sftpConfig.getAcquireSftpHostname();
        final int port = sftpConfig.getAcquireSftpPort();
        final String username = sftpConfig.getAcquireSftpUsername();
        final String password = sftpConfig.getAcquireSftpPassword();
        final String privateKeyStr = sftpConfig.getAcquireSftpPrivateKey();
        final String passphraseStr = sftpConfig.getAcquireSftpPrivateKeyPassphrase();
        final int connectTimeout = sftpConfig.getSftpConnectTimeout();
        final byte[] privateKey = !StringUtils.hasText(privateKeyStr) ? null : privateKeyStr.getBytes(StandardCharsets.UTF_8);
        final byte[] passphrase = !StringUtils.hasText(passphraseStr) ? null : passphraseStr.getBytes(StandardCharsets.UTF_8);

        final String sftpAbsPath = toAbstractPath(sftpPath);
        final String backupAbsPath = toAbstractPath(determineAcquiredSftpPath(sftpAbsPath));
        final ChannelSftp sftp = SftpUtils.connect(hostname, port, username, password, privateKey, passphrase, connectTimeout);
        try (final FileOutputStream out = new FileOutputStream(targetFile)) {
            sftp.get(sftpPath, out);

            if (StringUtils.hasText(backupAbsPath)) {
                final String directory = FilenameUtils.getPath(backupAbsPath);
                if (StringUtils.hasText(directory) && !SftpUtils.mkdirs(sftp, directory)) {
                    throw new IOException(String.format("Failed to mkdirs %s", directory));
                }
                sftp.rename(sftpPath, backupAbsPath);
            }
        } catch (final SftpException e) {
            throw new IOException(String.format("Failed to download file from SFTP server: %s", e.getMessage()), e);
        } finally {
            SftpUtils.disconnect(sftp);
        }
    }

    protected abstract String determineAcquiredSftpPath(final String sftpPath);

    private String toAbstractPath(final String path) {
        if (null != path && !path.startsWith(SftpUtils.PATH_SEPARATOR)) {
            return SftpUtils.PATH_SEPARATOR + path;
        }
        return path;
    }

}
