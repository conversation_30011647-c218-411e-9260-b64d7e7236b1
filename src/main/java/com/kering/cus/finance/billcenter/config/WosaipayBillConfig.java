package com.kering.cus.finance.billcenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
public class WosaipayBillConfig {

    /**
     * 交易账单路径中的品牌匹配模式.
     */
    @Value("${wosaipay.trade.path.brand-pattern:_([^/_]+)_\\d+.xlsx$}")
    private String tradePathBrandPattern;

}