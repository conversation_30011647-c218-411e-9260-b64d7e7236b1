package com.kering.cus.finance.billcenter.service;

import java.time.ZonedDateTime;

/**
 * Create a billing task to obtain bills through SFTP.
 *
 * <AUTHOR>
 * @since 20250701
 */
public interface SftpBillTaskCreateService {

    /**
     * Does it match the given sftp path.
     *
     * @param sftpPath the sftp path
     * @return matches
     */
    boolean matches(final String sftpPath);

    /**
     * 使用给定SFTP路径创建账单任务.
     *
     * @param sftpPath the SFTP path
     * @param when     the trigger time
     * @param source   the task source
     * @return the created task id list
     */
    Long createTaskIfNecessary(final String sftpPath, final ZonedDateTime when, final String source);

}
