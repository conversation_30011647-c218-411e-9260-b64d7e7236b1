package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;

import java.io.File;

public interface BillAcquireHandler {

    /**
     * 是否匹配给定渠道+授权类型+账单类型+API版本.
     *
     * @param channel    渠道
     * @param grantType  授权类型
     * @param billType   账单类型
     * @param apiVersion 账单 API 版本
     * @return 是否匹配
     */
    boolean matches(final Channel channel, final GrantType grantType,
                    final BillType billType, final String apiVersion);

    /**
     * 获取给定任务对应的账单文件, 并返回对应文件类型.
     *
     * @param task       任务信息
     * @param targetFile 账单文件写入的文件
     * @return 文件类型(扩展名, eg : zip, csv)
     */
    String acquireTo(final BillTaskEntity task, final File targetFile);

}
