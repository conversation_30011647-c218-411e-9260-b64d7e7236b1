package com.kering.cus.finance.billcenter.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class WebUtils {
    private static final String HTTPS = "HTTPS";
    private static final int HTTP_PORT = 80;
    private static final int HTTPS_PORT = 443;

    /**
     * Get application URL.
     * <p/>
     * For example: http://www.some.host.com/myapp
     *
     * @param request current HTTP request
     * @return the path for web application
     */
    public static String getApplicationUrl(HttpServletRequest request) {
        final String scheme = request.getScheme();
        final String host = request.getServerName();
        final int port = request.getServerPort();
        final String contextPath = request.getContextPath();
        final boolean isHttps = HTTPS.equalsIgnoreCase(scheme);
        final int defPort = isHttps ? HTTPS_PORT : HTTP_PORT;

        return scheme + "://" + host + (port == defPort ? "" : ":" + port) + (null != contextPath && !contextPath.isEmpty() ? contextPath : "");
    }
}
