package com.kering.cus.finance.billcenter.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class SftpConfig {

    @Value("${sftp.connect-timeout:0}")
    private int sftpConnectTimeout;

    @Value("${SFTP_HOST}")
    private String acquireSftpHostname;
    @Value("${SFTP_PORT:22}")
    private int acquireSftpPort;
    @Value("${SFTP_USERNAME}")
    private String acquireSftpUsername;
    @Value("${SFTP_PASSWORD}")
    private String acquireSftpPassword;
    @Value("${SFTP_PRIVATE_KEY:}")
    private String acquireSftpPrivateKey;
    @Value("${SFTP_PASSPHRASE:}")
    private String acquireSftpPrivateKeyPassphrase;

}
