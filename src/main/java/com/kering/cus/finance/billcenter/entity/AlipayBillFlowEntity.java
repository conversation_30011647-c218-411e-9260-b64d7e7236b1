package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * Alipay trade flow.
 * 
 * <AUTHOR>
 * @since ********
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_alipay_bill_flow")
public class AlipayBillFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private String channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("account_transaction_no")
    private String accountTransactionNo;

    @TableField("biz_transaction_no")
    private String bizTransactionNo;

    @TableField("merchant_order_id")
    private String merchantOrderId;

    @TableField("sku_name")
    private String skuName;

    @TableField("transaction_time")
    private String transactionTime;

    @TableField("reciprocal_account")
    private String reciprocalAccount;

    @TableField("income")
    private BigDecimal income;

    @TableField("expense")
    private BigDecimal expense;

    @TableField("balance")
    private BigDecimal balance;

    @TableField("transaction_channel")
    private String transactionChannel;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("note")
    private String note;

    @TableField("biz_desc")
    private String bizDesc;

    @TableField("biz_order_id")
    private String bizOrderId;

    @TableField("biz_base_order_id")
    private String bizBaseOrderId;

    @TableField("biz_bill_source")
    private String bizBillSource;

    @TableField("final_biz_base_order_id")
    private String finalBizBaseOrderId;

    @TableField("is_tmall")
    private Boolean isTmall;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
