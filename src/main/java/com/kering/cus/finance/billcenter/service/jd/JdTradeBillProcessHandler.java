package com.kering.cus.finance.billcenter.service.jd;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.JdBillConverter;
import com.kering.cus.finance.billcenter.dao.JdTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import com.kering.cus.finance.billcenter.util.ZipUtils;
import com.kering.cus.finance.billcenter.vo.JdTradeFlowVO;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;


@Slf4j
@Service
public class JdTradeBillProcessHandler implements BillProcessHandler {

    @Resource
    private JdBillConverter jdBillConverter;

    @Resource
    private JdTradeFlowDAO jdTradeFlowDAO;


    private static final Integer BATCH_SIZE = 100;

    @Override
    public boolean matches(Channel channel, GrantType grantType, BillType billType) {
        return Objects.equals(channel.getName(), Channel.JD.getName()) && Objects.equals(grantType, GrantType.SFTP)
                && Objects.equals(billType, BillType.TRADE_FLOW);
    }


    @Override
    @Transactional
    public List<String> process(File tempFile, BillTaskEntity task) {
        log.info("jd trade bill processing starts,merchantId :{}", task.getMerchantId());
        if (Objects.isNull(tempFile)) {
            log.warn("JD trade billing format is incorrect,merchantId : {}", task.getMerchantId());
            return Collections.emptyList();
        }
        try {
            if (ZipUtils.isZipFile(tempFile)) {
                Path safePath = getSafePath();
                ZipUtils.unzip(tempFile, safePath);

                try (Stream<Path> stream = Files.walk(safePath)) {
                    stream.filter(Files::isRegularFile)
                            .filter(path -> path.getFileName().toString().endsWith(Constants.SUFFIX_CSV))
                            .forEach(path -> parsingBillFile(task, path));
                }
                FileUtils.deleteDirectory(safePath.toFile());
            } else {
                parsingBillFile(task, tempFile.toPath());
            }
            log.info("JD trade bill processing completed,merchantId :{}", task.getMerchantId());
        } catch (Exception e) {
            log.error("JD trade bill processing error", e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED,e);
        }
        return Collections.emptyList();
    }

    @Override
    public void determineBusinessDate(BillTaskEntity task) {
        String dateStr = task.getExtraParams().substring(task.getExtraParams().lastIndexOf("-") + 1, task.getExtraParams().lastIndexOf("."));
        ZonedDateTime zonedDateTime;
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDD);
            LocalDate localDate = LocalDate.parse(dateStr, formatter);
            zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        }catch (Exception e){
            log.error("determineBusinessDate error,data : {}",dateStr);
            zonedDateTime=task.getBillStartTime();
        }
        task.setBusinessDate(zonedDateTime);
    }



    private void parsingBillFile(BillTaskEntity task, Path path) {
        List<JdTradeFlowEntity> jdTradeFlowEntities = Lists.newArrayList();
        Set<String> documentNos = Sets.newHashSet();
        try (CsvReaderUtils reader = CsvReaderUtils.builder().headerLine(0).charset(Charset.forName(Constants.CHARSET_GBK)).build()) {
            reader.open(path);
            reader.forEach(row -> {
                JdTradeFlowVO jdTradeFlowVO = JsonUtil.convertValue(StringUtils.parseAndFormatMap(row), JdTradeFlowVO.class);
                jdTradeFlowVO.setBillingDate(matchBillDate(jdTradeFlowVO.getSettlementNote()));

                if (!documentNos.contains(jdTradeFlowVO.getTransactionNo())) {
                    documentNos.add(jdTradeFlowVO.getTransactionNo());
                    jdTradeFlowEntities.add(jdBillConverter.toJdTradeFlowEntity(task, jdTradeFlowVO));
                }
                if (jdTradeFlowEntities.size() == BATCH_SIZE) {
                    saveJdTradeBillList(jdTradeFlowEntities);
                    jdTradeFlowEntities.clear();
                }
            });
            if (!CollectionUtils.isEmpty(jdTradeFlowEntities)) {
                saveJdTradeBillList(jdTradeFlowEntities);
            }
        } catch (IOException e) {
            log.error("reader jd trade bill file error", e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED,e);
        }
    }


    private void saveJdTradeBillList(List<JdTradeFlowEntity> jdTradeFlowEntities) {
        List<String> transactionNos = jdTradeFlowEntities.stream().map(JdTradeFlowEntity::getTransactionNo).toList();
        List<String> transactionNoList = jdTradeFlowDAO.findByMerchantIdAndTransactionNos(transactionNos, jdTradeFlowEntities.get(0).getMerchantId());
        List<JdTradeFlowEntity> list = jdTradeFlowEntities.stream().filter(e -> !transactionNoList.contains(e.getTransactionNo())).toList();
        if (!CollectionUtils.isEmpty(list)) {
            jdTradeFlowDAO.createBatch(list);
        }
    }

    private String matchBillDate(String settlementNote) {
        if (isIncludeDate(settlementNote)) {
            String[] patterns = {"yyyy年M月d日", "yyyy年MM月d日", "yyyy年M月dd日", "yyyy年MM月dd日"};
            String dateStr = settlementNote.replaceAll("^(\\d{4}年\\d{1,2}月\\d{1,2}日).*$", "$1");
            LocalDate localDate = DateUtils.extractDateEnhanced(dateStr, patterns);
            if (Objects.nonNull(localDate)) {
                return localDate.toString();
            }
        }
        return null;

    }

    private boolean isIncludeDate(String settlementNote) {
        return org.springframework.util.StringUtils.hasText(settlementNote) && settlementNote.contains("年") && settlementNote.contains("月") && settlementNote.contains("日");
    }


    private Path getSafePath() throws IOException {
        Path safeBaseDir = Paths.get(System.getProperty("java.io.tmpdir"), "jdTrade").toAbsolutePath().normalize();
        Path targetPath = safeBaseDir.resolve(UUID.randomUUID().toString()).normalize();
        if (!Files.exists(targetPath)) {
            Files.createDirectories(targetPath);
        }
        return targetPath;
    }




}
