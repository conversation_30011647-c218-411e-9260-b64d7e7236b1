package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.util.BizPreconditions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;

@Lazy
@Service
@Slf4j
@RequiredArgsConstructor
public class SftpBillTaskCreateProxyService {

    private final BillTaskEventPublisher billTaskEventPublisher;
    private final List<SftpBillTaskCreateService> sftpBillTaskCreateServices;

    /**
     * Create a billing task using the SFTP path given TOPIC.
     *
     * @param sftpPath the SFTP path
     * @param when     the trigger time
     * @param source   the task source
     * @return the created task id list
     */
    public boolean createTaskIfNecessary(final String sftpPath,
                                         final ZonedDateTime when, final String source) {
        final SftpBillTaskCreateService service = getRequiredSftpTaskCreateService(sftpPath);
        if (null == service) {
            log.warn("[SKIP] mismatch sftp path file: {}", sftpPath);
            return false;
        }
        final Long taskId = service.createTaskIfNecessary(sftpPath, when, source);
        return null != taskId && dispatchWaitAcquireTask(taskId);
    }

    private boolean dispatchWaitAcquireTask(final Long taskId) {
        log.info("dispatch wait acquire task: {}", taskId);
        billTaskEventPublisher.publishWaitAcquireEvent(taskId);
        return true;
    }

    private SftpBillTaskCreateService getRequiredSftpTaskCreateService(final String sftpPath) {
        return sftpBillTaskCreateServices.stream()
                .filter(service -> service.matches(sftpPath))
                .findFirst()
                .orElse(null);
    }

}