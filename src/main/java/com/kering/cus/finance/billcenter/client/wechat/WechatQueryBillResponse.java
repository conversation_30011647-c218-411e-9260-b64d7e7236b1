package com.kering.cus.finance.billcenter.client.wechat;

import com.google.gson.annotations.SerializedName;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName WechatQueryBillResponse
 * @Description WeChat Platform Bill Acquisition Response Parameters
 * @Date 2025-07-07 18:31
 * @Version V1.0
 **/
@Data
public class WechatQueryBillResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -9057696239441698030L;

    /**
     * [Hash Type] Hash algorithm type, fixed as SHA1.
     */
    @SerializedName("hash_type")
    private HashType hashType;

    /**
     * [Hash Value] SHA1 digest value of the bill file for merchant-side consistency verification.
     */
    @SerializedName("hash_value")
    private String hashValue;

    /**
     * [Download URL] Download address for requesting bill files, valid within 5 minutes.
     */
    @SerializedName("download_url")
    private String downloadUrl;

    /**
     * Hash algorithm types
     */
    public enum HashType {
        @SerializedName("SHA1")
        SHA1; // SHA1 algorithm
    }
}
