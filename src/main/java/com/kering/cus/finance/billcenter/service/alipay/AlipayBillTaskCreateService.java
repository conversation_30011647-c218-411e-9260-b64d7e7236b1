package com.kering.cus.finance.billcenter.service.alipay;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.service.support.AbstractApiBillTaskCreateService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 支付宝账单任务创建.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Service
public class AlipayBillTaskCreateService extends AbstractApiBillTaskCreateService {

    public AlipayBillTaskCreateService(final BillTaskDAO billTaskDAO,
                                       final TransitLogDAO transitLogDAO,
                                       final MerchantConfigDAO merchantConfigDAO,
                                       final MerchantGrantConfigDAO merchantGrantConfigDAO) {
        super(Channel.ALIPAY, billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected List<BillType> getSupportedBillTypes() {
        return Collections.singletonList(BillType.TRADE_FLOW);
    }

}