package com.kering.cus.finance.billcenter.constant;

public enum WechatAccountType {
  BASIC("BASIC"),
  OPERATION("OPERATION"),
  FEES("FEES");

  private final String value;

  WechatAccountType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public static WechatAccountType fromValue(String value) {
    for (WechatAccountType type : WechatAccountType.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid WeChat fund account type: " + value);
  }
}
