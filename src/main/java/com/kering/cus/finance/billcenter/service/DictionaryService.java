package com.kering.cus.finance.billcenter.service;

import static com.kering.cus.finance.billcenter.constant.Constants.DICTIONARY_BILL_TASK_STATUS;
import static com.kering.cus.finance.billcenter.constant.Constants.DICTIONARY_BILL_TYPE;

import com.kering.cus.finance.billcenter.dao.DictionaryDAO;
import com.kering.cus.finance.billcenter.vo.DictionaryVO;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName DictionaryService
 * @Description Data dictionary service class for handling business operations related to data dictionary entries.
 * @Date 2025年07月23日 17:43
 * @Version V1.0
 **/
@Service
@Slf4j
public class DictionaryService {

  @Resource
  private DictionaryDAO dictionaryDAO;

  public List<DictionaryVO> getDictionaryData(String dicGroup) {
    if (StringUtils.isBlank(dicGroup)) {
      throw new IllegalArgumentException("Dictionary group must not be empty");
    }
    if (!isValidGroup(dicGroup)) {
      throw new IllegalArgumentException("Invalid dictionary group: " + dicGroup);
    }

    // Query dictionary entities by group
    var entities = dictionaryDAO.findByDicGroup(dicGroup);

    // Convert entities to VO list
    return entities.stream()
        .map(entity -> new DictionaryVO()
            .setDicGroup(dicGroup)
            .setDicKey(entity.getDicKey())
            .setDicValue(entity.getDicValue())
            .setIsEnabled(entity.getIsEnabled()))
        .toList();
  }

  private boolean isValidGroup(String group) {
    return group.startsWith("bill.") && (
        "bill.brands".equals(group) ||
            "bill.channels".equals(group) ||
            DICTIONARY_BILL_TASK_STATUS.equals(group) ||
            DICTIONARY_BILL_TYPE.equals(group)
    );
  }
}
