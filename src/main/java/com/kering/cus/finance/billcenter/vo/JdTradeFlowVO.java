package com.kering.cus.finance.billcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kering.cus.finance.billcenter.config.serializer.BigDecimalDeserializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class JdTradeFlowVO  {
    

    @JsonProperty(value="订单编号")
    private String orderId;

    @JsonProperty(value="单据编号")
    private String transactionNo;
    @JsonProperty(value="单据类型")
    private String transactionType;
    @JsonProperty(value="商品编号")
    private String skuCode;

    @JsonProperty(value="商户订单号")
    private String merchantOrderId;

    @JsonProperty(value="商品名称")
    private String skuName;

    @JsonProperty(value="结算状态")
    private String settlementStatus;

    @JsonProperty(value="费用发生时间")
    private String occurTime;

    @JsonProperty(value="费用计费时间")
    private String chargeableTime;

    @JsonProperty(value="费用结算时间")
    private String settlementTime;

    @JsonProperty(value="费用项")
    private String feeItem;

    @JsonProperty(value="金额")
    @JsonDeserialize(using =BigDecimalDeserializer.class)
    private BigDecimal amount;

    @JsonProperty(value="币种")
    private String currency;

    @JsonProperty(value="商家应收/应付")
    private String merchantPaymentType;

    @JsonProperty(value="钱包结算备注")
    private String settlementNote;

    @JsonProperty(value="店铺号")
    private String shopCode;

    @JsonProperty(value="京东门店编号")
    private String jdStoreCode;

    @JsonProperty(value="品牌门店编号")
    private String brandStoreCode;

    @JsonProperty(value="门店名称")
    private String storeName;

    @JsonProperty(value="备注")
    private String note;

    @JsonProperty(value="收支方向")
    private String paymentType;

    @JsonProperty(value="商品数量")
    private Integer skuQty;


    private String billingDate;



}
