package com.kering.cus.finance.billcenter.service.alipay;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractBlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.CsvWriterUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.DEFAULT_VALUE;

@Slf4j
@Service
public class AlipayBlacklineBillArchiveHandler extends AbstractBlacklineBillArchiveHandler implements BlacklineBillArchiveHandler {

  @Resource
  private AlipayBillFlowDAO alipayBillFlowDAO;

  public AlipayBlacklineBillArchiveHandler(SftpConfig sftpConfig) {
    super(Channel.ALIPAY, sftpConfig);
  }

  @Override
  public boolean matches(Channel channel) {
    return Objects.equals(channel, Channel.ALIPAY);
  }

  @Override
  public boolean isGroupingByTmall() {
    return true;
  }


  @Override
  public Map<String, File> archive(Group group, long seqInGroup, List<Long> taskIds) throws IOException {
    log.info("Archive Alipay bill for group: {}, seq: {}, taskIds: {}", group, seqInGroup, taskIds);
    boolean isTmall = Objects.equals(group, Group.TMALL);
    Map<String, File> fileMap = new HashMap<>();
    List<String> fileTypes = Lists.newArrayList(Constants.SUFFIX_XLSX, Constants.SUFFIX_CSV);
    for (String fileType : fileTypes) {
      File targetFile = FileUtils2.createTempFile(String.format("alipay_blackline_%s", group.name()), fileType).toFile();
      try {
        boolean hasData;
        if (Objects.equals(fileType, Constants.SUFFIX_CSV)) {
          hasData = createCsvFile(targetFile, taskIds, isTmall);
        } else {
          hasData = createExcelFile(targetFile, taskIds, isTmall);
        }
        if (!hasData) {
          log.warn("no alipay bill,taskIds ：{} ", taskIds);
          FileUtils.deleteQuietly(targetFile);
        } else {
          fileMap.put(fileType, targetFile);
        }
      } catch (Exception e) {
        log.error("Archive Alipay bill failed,taskIds ：{} ", taskIds, e);
        FileUtils.deleteQuietly(targetFile);
        throw new BillCenterBusinessException(BillCenterErrorCode.BILL_BLACKLINE_ARCHIVE_FAILED, e);
      }
    }

    return fileMap;
  }

  private boolean createCsvFile(File targetFile, List<Long> taskIds, boolean isTmall) throws IOException {
    boolean hasData = false;
    int pageNum = 1;
    Page<AlipayBillFlowEntity> batch;
    try (CsvWriterUtils writer = new CsvWriterUtils(targetFile)) {
      writer.writeHeader(getHeaders());
      do {
        batch = alipayBillFlowDAO.findAlipayBillByParams(taskIds, isTmall, BATCH_SIZE, pageNum);
        List<AlipayBillFlowEntity> records = batch.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
          List<String[]> list = records.stream().map(this::getLines).toList();
          writer.writeBatch(list);
          hasData = true;
        }
        pageNum++;
      } while (batch.hasNext());
    }
    return hasData;
  }

  private boolean createExcelFile(File targetFile, List<Long> taskIds, boolean isTmall) throws IOException {
    boolean hasData = false;
    int pageNum = 1;
    Page<AlipayBillFlowEntity> batch;
    ExcelWriterUtils writer = new ExcelWriterUtils();
    writer.createSheet("Alipay_Blackline").writeHeader(getHeaders());
    do {
      batch = alipayBillFlowDAO.findAlipayBillByParams(taskIds, isTmall, BATCH_SIZE, pageNum);
      List<AlipayBillFlowEntity> records = batch.getRecords();
      if (!CollectionUtils.isEmpty(records)) {
        List<String[]> list = records.stream().map(this::getLines).toList();
        writer.writeRows(list);
        hasData = true;
      }
      pageNum++;
    } while (batch.hasNext());
    writer.writeTo(targetFile);
    return hasData;
  }

  private String[] getLines(AlipayBillFlowEntity alipayBillFlowEntity) {
    return new String[]{
        StringUtils.defaultString(alipayBillFlowEntity.getBrand()),
        StringUtils.defaultString(alipayBillFlowEntity.getChannel()),
        StringUtils.defaultString(alipayBillFlowEntity.getMerchantId()),
        StringUtils.defaultString(alipayBillFlowEntity.getAccountTransactionNo()),
        StringUtils.defaultString(alipayBillFlowEntity.getBizTransactionNo()),
        StringUtils.defaultString(alipayBillFlowEntity.getMerchantOrderId()),
        StringUtils.defaultString(alipayBillFlowEntity.getSkuName()),
        DateUtils.convertDateStr(alipayBillFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD),
        StringUtils.defaultString(alipayBillFlowEntity.getReciprocalAccount()),
        com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getIncome(), DEFAULT_VALUE),
        com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getExpense(), DEFAULT_VALUE),
        com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getBalance(), DEFAULT_VALUE),
        StringUtils.defaultString(alipayBillFlowEntity.getTransactionChannel()),
        StringUtils.defaultString(alipayBillFlowEntity.getTransactionType()),
        StringUtils.defaultString(alipayBillFlowEntity.getNote()),
        StringUtils.defaultString(alipayBillFlowEntity.getBizDesc()),
        StringUtils.defaultString(alipayBillFlowEntity.getBizOrderId()),
        StringUtils.defaultString(alipayBillFlowEntity.getBizBaseOrderId()),
        StringUtils.defaultString(alipayBillFlowEntity.getBizBillSource())};

  }

  private String[] getHeaders() {
    return new String[]{"Brand", "Channel", "Settlement Merchant ID", "Accounting Serial Number", "Business Serial Number", "Merchant Order Number",
        "Product Name", "Occurrence Time", "Counterparty Account", "Debit Amount", "Credit Amount", "Account Balance",
        "Transaction Channel", "Business Type",
        "Remarks", "Business Description", "Business Order Number", "Basic Business Order Number", "Source of Business Bill"};
  }

  @Override
  protected String getBlacklineSftpTargetDirectory() {
    return sftpConfig.getBlacklineSftpAlipayArchiveDirectory();
  }
}
