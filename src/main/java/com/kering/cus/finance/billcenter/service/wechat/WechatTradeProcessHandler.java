package com.kering.cus.finance.billcenter.service.wechat;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.exception.BillCenterErrorCode.WECHAT_TRADEBILL_DATA_DUPLICATED;
import static com.kering.cus.finance.billcenter.util.CsvParserUtil.isSummaryRow;

import com.alibaba.schedulerx.shade.org.apache.commons.collections.CollectionUtils;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.WechatTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WechatTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.CsvParserUtil;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @ClassName WechatTradeProcessHandler
 * @Description WeChat transaction bill processor - implements .gz file processing logic
 * @Date 2025-07-09 11:01
 * @Version V1.0
 **/
@Slf4j
@Service
public class WechatTradeProcessHandler implements BillProcessHandler {

  @Resource
  private WechatTradeFlowDAO wechatTradeFlowDAO;

  private static final String TOTAL_LINE_FLAG = "总交易单数";
  private static final String DEFAULT_AMOUNT = "0.00";

  /**
   * Determines if the processing conditions are met
   *
   * @param channel   Channel
   * @param grantType Authorization type
   * @param billType  Bill type
   * @return Whether conditions are met
   */
  @Override
  public boolean matches(Channel channel, GrantType grantType, BillType billType) {
    return Channel.WECHAT == channel && GrantType.MERCHANT == grantType && BillType.TRADE_FLOW == billType;
  }

  /**
   * Process WeChat transaction bill file (only supports .gz format)
   *
   * @param tempFile       Temporary file object
   * @param billTaskEntity Bill task entity
   * @return Processing result
   * @throws RuntimeException When an error occurs during processing
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public List<String> process(java.io.File tempFile, BillTaskEntity billTaskEntity) {
    if (tempFile == null || !tempFile.exists() || billTaskEntity == null) {
      log.warn("Invalid parameters for processing WeChat trade file");
      return Lists.newArrayList();
    }
    if (tempFile.length() == 0) {
      return Lists.newArrayList(billTaskEntity.getMerchantId());
    }
    List<WechatTradeFlowEntity> batchList = new ArrayList<>(BATCH_SIZE);
    try (InputStream fis = new FileInputStream(tempFile);
        GZIPInputStream gis = new GZIPInputStream(fis);
        BufferedReader reader = new BufferedReader(
            new InputStreamReader(gis, StandardCharsets.UTF_8))) {
      dealCsvFileToDb(billTaskEntity, batchList, reader);
      return Lists.newArrayList(billTaskEntity.getMerchantId());
    } catch (IOException e) {
      log.error("Error processing WeChat trade file: {}", tempFile.getName(), e);
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_TRADEBILL_PROCESS_FAILED);
    }
  }


  @Override
  public void determineBusinessDate(BillTaskEntity task) {
    task.setBusinessDate(task.getBillStartTime());
  }
  /**
   * Process CSV file data and import into database with batch insertion
   *
   * @param billTaskEntity Bill task entity containing metadata for this import
   * @param batchList      List to accumulate records for batch insertion
   * @param reader         BufferedReader providing CSV content
   * @throws IOException if error occurs during file reading
   */
  void dealCsvFileToDb(BillTaskEntity billTaskEntity, List<WechatTradeFlowEntity> batchList, BufferedReader reader) throws IOException {
    String line;
    int recordCount = 0;
    List<String> headerMap = new ArrayList<>();
    boolean processingData = true;

    while ((line = reader.readLine()) != null && processingData) {
      List<String> rowData = CsvParserUtil.parseCsvLine(line);

      // 处理标题行逻辑
      if (recordCount == 0) {
        headerMap.addAll(rowData);
      } else {
        // 检查是否为汇总行
        boolean isSummary = isSummaryRow(rowData, headerMap, "交易时间", TOTAL_LINE_FLAG);

        // 处理数据行
        if (!isSummary) {
          dealFileData(billTaskEntity, batchList, headerMap, rowData);

          // 批量插入逻辑
          if (batchList.size() >= BATCH_SIZE) {
            wechatTradeFlowDAO.createBatch(batchList);
            batchList.clear();
          }
        } else {
          processingData = false; // 标记退出循环
        }
      }
      recordCount++; // 统一计数
    }

    if (CollectionUtils.isNotEmpty(batchList)) {
      wechatTradeFlowDAO.createBatch(batchList);
    }
  }

  private void dealFileData(BillTaskEntity billTaskEntity, List<WechatTradeFlowEntity> batchList, List<String> headerMap, List<String> rowData) {
    WechatTradeFlowEntity entity = buildEntity(rowData, billTaskEntity, headerMap);
    if (wechatTradeFlowDAO.checkBusinessUnique(entity) == null) {
      batchList.add(entity);
    } else {
      log.error("WeChat transaction bill data is duplicated, param = {}", JsonUtil.convertToString(entity));
    }
  }

  /**
   * Build WeChat transaction bill entity
   *
   * @param data           Row data
   * @param billTaskEntity Bill task entity
   * @param headerMap      Header field mapping
   * @return WeChat transaction bill entity
   */
  WechatTradeFlowEntity buildEntity(List<String> data, BillTaskEntity billTaskEntity, List<String> headerMap) {
    WechatTradeFlowEntity entity = new WechatTradeFlowEntity();
    entity.setTaskId(billTaskEntity.getId());
    entity.setBrand(billTaskEntity.getBrand());
    entity.setChannel(billTaskEntity.getChannel());
    entity.setMerchantId(billTaskEntity.getMerchantId());
    entity.setIsSyncToBlackline(billTaskEntity.getIsSyncToBlackline());
    entity.setIsSyncToSap(billTaskEntity.getIsSyncToSap());

    // Use utility class to get column values
    entity.setTransactionTime(CsvParserUtil.safeGetString(data, headerMap, "交易时间"));
    entity.setAppId(CsvParserUtil.safeGetString(data, headerMap, "公众账号ID"));
    entity.setMainMerchant(CsvParserUtil.safeGetString(data, headerMap, "商户号"));
    entity.setSubMerchant(CsvParserUtil.safeGetString(data, headerMap, "特约商户号"));
    entity.setDeviceNo(CsvParserUtil.safeGetString(data, headerMap, "设备号"));
    entity.setWechatOrderId(CsvParserUtil.safeGetString(data, headerMap, "微信订单号"));
    entity.setMerchantOrderId(CsvParserUtil.safeGetString(data, headerMap, "商户订单号"));
    entity.setOpenId(CsvParserUtil.safeGetString(data, headerMap, "用户标识"));
    entity.setTransactionType(CsvParserUtil.safeGetString(data, headerMap, "交易类型"));
    entity.setTradeStatus(CsvParserUtil.safeGetString(data, headerMap, "交易状态"));
    entity.setPaymentBank(CsvParserUtil.safeGetString(data, headerMap, "付款银行"));
    entity.setCurrency(CsvParserUtil.safeGetString(data, headerMap, "货币种类"));
    entity.setSettlementAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "应结订单金额", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setVoucherAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "代金券金额", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setWechatRefundId(CsvParserUtil.safeGetString(data, headerMap, "微信退款单号"));
    entity.setMerchantRefundId(CsvParserUtil.safeGetString(data, headerMap, "商户退款单号"));
    entity.setRefundAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "退款金额", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setRechargeRefundAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "充值券退款金额", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setRefundType(CsvParserUtil.safeGetString(data, headerMap, "退款类型"));
    entity.setRefundStatus(CsvParserUtil.safeGetString(data, headerMap, "退款状态"));
    entity.setSkuName(CsvParserUtil.safeGetString(data, headerMap, "商品名称"));
    entity.setMerchantData(CsvParserUtil.safeGetString(data, headerMap, "商户数据包"));
    entity.setServiceCharge(CsvParserUtil.safeParseBigDecimal(data, headerMap, "手续费", new BigDecimal(DEFAULT_AMOUNT)).negate());
    entity.setTransactionRate(CsvParserUtil.safeGetString(data, headerMap, "费率"));
    entity.setOrderAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "订单金额", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setApplyRefundAmount(CsvParserUtil.safeParseBigDecimal(data, headerMap, "申请退款金额", new BigDecimal(DEFAULT_AMOUNT)).negate());
    entity.setTransactionRateNote(CsvParserUtil.safeGetString(data, headerMap, "费率备注"));

    // Date processing
    entity.setCreatedDate(ZonedDateTime.now());
    entity.setModifiedDate(ZonedDateTime.now());

    return entity;
  }
}
