package com.kering.cus.finance.billcenter.service.alipay;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractSapBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.SapBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.CsvWriterUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.collections4.ListUtils;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.DEFAULT_VALUE;

@Service
@Slf4j
public class AlipaySapBillArchiveHandler extends AbstractSapBillArchiveHandler implements SapBillArchiveHandler {

  @Resource
  private BillTaskDAO billTaskDAO;
  @Resource
  private AlipayBillFlowDAO alipayBillFlowDAO;


  public AlipaySapBillArchiveHandler(SftpConfig sftpConfig) {
    super(Channel.ALIPAY, BillType.TRADE_FLOW, sftpConfig);
  }

  @Override
  public boolean matches(Channel channel, BillType billType) {
    return Objects.equals(channel, Channel.ALIPAY) && Objects.equals(billType, BillType.TRADE_FLOW);

  }

  @Override
  public Map<String,File> archive(Long taskId, MerchantConfigEntity config) throws IOException {

    if (Objects.isNull(config.getIsSyncToSap()) || Boolean.FALSE.equals(config.getIsSyncToSap())) {
      log.warn("alipay task {} is not sync to sap", taskId);
      return Collections.emptyMap();
    }
    BillTaskEntity task = billTaskDAO.findById(taskId).orElse(null);
    Assert.notNull(task, String.format("task not found,taskId: %s", taskId));

    List<AlipayBillFlowEntity> alipayList=alipayBillFlowDAO.findAlipayBillByParams(task.getMerchantId(),taskId,task.getBrand());
    if(CollectionUtils.isEmpty(alipayList)){
      log.warn("alipay task {} is empty", taskId);
      return Collections.emptyMap();
    }
    Map<String, File> mapFile = new HashMap<>();
    try {
      mapFile.put(Constants.SUFFIX_CSV, createAlipayCsvFile(alipayList, config));
      mapFile.put(Constants.SUFFIX_XLSX, createAlipayExcelFile(alipayList, config));
    } catch (Exception e) {
      log.error("create alipay  file failed", e);
      throw new BillCenterBusinessException(BillCenterErrorCode.BILL_SAP_ARCHIVE_FAILED, e);
    }
    return mapFile;
  }

  private File createAlipayCsvFile(List<AlipayBillFlowEntity> alipayList, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile("alipay", Constants.SUFFIX_CSV).toFile();
    AtomicInteger serialNum = new AtomicInteger(1);
    try (CsvWriterUtils writer = new CsvWriterUtils(targetFile)) {
      writer.writeHeader(getHeaders());
      for (List<AlipayBillFlowEntity> batch : ListUtils.partition(alipayList, BATCH_SIZE)) {
        List<String[]> contentList = getContentList(batch, config, serialNum);
        writer.writeBatch(contentList);
      }
    }
    return targetFile;
  }

  private File createAlipayExcelFile(List<AlipayBillFlowEntity> alipayList, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile("alipay", Constants.SUFFIX_XLSX).toFile();
    AtomicInteger serialNum = new AtomicInteger(1);
    ExcelWriterUtils writer = new ExcelWriterUtils();
    writer.createSheet("Alipay_SAP").writeHeader(getHeaders());
    ListUtils.partition(alipayList, BATCH_SIZE).forEach(batch -> {
      List<String[]> contentList = getContentList(batch, config, serialNum);
      writer.writeRows(contentList);
    });
    writer.writeTo(targetFile);
    return targetFile;
  }


  private List<String[]> getContentList(List<AlipayBillFlowEntity> alipayList, MerchantConfigEntity config, AtomicInteger serialNum) {
    List<String[]> list = Lists.newArrayList();
    for (AlipayBillFlowEntity alipayBillFlowEntity : alipayList) {
      String[] lines = new String[]{
          String.valueOf(serialNum.getAndIncrement()),
          StringUtils.defaultString(alipayBillFlowEntity.getBrand()),
          StringUtils.defaultString(alipayBillFlowEntity.getChannel()),
          StringUtils.defaultString(alipayBillFlowEntity.getMerchantId()),
          StringUtils.defaultString(config.getSapProfitCenter()),
          StringUtils.defaultString(config.getSapGlAccount1()),
          StringUtils.defaultString(config.getSapGlAccount2()),
          StringUtils.defaultString(alipayBillFlowEntity.getAccountTransactionNo()),
          StringUtils.defaultString(alipayBillFlowEntity.getBizTransactionNo()),
          StringUtils.defaultString(alipayBillFlowEntity.getMerchantOrderId()),
          StringUtils.defaultString(alipayBillFlowEntity.getSkuName()),
          DateUtils.convertDateStr(alipayBillFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD),
          StringUtils.defaultString(alipayBillFlowEntity.getReciprocalAccount()),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getIncome(), DEFAULT_VALUE),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getExpense(), DEFAULT_VALUE),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(alipayBillFlowEntity.getBalance(), DEFAULT_VALUE),
          StringUtils.defaultString(alipayBillFlowEntity.getTransactionChannel()),
          StringUtils.defaultString(alipayBillFlowEntity.getTransactionType()),
          StringUtils.defaultString(alipayBillFlowEntity.getNote()),
          StringUtils.defaultString(alipayBillFlowEntity.getBizDesc()),
          StringUtils.defaultString(alipayBillFlowEntity.getBizOrderId()),
          StringUtils.defaultString(alipayBillFlowEntity.getBizBaseOrderId()),
          StringUtils.defaultString(alipayBillFlowEntity.getBizBillSource())};
      list.add(lines);
    }
    return list;
  }

  @Override
  protected String getSapSftpUsername() {
    return sftpConfig.getSapCrushSftpAlipayUsername();
  }


  @Override
  protected String getSapSftpPassword() {
    return sftpConfig.getSapCrushSftpAlipayPassword();
  }

  @Override
  protected String getSapSftpPrivateKey() {
    return sftpConfig.getSapCrushSftpAlipayPrivateKey();
  }

  @Override
  protected String getSapSftpPrivateKeyPassphrase() {
    return sftpConfig.getSapCrushSftpAlipayPrivateKeyPassphrase();
  }

  @Override
  protected String getSapSftpTargetDirectory() {
    return sftpConfig.getSapCrushSftpAlipayArchiveDirectory();
  }


  private String[] getHeaders() {
    return new String[]{"Serial Number","Brand","Channel","Settlement Merchant ID","Profit Center",
        "General Ledger Account","Clearing Account","Accounting Serial Number","Business Serial Number","Merchant Order Number",
        "Product Name","Occurrence Time","Counterparty Account","Debit Amount","Credit Amount ","Account Balance ","Transaction Channel",
        "Business Type","Remarks","Business Description",
        "Business Order Number","Basic Business Order Number","Source of Business Bill"};
  }


}
