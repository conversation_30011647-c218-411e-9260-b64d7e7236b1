package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.mapper.WosaipayTradeFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class WosaipayTradeFlowDAO extends MyBatisBaseDAO<WosaipayTradeFlowEntity, WosaipayTradeFlowMapper, Long> {

    public WosaipayTradeFlowEntity getBillDetailByParams(WosaipayTradeFlowEntity entity) {
        LambdaQueryWrapper<WosaipayTradeFlowEntity> query = Wrappers.<WosaipayTradeFlowEntity>lambdaQuery()
                .eq(WosaipayTradeFlowEntity::getPosTransactionNo, entity.getPosTransactionNo())
                .eq(WosaipayTradeFlowEntity::getMerchantId, entity.getMerchantId())
                .eq(WosaipayTradeFlowEntity::getDeleted,false)
                .eq(WosaipayTradeFlowEntity::getTransactionType, entity.getTransactionType())
                .eq(WosaipayTradeFlowEntity::getPaymentChannelOrderId, entity.getPaymentChannelOrderId())
                .eq(WosaipayTradeFlowEntity::getPaymentAccount, entity.getPaymentAccount());
        return entityMapper.selectOne(query);
    }




    public Page<WosaipayTradeFlowEntity> findWosaipayBillByTaskIds(List<Long> taskIds,int pageSize,int pageNum){
        final LambdaQueryWrapper<WosaipayTradeFlowEntity> query = Wrappers.<WosaipayTradeFlowEntity>lambdaQuery()
                .in(WosaipayTradeFlowEntity::getTaskId,taskIds)
                .eq(WosaipayTradeFlowEntity::getDeleted, false)
                .eq(WosaipayTradeFlowEntity::getIsSyncToBlackline,true);
        return entityMapper.selectPage(new Page<>(pageNum, pageSize), query);
    }


}

