package com.kering.cus.finance.billcenter.exception;

import lombok.Getter;

@Getter
public enum BillCenterErrorCode {
    CHANNEL_NOT_FOUND("channel_not_found", "channel not found"),
    MERCHANT_NOT_FOUND("merchant_not_found", "merchant not found"),
    TASK_NOT_FOUND("task_not_found", "task not found"),
    CHANNEL_CAN_NOT_DETERMINED("channel_can_not_determined", "channel can not determined"),
    BILL_TYPE_CAN_NOT_DETERMINED("bill_type_can_not_determined", "bill type can not determined"),
    MERCHANT_CAN_NOT_DETERMINED("merchant_can_not_determined", "merchant can not determined"),
    BILL_ACQUIRE_FAILED("bill_acquire_failed", "bill acquire failed"),
    BILL_PROCESS_FAILED("bill_process_failed", "bill process failed"),
    BILL_SAP_ARCHIVE_FAILED("bill_sap_archive_failed", "bill sap archive failed"),
    BILL_BLACKLINE_ARCHIVE_FAILED("bill_blackline_archive_failed", "bill blackline archive failed"),
    WECHAT_BILL_ACQUIRE_FAILED("WECHAT_BILL_ACQUIRE_FAILED", "Wechat bill acquire failed"),
    WECHAT_V3BILL_ACQUIRE_FAILED("WECHAT_V3BILL_ACQUIRE_FAILED", "Wechat v3bill acquire failed"),
    WECHAT_BILL_KMS_ACQUIRE_FAILED("WECHAT_BILL_KMS_ACQUIRE_FAILED",
        "WeChat Pay Platform V2 could not find merchant authorization information. Please check KMS WeChat configuration"),
    WECHAT_V3BILL_KMS_ACQUIRE_FAILED("WECHAT_V3BILL_KMS_ACQUIRE_FAILED",
        "WeChat Payment Platform V3 could not find merchant authorization information. Please check KMS WeChat configuration"),
    WECHAT_TRADEBILL_PROCESS_FAILED("WECHAT_TRADEBILL_PROCESS_FAILED", "Failed to process WeChat trade file"),
    WECHAT_FUNDBILL_PROCESS_FAILED("WECHAT_FUNDBILL_PROCESS_FAILED", "Failed to process WeChat fund file"),
    WECHAT_TRADEBILL_DATA_DUPLICATED("WECHAT_TRADEBILL_DATA_DUPLICATED", "WeChat transaction bill data is duplicated"),
    WECHAT_FUNDBILL_DATA_DUPLICATED("WECHAT_FUNDBILL_DATA_DUPLICATED", "WeChat fund bill data is duplicated"),
    WECHAT_FUND_GENERATE_FAIL_SAP("WECHAT_FUND_GENERATE_FAIL_SAP", "wechat fund sap generate failed"),
    WECHAT_TRADE_GENERATE_FAIL_BLACKLINE("WECHAT_TRADE_GENERATE_FAIL_BLACKLINE", "wechat trade blackLine generate failed"),
    DICTIONARY_NOT_FOUND("dictionary_not_found", "dictionary not found"),
    ;


    /**
     * error code
     */
    private final String code;
    /**
     * error message
     */
    private final String message;

    BillCenterErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
