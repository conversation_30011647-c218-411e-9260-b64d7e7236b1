package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.client.oauth2.AuthorizationInfo;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.service.OauthGrantService;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import freework.io.IOUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping
public class OauthController {
    private static final String OAUTH2_CONNECT_PATH = "connect/";
    private static final String OAUTH2_CALLBACK_PATH = "callback/";
    private static final String OAUTH2_CALLBACK_PATH_SUFFIX = "-oauth2-notify";
    private static final String OAUTH2_CALLBACK_SUCCESS_HTML = "META-INF/support/web/oauth2_success.html";
    private static final String OAUTH2_CALLBACK_MERCHANT_PLACEHOLDER = "${merchantId}";

    @Autowired
    private BillCenterConfig billCenterConfig;
    @Autowired
    private OauthGrantService oauthGrantService;

    /**
     * Redirect to oauth2 connector.
     */
    @Operation(summary = "redirect to oauth2 connector")
    @Probe(event = "redirectToOauth2Connector", group = "controller", retVal = true, error = true)
    @GetMapping({OAUTH2_CONNECT_PATH + "{channel}"})
    public ResponseEntity<Void> redirectToAuthorizeUri(@PathVariable("channel") @ProbeDimension(name = "channel") String channel) {
        return doRedirectToAuthorizeUri(channel);
    }

    private <T> ResponseEntity<T> doRedirectToAuthorizeUri(final String channel) {
        final String redirectUri = determineCallbackUri(channel);
        final String authorizeUrl = StringUtils.hasText(redirectUri)
                ? oauthGrantService.getAuthorizeUrl(getChannel(channel), redirectUri)
                : null;
        if (StringUtils.hasText(authorizeUrl)) {
            return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT).header(HttpHeaders.LOCATION, authorizeUrl).build();
        }
        return ResponseEntity.badRequest().build();
    }

    private String determineCallbackUri(final String channel) {
        if (Channel.ALIPAY.name().equalsIgnoreCase(channel)) {
            return billCenterConfig.getAlipayCallbackUrl();
        }
        return null;
    }

    /**
     * Oauth2 authorized callback.
     *
     * @param channel        oauth connector
     * @param callbackParams callback params
     * @return HTTP response
     */
    @Operation(summary = "oauth2 callback")
    @Probe(event = "oauth2 callback", group = "controller", retVal = true, error = true)
    @GetMapping(OAUTH2_CALLBACK_PATH + "{channel}" + OAUTH2_CALLBACK_PATH_SUFFIX)
    public ResponseEntity<String> oauth2Callback(@PathVariable("channel") @ProbeDimension(name = "channel") String channel,
                                                 @RequestParam @ProbeDimension(name = "callbackParams") Map<String, String> callbackParams) throws IOException {
        try {
            final AuthorizationInfo authorizationInfo = oauthGrantService.doAuthorizeCallback(getChannel(channel), callbackParams);
            if (null != authorizationInfo && null != authorizationInfo.getMerchantId()) {
                final InputStream in = getClass().getClassLoader().getResourceAsStream(OAUTH2_CALLBACK_SUCCESS_HTML);
                if (null != in) {
                    final String html = IOUtils.toString(in, StandardCharsets.UTF_8, true);
                    return ResponseEntity.ok()
                            .contentType(MediaType.TEXT_HTML)
                            .body(html.replace(OAUTH2_CALLBACK_MERCHANT_PLACEHOLDER, authorizationInfo.getMerchantId()));
                }
                return ResponseEntity.ok("OK");
            }
        } catch (final RuntimeException ex) {
            log.warn("Oauth callback error: {}", ex.getMessage());
            return doRedirectToAuthorizeUri(channel);
        }
        return doRedirectToAuthorizeUri(channel);
    }

    /**
     * Refresh Oauth2 access token.
     *
     * @return HTTP response
     */
    @Operation(summary = "oauth2 refresh all access token")
    @Probe(event = "oauth2 refresh all access token", group = "controller")
    @GetMapping("api/oauth2/access-token/refresh")
    public ResponseEntity<Void> refreshAllOauth2Token() {
        oauthGrantService.refreshAccessTokenIfNecessary();
        return ResponseEntity.ok().build();
    }

    /**
     * Refresh Oauth2 access token.
     *
     * @param id oauth access token id
     * @return HTTP response
     */
    @Operation(summary = "oauth2 refresh spec token")
    @Probe(event = "oauth2 refresh spec token", group = "controller")
    @GetMapping("api/oauth2/access-token/{id}/refresh")
    public ResponseEntity<Void> refreshOauth2Token(@PathVariable("id") @ProbeDimension(name = "id") Long id) {
        oauthGrantService.refreshAccessTokenIfNecessary(id);
        return ResponseEntity.ok().build();
    }

    private Channel getChannel(String channelStr) {
        return Channel.valueOf(channelStr.toUpperCase());
    }
}
