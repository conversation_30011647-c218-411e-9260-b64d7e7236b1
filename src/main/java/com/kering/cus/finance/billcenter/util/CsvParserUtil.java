package com.kering.cus.finance.billcenter.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * CSV parsing utility class
 */
@Slf4j
public class CsvParserUtil {

  private CsvParserUtil() {
  }

  /**
   * Parse CSV line data, handle quoted fields
   *
   * @param line Raw CSV line data
   * @return List of parsed string values
   */
  public static List<String> parseCsvLine(String line) {
    if (StringUtils.isBlank(line)) {
      return new ArrayList<>();
    }
    List<String> result = new ArrayList<>();
    StringBuilder fieldBuilder = new StringBuilder();
    boolean inQuotes = false;
    final char[] chars = line.toCharArray();
    int i = 0;
    int length = chars.length;

    while (i < length) {
      char c = chars[i];

      if (c == '"') {
        if (inQuotes && i + 1 < length && chars[i + 1] == '"') {
          fieldBuilder.append('"');
          i += 2;
          continue;
        }
        inQuotes = !inQuotes;
      } else if (c == ',' && !inQuotes) {
        result.add(fieldBuilder.toString());
        fieldBuilder.setLength(0);
      } else {
        fieldBuilder.append(c);
      }
      i++;
    }
    result.add(fieldBuilder.toString().trim());
    return result.stream()
        .map(com.kering.cus.finance.billcenter.util.StringUtils::trimAndRemoveSpecialChars)
        .toList();
  }

  /**
   * Safely retrieves a string value from CSV data
   *
   * @param data       Data list
   * @param headerMap  Header mapping
   * @param columnName Column name
   * @return String value of corresponding column, returns null if not exists
   */
  public static String safeGetString(List<String> data, List<String> headerMap, String columnName) {
    int index = headerMap.indexOf(columnName);
    if (index >= 0 && index < data.size()) {
      return com.kering.cus.finance.billcenter.util.StringUtils.trimAndRemoveSpecialChars(data.get(index));
    }
    return null;
  }

  /**
   * Safely parses BigDecimal value from CSV data
   *
   * @param data         Data list
   * @param headerMap    Header mapping
   * @param columnName   Column name
   * @param defaultValue Default value
   * @return Parsed BigDecimal value
   */
  public static BigDecimal safeParseBigDecimal(List<String> data, List<String> headerMap, String columnName, BigDecimal defaultValue) {
    int index = headerMap.indexOf(columnName);
    if (index >= 0 && index < data.size()) {
      String value = data.get(index);
      try {
        // Remove thousand separators and replace different number formats
        String cleanedValue = value.replaceAll("[,\\s\\u00A0]", "").replace("元", "");
        return BigDecimal.valueOf(Double.parseDouble(cleanedValue))
            .setScale(2, RoundingMode.HALF_UP);
      } catch (NumberFormatException e) {
        // Use System.out to avoid circular dependency
        log.error("Invalid number format: " + value, e);
      }
    }
    return defaultValue;
  }

  /**
   * Determines if the current row is a summary row Check if headerMap contains summary indicator field
   */
  public static boolean isSummaryRow(List<String> rowData, List<String> headerMap, String columnName, String totalLineFlag) {
    String firstValue = CsvParserUtil.safeGetString(rowData, headerMap, columnName);
    return StringUtils.isNotBlank(firstValue) && (totalLineFlag.equals(firstValue) || totalLineFlag.equalsIgnoreCase(
        CsvParserUtil.safeGetString(rowData, headerMap, headerMap.get(0))));
  }

  /**
   * Special character processing for CSV field values
   *
   * @param line  StringBuilder for CSV line
   * @param value Field value to process
   */
  public static void appendCsvField(StringBuilder line, String value) {
    if (value == null) {
      return;
    }
    boolean needsQuotes = value.contains(",") || value.contains("\"") || value.contains("\n");
    if (needsQuotes) {
      line.append("\"");
      line.append(value.replace("\"", "\"\""));
      line.append("\"");
    } else {
      line.append(value);
    }
  }
}
