package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.mapper.MerchantGrantConfigMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class MerchantGrantConfigDAO extends MyBatisBaseDAO<MerchantGrantConfigEntity, MerchantGrantConfigMapper, Long> {

    public MerchantGrantConfigEntity findByChannelAndMerchantId(final Channel channel,
                                                                final String merchantId) {
        final LambdaQueryWrapper<MerchantGrantConfigEntity> query = new LambdaQueryWrapper<MerchantGrantConfigEntity>()
                .eq(MerchantGrantConfigEntity::getChannel, channel)
                .eq(MerchantGrantConfigEntity::getDeleted, false)
                .eq(MerchantGrantConfigEntity::getMerchantId, merchantId);
        return entityMapper.selectOne(query);
    }

    public MerchantGrantConfigEntity findEnabledMerchantGrantConfig(final Channel channel,
                                                                    final List<GrantType> types,
                                                                    final String merchantId) {
        final LambdaQueryWrapper<MerchantGrantConfigEntity> query = new LambdaQueryWrapper<MerchantGrantConfigEntity>()
                .eq(MerchantGrantConfigEntity::getChannel, channel)
                .eq(MerchantGrantConfigEntity::getDeleted, false)
                .eq(MerchantGrantConfigEntity::getMerchantId, merchantId)
                .in(MerchantGrantConfigEntity::getGrantType, types)
                .eq(MerchantGrantConfigEntity::getIsEnabled, true);
        return entityMapper.selectOne(query);
    }

    public List<MerchantGrantConfigEntity> findEnabledMerchantGrantConfigs(final Channel channel,
                                                                           final List<GrantType> types) {
        final LambdaQueryWrapper<MerchantGrantConfigEntity> query = new LambdaQueryWrapper<MerchantGrantConfigEntity>()
                .eq(MerchantGrantConfigEntity::getChannel, channel)
                .eq(MerchantGrantConfigEntity::getDeleted, false)
                .in(MerchantGrantConfigEntity::getGrantType, types)
                .eq(MerchantGrantConfigEntity::getIsEnabled, true);
        return entityMapper.selectList(query);
    }

}

