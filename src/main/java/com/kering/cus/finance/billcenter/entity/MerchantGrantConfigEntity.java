package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

/**
 * Merchant grant configuration.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_merchant_grant_config")
public class MerchantGrantConfigEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("channel")
    private Channel channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("grant_type")
    private GrantType grantType;

    @TableField("api_version")
    private String apiVersion;

    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * brand id for KMS secret.
     */
    @TableField("kms_brand_id")
    private String kmsBrandId;

}
