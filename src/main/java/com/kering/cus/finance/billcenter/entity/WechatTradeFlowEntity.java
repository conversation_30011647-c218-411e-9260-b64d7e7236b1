package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * Wechat trade flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_wechat_trade_flow")
public class WechatTradeFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private Channel channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("transaction_time")
    private String transactionTime;

    @TableField("app_id")
    private String appId;

    @TableField("main_merchant")
    private String mainMerchant;

    @TableField("sub_merchant")
    private String subMerchant;

    @TableField("device_no")
    private String deviceNo;

    @TableField("wechat_order_id")
    private String wechatOrderId;

    @TableField("merchant_order_id")
    private String merchantOrderId;

    @TableField("open_id")
    private String openId;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("trade_status")
    private String tradeStatus;

    @TableField("payment_bank")
    private String paymentBank;

    @TableField("currency")
    private String currency;

    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    @TableField("voucher_amount")
    private BigDecimal voucherAmount;

    @TableField("wechat_refund_id")
    private String wechatRefundId;

    @TableField("merchant_refund_id")
    private String merchantRefundId;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField("recharge_refund_amount")
    private BigDecimal rechargeRefundAmount;

    @TableField("refund_type")
    private String refundType;

    @TableField("refund_status")
    private String refundStatus;

    @TableField("sku_name")
    private String skuName;

    @TableField("merchant_data")
    private String merchantData;

    @TableField("service_charge")
    private BigDecimal serviceCharge;

    @TableField("transaction_rate")
    private String transactionRate;

    @TableField("order_amount")
    private BigDecimal orderAmount;

    @TableField("apply_refund_amount")
    private BigDecimal applyRefundAmount;

    @TableField("transaction_rate_note")
    private String transactionRateNote;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
