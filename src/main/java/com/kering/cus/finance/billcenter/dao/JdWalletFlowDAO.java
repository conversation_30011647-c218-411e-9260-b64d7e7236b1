package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.mapper.JdWalletFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class JdWalletFlowDAO extends MyBatisBaseDAO<JdWalletFlowEntity, JdWalletFlowMapper, Long> {

    public JdWalletFlowEntity findJdWalletBillDetails(JdWalletFlowEntity entity){
        final LambdaQueryWrapper<JdWalletFlowEntity> query = Wrappers.<JdWalletFlowEntity>lambdaQuery()
                .eq(JdWalletFlowEntity::getMerchantId,entity.getMerchantId())
                .eq(JdWalletFlowEntity::getTransactionTime,entity.getTransactionTime())
                .eq(JdWalletFlowEntity::getMerchantOrderId,entity.getMerchantOrderId())
                .eq(JdWalletFlowEntity::getIncome,entity.getIncome())
                .eq(JdWalletFlowEntity::getExpense,entity.getExpense())
                .eq(JdWalletFlowEntity::getTransactionNote,entity.getTransactionNote())
                .eq(JdWalletFlowEntity::getDeleted,false);
        return entityMapper.selectOne(query);
    }


    public List<JdWalletFlowEntity> findJdWalletBillByParams(String merchantId, Long taskId,String brand){
        final LambdaQueryWrapper<JdWalletFlowEntity> query = Wrappers.<JdWalletFlowEntity>lambdaQuery()
                .eq(JdWalletFlowEntity::getMerchantId,merchantId)
                .eq(JdWalletFlowEntity::getTaskId,taskId)
                .eq(JdWalletFlowEntity::getDeleted, false)
                .eq(JdWalletFlowEntity::getBrand,brand)
                .eq(JdWalletFlowEntity::getIsSyncToSap,true);
        return entityMapper.selectList(query);
    }



    public Page<JdWalletFlowEntity> findJdWalletBillPageByParams(List<Long> taskIds,String remark, int pageSize, int pageNum){
        final LambdaQueryWrapper<JdWalletFlowEntity> query = Wrappers.<JdWalletFlowEntity>lambdaQuery()
                .in(JdWalletFlowEntity::getTaskId,taskIds)
                .eq(JdWalletFlowEntity::getDeleted, false)
                .eq(JdWalletFlowEntity::getIsSyncToBlackline,true)
                .like(JdWalletFlowEntity::getTransactionNote,remark);
        return entityMapper.selectPage(new Page<>(pageNum, pageSize), query);
    }

}

