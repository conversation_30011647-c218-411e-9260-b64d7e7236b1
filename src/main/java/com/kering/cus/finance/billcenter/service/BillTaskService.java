package com.kering.cus.finance.billcenter.service;

import static com.kering.cus.finance.billcenter.constant.Constants.DICTIONARY_BILL_TASK_STATUS;
import static com.kering.cus.finance.billcenter.constant.Constants.DICTIONARY_BILL_TYPE;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.DictionaryDAO;
import com.kering.cus.finance.billcenter.dto.BillTaskQueryDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.DictionaryEntity;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.vo.BillTaskVO;
import jakarta.annotation.Resource;
import java.io.Serial;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class BillTaskService {

  @Resource
  private BillTaskDAO billTaskDAO;
  @Resource
  private DictionaryDAO dictionaryDAO;
  @Resource
  private AppStorageService appStorageService;


    @Transactional
    public void updateSyncBlacklineReadyTaskToRunning(final List<Long> taskIds) {
        final int updated = billTaskDAO.updateBlacklineSyncStateByIdAndSyncStatus(
                SyncState.RUNNING, taskIds, Arrays.asList(SyncState.WAITING, SyncState.FAILED)
        );
        Preconditions.checkState(updated == taskIds.size(), "The tasks status has been changed");
    }

  @Transactional
  public void updateTaskStateBySapAndBlacklineSyncState(final ZonedDateTime since, final ZonedDateTime until) {
    /*-
     * SAP failed, Blackline any -> sync_failed
     * SAP any, Blackline failed -> sync_failed
     * SAP skip, Blackline skip -> sync_skip
     * SAP skip, Blackline success, skip -> sync_success
     * SAP success, skip, Blackline success skip -> sync_success
     * SAP success Blackline success -> sync_success
     */

final List<TaskState> ignoreTaskStatus = Arrays.asList(TaskState.SYNC_SKIP, TaskState.SYNC_FINISHED);
        billTaskDAO.updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(
                TaskState.SYNC_FAILED, null,
                Collections.singletonList(SyncState.FAILED),
                Arrays.asList(SyncState.values()),
                since, until, ignoreTaskStatus
        );
        billTaskDAO.updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(
                TaskState.SYNC_FAILED, null,
                Arrays.asList(SyncState.values()),
                Collections.singletonList(SyncState.FAILED),
                since, until, ignoreTaskStatus
        );
        billTaskDAO.updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(
                TaskState.SYNC_FINISHED, ZonedDateTime.now(),
                Arrays.asList(SyncState.SKIP, SyncState.SUCCESS),
                Arrays.asList(SyncState.SKIP, SyncState.SUCCESS),
                since, until, ignoreTaskStatus
        );
        billTaskDAO.updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(
                TaskState.SYNC_SKIP, null,
                Collections.singletonList(SyncState.SKIP),
                Collections.singletonList(SyncState.SKIP),
                since, until, Collections.singletonList(TaskState.SYNC_SKIP)
        );
    }

  /**
   * Query bill tasks with pagination and filtering
   *
   * @param queryDTO Query parameters for pagination and filtering
   * @return Page<BillTaskVO> Paged result containing converted VO objects
   */
  public Page<BillTaskVO> getBillTasksWithPagination(BillTaskQueryDTO queryDTO) {
    queryDTO.validate();

    Page<BillTaskEntity> page = new Page<>(queryDTO.getPage(), Math.min(queryDTO.getLimit(), 200));

    Page<BillTaskEntity> entityPage = billTaskDAO.queryTasks(page, queryDTO);

    return convertToVOPage(entityPage);
  }

  /**
   * Convert Page of entities to Page of VO objects
   *
   * @param entityPage Page of entities to convert
   * @return Page<BillTaskVO> Converted VO page
   */
  Page<BillTaskVO> convertToVOPage(Page<BillTaskEntity> entityPage) {
    List<BillTaskVO> voList = entityPage.getRecords().stream().map(this::convertToVO).toList();

    return new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal()) {
      @Serial
      private static final long serialVersionUID = 6120903467271727811L;

      @Override
      public List<BillTaskVO> getRecords() {
        return voList;
      }
    };
  }

  /**
   * Convert entity to VO object
   *
   * @param entity Entity to convert
   * @return BillTaskVO Converted VO object
   */
  public BillTaskVO convertToVO(BillTaskEntity entity) {
    if (entity == null) {
      return null;
    }

    String state = entity.getState() != null ? entity.getState().name() : "";
    String billType = entity.getBillType() != null ? entity.getBillType().name() : "";

    return new BillTaskVO()
        .setId(entity.getId())
        .setBrand(entity.getBrand())
        .setChannel(entity.getChannel() != null ? entity.getChannel().name() : "")
        .setChannelName(entity.getChannel() != null ? entity.getChannel().getName() : "")
        .setMerchantId(entity.getMerchantId())
        .setBillType(billType)
        .setBillTypeName(getDictionaryName(billType, DICTIONARY_BILL_TYPE))
        .setState(state)
        .setStatusName(getDictionaryName(state, DICTIONARY_BILL_TASK_STATUS))
        .setCreatedDate(entity.getCreatedDate() != null ? DateUtils.formatDateTime(entity.getCreatedDate(), DateUtils.YYYY_MM_DD_HH_MM_SS) : null)
        .setFinishedDate(entity.getFinishedDate() != null ? DateUtils.formatDateTime(entity.getFinishedDate(), DateUtils.YYYY_MM_DD_HH_MM_SS) : null)
        .setOriginBillUrl(entity.getOriginBillUrl())
        .setSapArchiveUrl(entity.getSapArchiveUrl())
        .setBlacklineArchiveUrl(entity.getBlacklineArchiveUrl())
        .setErrorCount(entity.getErrorCount())
        .setErrorMsg(entity.getErrorMsg())
        .setNextRunTime(entity.getNextRunTime() != null ? DateUtils.formatDateTime(entity.getNextRunTime(), DateUtils.YYYY_MM_DD_HH_MM_SS) : null)
        .setSapSyncState(entity.getSapSyncState() != null ? entity.getSapSyncState().name() : "")
        .setBlacklineSyncState(entity.getBlacklineSyncState() != null ? entity.getBlacklineSyncState().name() : "")
        .setIsTmall(entity.getIsTmall())
        .setIsSyncToBlackline(entity.getIsSyncToBlackline())
        .setIsSyncToSap(entity.getIsSyncToSap())
        .setTraceId(entity.getTraceId())
        .setNote(entity.getNote())
        .setCreatedBy(entity.getCreatedBy())
        .setModifiedDate(entity.getModifiedDate() != null ? DateUtils.formatDateTime(entity.getModifiedDate(), DateUtils.YYYY_MM_DD_HH_MM_SS) : null)
        .setModifiedBy(entity.getModifiedBy())
        .setDeleted(entity.getDeleted())
        .setTenantId(entity.getTenantId())
        .setBlacklineArchiveExcelUrl(entity.getBlacklineArchiveExcelUrl())
        .setSapArchiveExcelUrl(entity.getSapArchiveExcelUrl())
        .setBusinessDate(entity.getBusinessDate() != null ? DateUtils.formatDateTime(entity.getBusinessDate(), DateUtils.YYYY_MM_DD) : null);
  }

  private String getDictionaryName(String state, String dictionaryBillTaskStatus) {
    String dictionaryName = "";

    if (state != null && !state.isEmpty()) {
      DictionaryEntity dictionaryEntity = dictionaryDAO.findNameByGroupAndKey(dictionaryBillTaskStatus, state);
      if (dictionaryEntity != null) {
        dictionaryName = dictionaryEntity.getDicValue();
      }
    }
    return dictionaryName;
  }

  /**
   * Get bill file stream from OSS
   *
   * @param ossUrl OSS file URL
   * @return InputStream file stream
   */
  public InputStreamResource getBillFileStream(String ossUrl) {
    InputStreamResource inputStreamResource = null;
    try {
      inputStreamResource = new InputStreamResource(appStorageService.readStream(ossUrl));
    } catch (Exception e) {
      log.error("Failed to get file stream: {}", ossUrl, e);
    }
    return inputStreamResource;
  }
}
