package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.WechatTradeFlowEntity;
import com.kering.cus.finance.billcenter.mapper.WechatTradeFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @ClassName WechatTradeFlowDAO
 * @Description Data Access Object for WeChat transaction flow records
 * @Date <unknown>
 * @Version V1.0
 **/
@Repository
public class WechatTradeFlowDAO extends MyBatisBaseDAO<WechatTradeFlowEntity, WechatTradeFlowMapper, Long> {

  /**
   * Checks business uniqueness for WeChat transaction records Uniqueness criteria: Merchant ID + Transaction Time + WeChat Order ID + WeChat Refund
   * ID + Merchant Order ID + Trade Status
   *
   * @param entity Entity to check uniqueness for
   * @return Existing entity if duplicate found, null otherwise
   */
  public WechatTradeFlowEntity checkBusinessUnique(WechatTradeFlowEntity entity) {
    LambdaQueryWrapper<WechatTradeFlowEntity> query = new LambdaQueryWrapper<WechatTradeFlowEntity>()
        .eq(WechatTradeFlowEntity::getMerchantId, entity.getMerchantId())
        .eq(WechatTradeFlowEntity::getTransactionTime, entity.getTransactionTime())
        .eq(WechatTradeFlowEntity::getWechatOrderId, entity.getWechatOrderId())
        .eq(WechatTradeFlowEntity::getWechatRefundId, entity.getWechatRefundId())
        .eq(WechatTradeFlowEntity::getMerchantOrderId, entity.getMerchantOrderId())
        .eq(WechatTradeFlowEntity::getTradeStatus, entity.getTradeStatus());
    query.last("limit 1");
    return entityMapper.selectOne(query);
  }

  /**
   * Query count of transaction bill records matching the task criteria
   *
   * @param taskIds Bill task ids
   * @return Number of matching records
   */
  public long selectTradeBillCount(List<Long> taskIds) {
    return entityMapper.selectCount(new LambdaQueryWrapper<WechatTradeFlowEntity>()
        .in(WechatTradeFlowEntity::getTaskId, taskIds));
  }

  /**
   * Query paginated transaction bill records matching the task criteria
   *
   * @param pageRequest Pagination parameters
   * @param taskIds     Bill task ids
   * @return Page of matching transaction flow entities
   */
  public Page<WechatTradeFlowEntity> selectTradeBillPage(Page<WechatTradeFlowEntity> pageRequest, List<Long> taskIds) {
    return entityMapper.selectPage(pageRequest, new LambdaQueryWrapper<WechatTradeFlowEntity>()
        .in(WechatTradeFlowEntity::getTaskId, taskIds));
  }
}
