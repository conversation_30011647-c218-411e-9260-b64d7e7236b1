package com.kering.cus.finance.billcenter.service.jd;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.JdBillConverter;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.vo.JdWalletFlowVO;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.kering.cus.finance.billcenter.constant.Constants.JD_WALLET_FLOW_TYPE;
import static com.kering.cus.finance.billcenter.constant.Constants.NOTE;
import static com.kering.cus.finance.billcenter.constant.Constants.ORDER_NO_NOTE;


@Slf4j
@Service
public class JdWalletBillProcessHandler implements BillProcessHandler {

  @Resource
  private JdBillConverter jdBillConverter;

  @Resource
  private JdWalletFlowDAO jdWalletFlowDAO;


  private static final Integer BATCH_SIZE = 100;

  @Override
  public boolean matches(Channel channel, GrantType grantType, BillType billType) {
    return Objects.equals(channel.getName(), Channel.JD.getName()) && Objects.equals(grantType, GrantType.SFTP)
        && Objects.equals(billType, BillType.FUND_FLOW);
  }

  @Override
  @Transactional
  public List<String> process(File tempFile, BillTaskEntity task) {
    log.info("jd wallet bill processing starts,merchantId :{}", task.getMerchantId());
    if (Objects.isNull(tempFile) || !tempFile.getName().endsWith(".csv")) {
      log.warn("JD Wallet billing format is incorrect,merchantId : {}", task.getMerchantId());
      return Collections.emptyList();
    }

    parsingBillFile(task, tempFile);
    log.info("JD Wallet bill processing completed,merchantId :{}", task.getMerchantId());
    return Collections.emptyList();
  }


  @Override
  public void determineBusinessDate(BillTaskEntity task) {
    String dateStr = task.getExtraParams().substring(task.getExtraParams().lastIndexOf("-") + 1, task.getExtraParams().lastIndexOf("."));
    ZonedDateTime zonedDateTime;
    try {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDD);
      LocalDate localDate = LocalDate.parse(dateStr, formatter);
      zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
    }catch (Exception e){
      log.error("determineBusinessDate error,data : {}",dateStr);
      zonedDateTime=task.getBillStartTime();
    }
    task.setBusinessDate(zonedDateTime);
  }

  private void parsingBillFile(BillTaskEntity task, File file) {
    List<JdWalletFlowEntity> jdWalletFlowEntityList = Lists.newArrayList();
    Set<String> uniqueKeys = Sets.newHashSet();
    try (CsvReaderUtils reader = CsvReaderUtils.builder().headerLine(0).build()) {
      reader.open(file.toPath());
      reader.forEach(row -> {
        JdWalletFlowVO jdWalletFlowVO = JsonUtil.convertValue(com.kering.cus.finance.billcenter.util.StringUtils.parseAndFormatMap(row),
            JdWalletFlowVO.class);
        jdWalletFlowVO.setPlatformOrderId(getPlatformOrderIdByNote(jdWalletFlowVO.getTransactionNote()));

        JdWalletFlowEntity entity = jdBillConverter.toJdWalletFlowEntity(task, jdWalletFlowVO);
        entity.setIsSyncToBlackline(getSyncToBlackline(entity.getTransactionNote(), task.getIsSyncToBlackline()));
        JdWalletFlowEntity jdWalletBillDetails = jdWalletFlowDAO.findJdWalletBillDetails(entity);

        String uniqueKey = getUniqueKey(entity);

        if (!uniqueKeys.contains(uniqueKey) && Objects.isNull(jdWalletBillDetails)) {
          jdWalletFlowEntityList.add(entity);
          uniqueKeys.add(uniqueKey);
        }
        if (jdWalletFlowEntityList.size() == BATCH_SIZE) {
          jdWalletFlowDAO.createBatch(jdWalletFlowEntityList);
          jdWalletFlowEntityList.clear();
        }
      });
      if (!CollectionUtils.isEmpty(jdWalletFlowEntityList)) {
        jdWalletFlowDAO.createBatch(jdWalletFlowEntityList);

      }
    } catch (IOException e) {
      log.error("reader jd wallet bill file error", e);
      throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED, e);
    }
  }


  private String getPlatformOrderIdByNote(String transactionNote) {
    if (StringUtils.hasText(transactionNote)
        && transactionNote.contains(NOTE)) {
      List<String> notes = Arrays.asList(transactionNote.split(Constants.SYMBOL_COMMA_CN));
      String orderNoNote = notes.stream().filter(item -> item.contains(ORDER_NO_NOTE)).findFirst().orElse(null);
      if (StringUtils.hasText(orderNoNote)) {
        Pattern pattern = Pattern.compile(ORDER_NO_NOTE + "(\\d+)");
        Matcher matcher = pattern.matcher(orderNoNote);
        if (matcher.find()) {
          return matcher.group(1);
        } else {
          log.warn("getPlatformOrderIdByNote is null ,remark is {}", transactionNote);
        }
      }
    }
    return null;

  }


  private String getUniqueKey(JdWalletFlowEntity entity) {
    StringJoiner joiner = new StringJoiner("_");
    joiner.add(entity.getMerchantId())
        .add(entity.getTransactionTime())
        .add(entity.getMerchantOrderId())
        .add(String.valueOf(entity.getIncome()))
        .add(String.valueOf(entity.getExpense()))
        .add(entity.getTransactionNote());
    return joiner.toString();
  }


  private boolean getSyncToBlackline(String transactionNote, boolean isSyncToBlackline) {
    return StringUtils.hasText(transactionNote)
        && transactionNote.contains(JD_WALLET_FLOW_TYPE) && isSyncToBlackline;
  }
}
