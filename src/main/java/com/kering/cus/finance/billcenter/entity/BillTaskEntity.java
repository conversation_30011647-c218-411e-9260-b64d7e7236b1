package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.*;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.time.ZonedDateTime;

/**
 * Bill task.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_bill_task")
public class BillTaskEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private Channel channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("grant_type")
    private GrantType grantType;

    @TableField("bill_type")
    private BillType billType;

    @TableField("bill_start_time")
    private ZonedDateTime billStartTime;

    @TableField("bill_end_time")
    private ZonedDateTime billEndTime;

    @TableField("extra_params")
    private String extraParams;

    @TableField("state")
    private TaskState state;

    @TableField("source")
    private String source;

    @TableField("task_unique_key")
    private String taskUniqueKey;

    @TableField("origin_bill_url")
    private String originBillUrl;

    @TableField("sap_archive_url")
    private String sapArchiveUrl;

    @TableField("blackline_archive_url")
    private String blacklineArchiveUrl;

    @TableField("sap_archive_excel_url")
    private String sapArchiveExcelUrl;

    @TableField("blackline_archive_excel_url")
    private String blacklineArchiveExcelUrl;


    @TableField("error_count")
    private Integer errorCount;

    @TableField("error_msg")
    private String errorMsg;

    @TableField("next_run_time")
    private ZonedDateTime nextRunTime;

    @TableField("sap_sync_state")
    private SyncState sapSyncState;
    @TableField("sap_error_count")
    private Integer sapErrorCount;
    @TableField("sap_error_msg")
    private String sapErrorMsg;
    @TableField("sap_next_run_time")
    private ZonedDateTime sapNextRunTime;

    @TableField("blackline_sync_state")
    private SyncState blacklineSyncState;
    @TableField("blackline_error_count")
    private Integer blacklineErrorCount;
    @TableField("blackline_error_msg")
    private String blacklineErrorMsg;
    @TableField("blackline_next_run_time")
    private ZonedDateTime blacklineNextRunTime;

    @TableField("trace_id")
    private String traceId;

    @TableField("is_tmall")
    private Boolean isTmall;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

    @TableField("note")
    private String note;

    @TableField("finished_date")
    private ZonedDateTime finishedDate;


    @TableField("business_date")
    private ZonedDateTime businessDate;


}
