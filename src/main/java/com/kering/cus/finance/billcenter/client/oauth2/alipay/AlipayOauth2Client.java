package com.kering.cus.finance.billcenter.client.oauth2.alipay;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayOpenAuthTokenAppModel;
import com.alipay.api.request.AlipayOpenAuthTokenAppRequest;
import com.alipay.api.response.AlipayOpenAuthTokenAppResponse;
import com.google.common.base.Preconditions;
import com.kering.cus.finance.billcenter.client.oauth2.AuthorizationInfo;
import com.kering.cus.finance.billcenter.client.oauth2.Oauth2Client;
import com.kering.cus.finance.billcenter.util.Errors;
import freework.net.Http;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * Alipay Oauth2 client.
 *
 * <AUTHOR>
 * @see <a href="https://opendocs.alipay.com/isv/04h3ue?pathHash=0fec5099">第三方应用授权</a>
 * @since 20250702
 */
@Slf4j
public class AlipayOauth2Client implements Oauth2Client {

    public static final String SPECIFIED_APP_AUTHORIZE_URL_PROD = "https://b.alipay.com/page/message/tasksDetail";
    public static final String SINGLE_APP_AUTHORIZE_URL_PROD = "https://openauth.alipay.com/oauth2/appToAppAuth.htm";
    public static final String APP_TYPE_MOBILE_APP = "MOBILEAPP";
    public static final String APP_TYPE_WEB_APP = "WEBAPP";
    public static final String APP_TYPE_PUBLIC_APP = "PUBLICAPP";
    public static final String APP_TYPE_TINY_APP = "TINYAPP";
    public static final String APP_TYPE_BASE_APP = "BASEAPP";

    private static final String PLATFORM_CODE_FIXED = "O";
    private static final String TASK_TYPE_FIXED = "INTERFACE_AUTH";

    private static final String PARAM_APP_ID = "app_id";
    private static final String PARAM_REDIRECT_URI = "redirect_uri";
    private static final String PARAM_STATE = "state";

    private static final String PARAM_BIZ_DATA = "bizData";

    private static final String PROP_PLATFORM_CODE = "platformCode";
    private static final String PROP_TASK_TYPE = "taskType";
    private static final String PROP_AGENT_OP_PARAM = "agentOpParam";

    private static final String PROP_ISV_APP_ID = "isvAppId";
    private static final String PROP_APP_TYPES = "appTypes";
    private static final String PROP_REDIRECT_URI = "redirectUri";
    private static final String PROP_STATE = "state";

    private static final String GATEWAY_URL = "https://openapi.alipay.com/gateway.do";
    private static final String FORMAT = "JSON";
    private static final String SIGN_TYPE = "RSA2";
    private static final String CHARSET = "UTF-8";

    private static final String PARAM_APP_AUTH_CODE = "app_auth_code";
    private static final String PARAM_APP_AUTH_TOKEN = "app_auth_token";

    private static final String GRANT_TYPE_AUTHORIZATION_CODE = "authorization_code";

    private static final String GRANT_TYPE_REFRESH_TOKEN = "refresh_token";

    private static final String SUB_DIV = ":";

    private static final String MERCHANT_FIXED_SUFFIX = "0156";

    private final String specifiedAppAuthorizeUrl;
    private final String singleAppAuthorizeUrl;
    private final String appId;
    private final String appPrivateKey;
    private final String alipayPublicKey;
    private final String[] appTypes;

    public AlipayOauth2Client(final String appId, final String appPrivateKey, final String alipayPublicKey) {
        this(SPECIFIED_APP_AUTHORIZE_URL_PROD, SINGLE_APP_AUTHORIZE_URL_PROD, appId, appPrivateKey, alipayPublicKey, null);
    }

    public AlipayOauth2Client(final String specifiedAppAuthorizeUrl, final String singleAppAuthorizeUrl, final String appId, final String appPrivateKey, final String alipayPublicKey, final String[] appTypes) {
        this.specifiedAppAuthorizeUrl = specifiedAppAuthorizeUrl;
        this.singleAppAuthorizeUrl = singleAppAuthorizeUrl;
        this.appId = appId;
        this.appPrivateKey = appPrivateKey;
        this.alipayPublicKey = alipayPublicKey;
        this.appTypes = appTypes;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getAuthorizeUrl(String redirectUri, String state) {
        return null != appTypes && appTypes.length > 0 ? getAppTypesAuthorizeUrl(appTypes, redirectUri, state) : getSingleAppAuthorizeUrl(redirectUri, state);
    }

    private String getAppTypesAuthorizeUrl(final String[] appTypes, final String redirectUri, final String state) {
        final JSONObject agentOpParams = new JSONObject().fluentPut(PROP_ISV_APP_ID, appId).fluentPut(PROP_APP_TYPES, appTypes).fluentPut(PROP_REDIRECT_URI, redirectUri).fluentPut(PROP_STATE, state);

        final String bizData = new JSONObject().fluentPut(PROP_PLATFORM_CODE, PLATFORM_CODE_FIXED).fluentPut(PROP_TASK_TYPE, TASK_TYPE_FIXED).fluentPut(PROP_AGENT_OP_PARAM, agentOpParams).toJSONString();
        return Http.createUrl(specifiedAppAuthorizeUrl, PARAM_BIZ_DATA, bizData);
    }

    private String getSingleAppAuthorizeUrl(final String redirectUri, final String state) {
        if (!StringUtils.hasText(state)) {
            return Http.createUrl(singleAppAuthorizeUrl, PARAM_APP_ID, appId, PARAM_REDIRECT_URI, redirectUri);
        }
        return Http.createUrl(singleAppAuthorizeUrl, PARAM_APP_ID, appId, PARAM_REDIRECT_URI, redirectUri, PARAM_STATE, state);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuthorizationInfo getAuthorizationInfo(Map<String, String> callbackParams) {
        final String callbackAppId = callbackParams.get(PARAM_APP_ID);
        final String accessCode = callbackParams.get(PARAM_APP_AUTH_CODE);
        Preconditions.checkState(!StringUtils.hasText(callbackAppId) || Objects.equals(callbackAppId, this.appId), "Mismatch app_id: %s", callbackAppId);
        Preconditions.checkState(StringUtils.hasText(accessCode), "Missing %s", PARAM_APP_AUTH_CODE);

        try {
            final AlipayOpenAuthTokenAppModel model = new AlipayOpenAuthTokenAppModel();
            model.setGrantType(GRANT_TYPE_AUTHORIZATION_CODE);
            model.setCode(accessCode);

            final AlipayOpenAuthTokenAppRequest request = new AlipayOpenAuthTokenAppRequest();
            request.setBizModel(model);

            return doExecuteTokenRequest(request);
        } catch (final AlipayApiException ex) {
            return throwUnchecked(ex);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuthorizationInfo refreshAccessToken(String refreshToken, String accessToken) {
        try {
            final AlipayOpenAuthTokenAppModel model = new AlipayOpenAuthTokenAppModel();
            model.setGrantType(GRANT_TYPE_REFRESH_TOKEN);
            model.setRefreshToken(refreshToken);

            final AlipayOpenAuthTokenAppRequest request = new AlipayOpenAuthTokenAppRequest();
            request.setBizModel(model);
            request.putOtherTextParam(PARAM_APP_AUTH_TOKEN, accessToken);

            return doExecuteTokenRequest(request);
        } catch (final AlipayApiException ex) {
            return throwUnchecked(ex);
        }
    }

    protected AuthorizationInfo doExecuteTokenRequest(final AlipayOpenAuthTokenAppRequest request) throws AlipayApiException {
        final AlipayOpenAuthTokenAppResponse response = doExecuteTokenRequest0(request);
        final String message = Errors.format(
                response.getCode() + SUB_DIV + response.getSubCode(),
                response.getMsg() + SUB_DIV + response.getSubMsg()
        );
        Preconditions.checkState(response.isSuccess(), message);

        final String expiresInStr = response.getExpiresIn();
        final String reExpiresInStr = response.getReExpiresIn();
        final long expiresIn = StringUtils.hasText(expiresInStr) ? Long.parseLong(expiresInStr) : -1;
        final long reExpiresIn = StringUtils.hasText(reExpiresInStr) ? Long.parseLong(reExpiresInStr) : -1;

        final AuthorizationInfo authorizationInfo = new AuthorizationInfo();
        authorizationInfo.setClientId(response.getAuthAppId());
        authorizationInfo.setUserId(response.getUserId());
        authorizationInfo.setMerchantId(response.getUserId() + MERCHANT_FIXED_SUFFIX);
        authorizationInfo.setAccessToken(response.getAppAuthToken());
        authorizationInfo.setRefreshToken(response.getAppRefreshToken());
        authorizationInfo.setExpiresIn(expiresIn);
        authorizationInfo.setReExpiresIn(reExpiresIn);

        return authorizationInfo;
    }

    protected AlipayOpenAuthTokenAppResponse doExecuteTokenRequest0(final AlipayOpenAuthTokenAppRequest request)
            throws AlipayApiException {
        final AlipayClient client = new DefaultAlipayClient(getConfig());
        return client.execute(request);
    }

    private <T> T throwUnchecked(final AlipayApiException thrown) {
        throw new IllegalStateException(thrown);
    }

    private AlipayConfig getConfig() {
        final AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(GATEWAY_URL);
        alipayConfig.setAppId(appId);
        alipayConfig.setPrivateKey(appPrivateKey);
        alipayConfig.setFormat(FORMAT);
        alipayConfig.setCharset(CHARSET);
        alipayConfig.setSignType(SIGN_TYPE);
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        return alipayConfig;
    }

}
