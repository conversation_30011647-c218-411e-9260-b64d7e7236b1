package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.dto.BillTaskDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterConfigurationException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.mapper.BillTaskMapper;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.SapBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.Errors;
import freework.util.Throwables;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SapBillArchiveService {
    private static final int BATCH_SIZE = 1000;
    private static final int MAX_BATCH_COUNT = 100;

    protected static final String SAP_SFTP_FILENAME_FORMAT = "CNCASH%s%s%02d_%s.%s";
    protected static final DateTimeFormatter SAP_SFTP_FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String SAP_OSS_PATH_FORMAT = "sap/%s/%s/%s/%s";

    private final BillCenterConfig billCenterConfig;
    private final BillTaskEventPublisher eventPublisher;
    private final AppStorageService appStorageService;

    private final BillTaskDAO billTaskDAO;
    private final BillTaskService billTaskService;
    private final TransitLogDAO transitLogDAO;
    private final MerchantConfigDAO merchantConfigDAO;
    private final List<SapBillArchiveHandler> sapBillArchiveHandlers;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void archiveAllIfNecessary(final Channel channel) {
        final ZonedDateTime now = ZonedDateTime.now();
        final ZonedDateTime since = now.toLocalDate().atStartOfDay(now.getZone());
        final int maxRetryTimes = billCenterConfig.getMaxRetryTimes();
        final int timeoutMinutes = billCenterConfig.getArchiveTaskTimeoutMinutes();

        int batchCount = 0;
        List<BillTaskEntity> waitSyncToSapTasks;
        do {
            batchCount++;
            waitSyncToSapTasks = billTaskDAO.findWaitSyncToSapTasksByChannel(channel, since, now, maxRetryTimes, BATCH_SIZE);
            for (final BillTaskEntity waitSyncToSapTask : waitSyncToSapTasks) {
                final int updated = billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(
                        SyncState.WAITING, now.plusMinutes(timeoutMinutes),
                        waitSyncToSapTask.getId(), Arrays.asList(SyncState.WAITING, SyncState.FAILED, SyncState.RUNNING)
                );
                if (updated == 1) {
                    log.info("dispatch SAP archive task, id: {}", waitSyncToSapTask.getId());
                    dispatchTask(waitSyncToSapTask.getId());
                } else {
                    log.warn("[SKIP] SAP archive task already running, id: {}", waitSyncToSapTask.getId());
                }
            }
        } while (waitSyncToSapTasks.size() >= BATCH_SIZE && batchCount < MAX_BATCH_COUNT);
    }

    private void dispatchTask(final Long taskId) {
        eventPublisher.publishSapBillArchiveEvent(taskId);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @SuppressWarnings("java:S3776")
    public boolean archiveIfNecessary(final Long taskId) {
        final BillTaskEntity task = billTaskDAO.findById(taskId).orElse(null);
        if (null == task) {
            log.warn("[SKIP] SAP archive task not found, id: {}", taskId);
            return false;
        }

        if (!SyncState.WAITING.equals(task.getSapSyncState()) && !SyncState.FAILED.equals(task.getSapSyncState())) {
            log.warn("[SKIP] SAP archive task state({}) mismatch, id: {}", task.getSapSyncState(), taskId);
            return false;
        }

        final ZonedDateTime now = ZonedDateTime.now();
        final int timeoutMinutes = billCenterConfig.getArchiveTaskTimeoutMinutes();
        final int updated = billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(
                SyncState.RUNNING, now.plusMinutes(timeoutMinutes),
                task.getId(), Arrays.asList(SyncState.WAITING, SyncState.FAILED)
        );
        if (1 > updated) {
            log.warn("[SKIP] failed to update the version, the SAP archive task may have already been executed, id = {}", taskId);
            return false;
        }
        try {
            log.info("SAP archive starting, task id: {}", taskId);
            logTransitLog(task.getTraceId(), "开始推送SAP", "开始推送SAP");

            final SapBillArchiveHandler archiveHandler = determineSapArchiveHandler(task.getChannel(), task.getBillType());
            if (null == archiveHandler) {
                throw new BillCenterConfigurationException(
                        String.format("No %s found, task id: %s", SapBillArchiveHandler.class.getSimpleName(), taskId)
                );
            }

            final MerchantConfigEntity config = merchantConfigDAO.findByChannelAndMerchantId(task.getChannel(), task.getMerchantId());
            if (null == config) {
                throw new BillCenterConfigurationException(
                        String.format("No merchant config found, merchant id: %s", task.getMerchantId())
                );
            }
            Map<String, String> map = doArchive(archiveHandler, task, config);
            final String archivedUrl = map.get(Constants.SUFFIX_CSV);
            final String archivedExcelUrl = map.get(Constants.SUFFIX_XLSX);
            final SyncState archiveState = StringUtils.hasText(archivedUrl) ? SyncState.SUCCESS : SyncState.SKIP;

            final BillTaskDTO parmas=BillTaskDTO.builder().newSyncState(archiveState).errorCount(0).errorMsg("OK").
                taskId(taskId).syncStatus(Collections.singletonList(SyncState.RUNNING))
                .nextRunTime(ZonedDateTime.now()).archiveUrl(archivedUrl).archiveExcelUrl(archivedExcelUrl).build();
            billTaskDAO.updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus(parmas);

            log.info("SAP archive successful, task id: {}", taskId);
            final String message = SyncState.SUCCESS.equals(archiveState) ? "推送SAP成功" : "无需推送SAP";
            logTransitLog(task.getTraceId(), "推送SAP完成", message);

            updateTaskStateBySapSyncStateAndBlacklineSyncState();

            return true;
        } catch (final Exception ex) {
            log.warn("SAP archive error: {}, task id: {}", ex.getMessage(), taskId, ex);

            final boolean shouldRetry = !Throwables.causedBy(ex, BillCenterConfigurationException.class);
            final int errorCountIncr = shouldRetry ? 1 : billCenterConfig.getMaxRetryTimes() + 1;
            final int errorCount = Optional.ofNullable(task.getSapErrorCount()).map(c -> c + errorCountIncr).orElse(errorCountIncr);

            final long delay = billCenterConfig.isBackoffRetry() ? (long) Math.pow(2, errorCount) : 0;
            final ZonedDateTime nextRetryTime = ZonedDateTime.now().plusMinutes(delay);
            final String message = Errors.format(Throwables.getRootCause(ex).getMessage(), 200);

            billTaskDAO.updateSapSyncStateErrorAndNextRunTimeByIdAndSyncStatus(
                    SyncState.FAILED, errorCount, message, nextRetryTime,
                    task.getId(), Collections.singletonList(SyncState.RUNNING)
            );
            logTransitLog(task.getTraceId(), "推送SAP失败", message);

            updateTaskStateBySapSyncStateAndBlacklineSyncState();

            if (!shouldRetry) {
                return false;
            }
            if (ex instanceof BillCenterBusinessException bizThrown) {
                throw bizThrown;
            }
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_SAP_ARCHIVE_FAILED, ex);
        }
    }

    private void updateTaskStateBySapSyncStateAndBlacklineSyncState() {
        final ZonedDateTime now = ZonedDateTime.now();
        final ZonedDateTime since = now.toLocalDate().atStartOfDay(now.getZone());
        final ZonedDateTime until = since.plusDays(1);
        billTaskService.updateTaskStateBySapAndBlacklineSyncState(since, until);
    }

    private Map<String, String>  doArchive(final SapBillArchiveHandler archiveHandler,
                             final BillTaskEntity task, final MerchantConfigEntity config) throws IOException {
        final StopWatch sw = new StopWatch("SAP-archive-" + task.getId());
        sw.start("archive");

        Map<String, String> filenameToOssArch= new HashMap<>();
        Map<String, File> archiveMap = archiveHandler.archive(task.getId(), config);
        sw.stop();

        if (CollectionUtils.isEmpty(archiveMap)) {
            return filenameToOssArch;
        }

        final ZonedDateTime now = ZonedDateTime.now();
        final ZonedDateTime startTime = ZonedDateTime.now().toLocalDate().atStartOfDay(now.getZone());
        final ZonedDateTime endTime = startTime.plusDays(1);
        final long count = billTaskDAO.countSapSyncSuccessTasksByChannelMerchantIdAndCreatedDate(
                task.getChannel(), task.getMerchantId(), startTime, endTime
        );

      for (Entry<String, File> entry : archiveMap.entrySet()) {
        String fileType = entry.getKey();
        File archiveFile = entry.getValue();
        final String suffix = FilenameUtils.getExtension(archiveFile.getName());
        final String filename = generateSapFilename(config.getSapMerchantCode(), config.getChannel(), count + 1, suffix);

        final String ossArchivePath = generateSapOssPath(filename);
        try (final InputStream in = new FileInputStream(archiveFile)) {
          appStorageService.writeStream(ossArchivePath, in);
        }

        try {
          if (Objects.equals(fileType, Constants.SUFFIX_CSV)) {
            sw.start("send-to-sap");
            archiveHandler.sendToSap(filename, archiveFile, config);
            sw.stop();
          }
        } finally {
          if (sw.isRunning()) {
            sw.stop();
          }
          log.info("SAP archive elapsed: {}", sw.prettyPrint());

          if (!FileUtils.deleteQuietly(archiveFile)) {   // NOSONAR
            log.warn("failed to cleanup temporary file: {}", archiveFile.getAbsolutePath());
          }
        }
        filenameToOssArch.put(fileType, ossArchivePath);
      }

      return filenameToOssArch;
    }

    private void logTransitLog(final String traceId, final String title, final String message) {
        final TransitLogEntity startAcquireLog = TransitLogEntity.builder()
                .traceId(traceId)
                .title(title)
                .message(Errors.format(message, 200))
                .build();
        EntityUtils.fill(startAcquireLog, ZonedDateTime.now());
        transitLogDAO.create(startAcquireLog);
    }

    SapBillArchiveHandler determineSapArchiveHandler(final Channel channel, final BillType billType) {
        return sapBillArchiveHandlers.stream()
                .filter(handler -> handler.matches(channel, billType))
                .findFirst()
                .orElse(null);
    }

    String generateSapFilename(final String sapMerchant,
                               final Channel channel,
                               final long seq, final String suffix) {
        // update 20250725 timestamp use prev day.
        final String timestamp = ZonedDateTime.now().minusDays(1).format(SAP_SFTP_FILENAME_DATE_FORMATTER);
        return String.format(SAP_SFTP_FILENAME_FORMAT, sapMerchant, channel, seq, timestamp, suffix);
    }

    static String generateSapOssPath(final String filename) {
        /*-
         * sap/${year}/${month}/${day}/${original_filename}.
         */
        final ZonedDateTime now = ZonedDateTime.now();

        return String.format(
                SAP_OSS_PATH_FORMAT,
                now.getYear(), now.getMonth().getValue(), now.getDayOfMonth(),
                filename
        );
    }

}