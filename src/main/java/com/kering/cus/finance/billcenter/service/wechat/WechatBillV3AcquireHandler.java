package com.kering.cus.finance.billcenter.service.wechat;

import static com.kering.cus.finance.billcenter.constant.Constants.CSV_GZ;

import com.alibaba.schedulerx.shade.org.apache.commons.lang.ObjectUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kering.cus.finance.billcenter.client.wechat.WechatPayUtils;
import com.kering.cus.finance.billcenter.client.wechat.WechatQueryBillRequest;
import com.kering.cus.finance.billcenter.client.wechat.WechatQueryBillResponse;
import com.kering.cus.finance.billcenter.config.WechatBillConfig;
import com.kering.cus.finance.billcenter.config.WechatV3MerchantAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.constant.WechatAccountType;
import com.kering.cus.finance.billcenter.constant.WechatBillType;
import com.kering.cus.finance.billcenter.constant.WechatTarType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import com.kering.cus.finance.billcenter.service.support.BillAcquireHandler;
import com.kering.cus.finance.billcenter.util.DateUtil;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName WechatBillV3AcquireHandler
 * @Description WeChat Payment Platform V3 Bill Acquisition
 * @Date 2025-07-08 15:43
 * @Version V1.0
 **/
@Slf4j
@Service
public class WechatBillV3AcquireHandler implements BillAcquireHandler {
  private static final String FILENAME_FORMAT = "%s_%s.%s";

  @Resource
  private AppSecretService appSecretService;

  @Resource
  private WechatBillConfig wechatBillConfig;

  @Override
  public boolean matches(Channel channel, GrantType grantType, BillType billType, String apiVersion) {
    return ObjectUtils.equals(Channel.WECHAT, channel) && ObjectUtils.equals(GrantType.MERCHANT, grantType) && (ObjectUtils.equals(
        BillType.TRADE_FLOW, billType) || ObjectUtils.equals(
        BillType.FUND_FLOW, billType)) && ObjectUtils.equals("v3", apiVersion);
  }

  @Override
  public String acquireTo(BillTaskEntity billTaskEntity, File targetFile) {
    log.info("Starting to process WeChat Payment Platform V3 bill task, param = {}", JsonUtil.convertToString(billTaskEntity));
    WechatV3MerchantAppProperties wechatV3MerchantAppProperties = appSecretService.getMerchantAppSecret(billTaskEntity.getExtraParams(),
        billTaskEntity.getChannel().toString(), billTaskEntity.getMerchantId(),
        WechatV3MerchantAppProperties.class);
    if (wechatV3MerchantAppProperties == null || StringUtil.isBlank(wechatV3MerchantAppProperties.getApiPrivateKey()) || StringUtil.isBlank(
        wechatV3MerchantAppProperties.getApiCertificateSerialNo())) {
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_V3BILL_KMS_ACQUIRE_FAILED);
    }
    // Call WeChat platform interface to acquire bill
    acquireWechatV3TradeBill(billTaskEntity, wechatV3MerchantAppProperties, targetFile);

    final String merchantId = billTaskEntity.getMerchantId();
    final String timestamp = DateUtil.format(billTaskEntity.getBillStartTime(), DateUtil.DATE_FORMATTER_FIRST);
    return String.format(FILENAME_FORMAT, merchantId, timestamp, CSV_GZ);
  }

  /**
   * Call WeChat platform interface to obtain bill
   *
   * @param billTaskEntity                Bill task entity
   * @param wechatV3MerchantAppProperties WeChat merchant authorization information (KMS)
   * @param targetFile                    Bill saved file
   */
  void acquireWechatV3TradeBill(BillTaskEntity billTaskEntity, WechatV3MerchantAppProperties wechatV3MerchantAppProperties, File targetFile) {
    // Build WeChat transaction bill acquisition parameters
    WechatQueryBillRequest wechatQueryBillRequest = new WechatQueryBillRequest().setBillType(WechatBillType.ALL)
        .setTarType(WechatTarType.GZIP)
        .setBillDate(billTaskEntity.getBillStartTime() != null ? DateUtil.format(billTaskEntity.getBillStartTime(),
            DateUtil.DATE_FORMATTER) : DateUtil.format(billTaskEntity.getBillEndTime(),
            DateUtil.DATE_FORMATTER)).setMchId(billTaskEntity.getMerchantId()).setAccountType(WechatAccountType.BASIC.getValue())
        .setWechatCertificateSerialNo(
            wechatV3MerchantAppProperties.getApiCertificateSerialNo()).setWechatPrivateKey(wechatV3MerchantAppProperties.getApiPrivateKey());
    CloseableHttpClient httpClient = null;
    try {
      log.info("WeChat Payment Platform V3 bill acquisition started, param = {}", JsonUtil.convertToString(wechatQueryBillRequest));
      httpClient = WechatPayUtils.createHttpClient(null, null,
          null);
      // 1. Apply for bill download URL
      WechatQueryBillResponse billResponse = applyBillDownloadUrl(wechatQueryBillRequest, httpClient, billTaskEntity.getBillType());

      // 2. Download bill file
      downloadBillFile(wechatQueryBillRequest, billResponse.getDownloadUrl(), targetFile, httpClient);

    } catch (Exception e) {
      log.error("WeChat V3 bill download failed, param = {}, error = {}", JsonUtil.convertToString(wechatQueryBillRequest), e);
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_V3BILL_ACQUIRE_FAILED, e);
    } finally {
      try {
        WechatPayUtils.closeHttpClient(httpClient);
      } catch (IOException e) {
        log.error("WeChat Payment Platform V3 bill download failed to close httpClient", e);
      }
    }
  }

  /**
   * Acquire bill download URL
   *
   * @param wechatQueryBillRequest Request parameters for WeChat platform
   * @param httpClient             HTTP client
   * @return Bill download information
   * @throws Exception if any error occurs
   */
  WechatQueryBillResponse applyBillDownloadUrl(WechatQueryBillRequest wechatQueryBillRequest,
      CloseableHttpClient httpClient, BillType billType) throws Exception {
    try (CloseableHttpResponse response = WechatPayUtils.executeWithRetry(null, getApplyBillHttpGet(wechatQueryBillRequest, billType), httpClient)) {
      int statusCode = response.getStatusLine().getStatusCode();
      String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
      log.info("WeChat Payment Platform V3 apply for bill applyBillDownloadUrl completed, result = {}", responseBody);
      if (responseBody == null) {
        log.error("WeChat Payment Platform V3 bill download failed, no content returned");
        throw new IOException("WeChat Payment Platform returned empty response");
      }
      if (statusCode < 200 || statusCode >= 300) {
        log.error(String.format(
            "WeChat Payment Platform failed to apply for V3 bill [Status Code: %d, Response: %s]", statusCode, responseBody));
        if (ObjectUtils.equals(statusCode, 400)) {
          ObjectMapper objectMapper = new ObjectMapper();
          Map<String, String> responseMap = objectMapper.readValue(responseBody, Map.class);
          if (responseMap.containsKey("code") && ObjectUtils.notEqual(responseMap.get("code"), "NO_STATEMENT_EXIST")) {
            throw new IOException(responseBody);
          }
        } else {
          throw new IOException(responseBody);
        }
      }
      return WechatPayUtils.fromJson(responseBody, WechatQueryBillResponse.class);
    }
  }

  /**
   * Creates an HTTP GET request to apply for a bill download URL from WeChat Payment Platform.
   *
   * @param wechatQueryBillRequest The request parameters for bill query
   * @param billType               The type of bill (TRADE_FLOW/FUND_FLOW)
   * @return Configured HttpGet request with necessary headers and parameters
   * @throws URISyntaxException if the URI construction fails
   */
  private HttpGet getApplyBillHttpGet(WechatQueryBillRequest wechatQueryBillRequest, BillType billType) throws URISyntaxException {
    String wechatBillHost = wechatBillConfig.getWechatBillHost();
    String baseUri = billType == BillType.TRADE_FLOW
        ? wechatBillConfig.getWechatTradeBillUrlV3()
        : wechatBillConfig.getWechatFundBillUrlV3();

    Map<String, Object> args = new HashMap<>();
    args.put("bill_date", wechatQueryBillRequest.getBillDate());
    args.put("tar_type", wechatQueryBillRequest.getTarType());
    if (billType == BillType.FUND_FLOW) {
      args.put("account_type", wechatQueryBillRequest.getAccountType());
    }
    URIBuilder uriBuilder = new URIBuilder(wechatBillHost + baseUri);
    for (Map.Entry<String, Object> entry : args.entrySet()) {
      if (entry.getValue() != null) {
        uriBuilder.addParameter(entry.getKey(), entry.getValue().toString());
      }
    }
    URI safeUri = uriBuilder.build();
    String originalUri = baseUri + "?" + WechatPayUtils.urlEncode(args);
    HttpGet httpGet = new HttpGet(safeUri);
    httpGet.setHeader("Accept", "application/json");

    httpGet.setHeader("Authorization", WechatPayUtils.buildAuthorization(
        wechatQueryBillRequest.getMchId(),
        wechatQueryBillRequest.getWechatCertificateSerialNo(),
        WechatPayUtils.loadPrivateKeyFromString(wechatQueryBillRequest.getWechatPrivateKey()),
        "GET", originalUri, null
    ));
    return httpGet;
  }

  /**
   * Download bill file
   *
   * @param downloadUrl Download URL
   * @param billFile    Bill file
   * @param httpClient  HTTP client
   * @throws Exception if any error occurs during download
   */
  void downloadBillFile(WechatQueryBillRequest wechatQueryBillRequest, String downloadUrl, File billFile, CloseableHttpClient httpClient)
      throws Exception {
    log.info("WechatBillV3AcquireHandler downloadBillFile execute, param = {}", downloadUrl);
    if (StringUtil.isBlank(downloadUrl)) {
      return;
    }
    try (CloseableHttpResponse response = WechatPayUtils.executeWithRetry(null, createDownloadBillFileHttpGet(wechatQueryBillRequest, downloadUrl),
        httpClient)) {
      int statusCode = response.getStatusLine().getStatusCode();
      if (statusCode < 200 || statusCode >= 300) {
        throw new IOException(String.format(
            "WeChat Payment Platform failed to download V3 bill [Status Code: %d]", statusCode));
      }
      HttpEntity entity = response.getEntity();
      if (entity == null) {
        log.error("WeChat Payment Platform V3 bill execution downloadBillFile, bill file content is empty");
        throw new IOException("WeChat Payment Platform V3 bill execution downloadBillFile, bill file content is empty");
      }
      final int BUFFER_SIZE = 1024 * 1024;
      try (InputStream in = new BufferedInputStream(entity.getContent());
          FileOutputStream out = new FileOutputStream(billFile);
          BufferedOutputStream bos = new BufferedOutputStream(out)) {
        byte[] buffer = new byte[BUFFER_SIZE];
        int bytesRead;
        while ((bytesRead = in.read(buffer)) != -1) {
          bos.write(buffer, 0, bytesRead);
        }
        bos.flush();
      }
    }
  }

  /**
   * Creates an HTTP GET request for downloading bill file from WeChat Payment Platform.
   *
   * @param wechatQueryBillRequest The request parameters containing merchant credentials
   * @param downloadUrl            The pre-signed URL for bill download
   * @return Configured HttpGet request with authentication headers
   * @throws URISyntaxException if URL parsing or construction fails
   */
  private static HttpGet createDownloadBillFileHttpGet(WechatQueryBillRequest wechatQueryBillRequest, String downloadUrl) throws URISyntaxException {
    URI originalUri = new URI(downloadUrl);
    URIBuilder uriBuilder = new URIBuilder()
        .setScheme(originalUri.getScheme())
        .setHost(originalUri.getHost())
        .setPort(originalUri.getPort())
        .setPath(originalUri.getPath());

    if (originalUri.getQuery() != null) {
      String[] queryParams = originalUri.getQuery().split("&");
      for (String param : queryParams) {
        String[] keyValue = param.split("=", 2);
        if (keyValue.length > 0) {
          String name = keyValue[0];
          String value = keyValue.length > 1 ? keyValue[1] : "";
          uriBuilder.addParameter(name, value);
        }
      }
    }
    URI safeUri = uriBuilder.build();
    HttpGet httpGet = new HttpGet(safeUri);
    httpGet.setHeader("Accept", "application/json");
    String result = safeUri.getPath() + (safeUri.getQuery() != null ? "?" + safeUri.getQuery() : "");

    httpGet.setHeader("Authorization", WechatPayUtils.buildAuthorization(
        wechatQueryBillRequest.getMchId(),
        wechatQueryBillRequest.getWechatCertificateSerialNo(),
        WechatPayUtils.loadPrivateKeyFromString(wechatQueryBillRequest.getWechatPrivateKey()),
        "GET", result, null
    ));
    return httpGet;
  }
}
