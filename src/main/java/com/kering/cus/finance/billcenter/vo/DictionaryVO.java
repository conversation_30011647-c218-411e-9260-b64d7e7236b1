package com.kering.cus.finance.billcenter.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName DictionaryVO
 * @Description Data dictionary value object
 * @Date 2025-07-23 18:19:36
 * @Version V1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DictionaryVO implements Serializable {

  private static final long serialVersionUID = 1L;

  private String dicGroup;

  private String dicKey;

  private String dicValue;

  private Boolean isEnabled;
}
