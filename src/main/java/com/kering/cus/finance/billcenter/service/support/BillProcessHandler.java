package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;

import java.io.File;
import java.util.List;

public interface BillProcessHandler {

    boolean matches(Channel channel, GrantType grantType, BillType billType);

    /**
     * Parse and process bill file.
     *
     * @param tempFile the bill file
     * @param task the task
     * @return the bill merchant ids
     */
    List<String> process(File tempFile, BillTaskEntity task);


    void determineBusinessDate(final BillTaskEntity task);


}
