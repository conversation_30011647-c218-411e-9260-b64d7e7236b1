package com.kering.cus.finance.billcenter.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName CommonBusinessVO
 * @Description This class represents a common business response object with generic data support.
 * @Date 2025-07-23 17:29
 * @Version V1.0
 **/
@Data
public class CommonBusinessVO<T> {

    /**
     * Exception title cus 规范
     */
    private String title;

    /**
     * Exception type (Framework: framework exception, Business: business exception) cus 规范
     */
    private String errType;

    /**
     * Exception code cus 规范
     */
    private String errCode;

    /**
     * Original exception message cus 规范
     */
    private String errorMessage;

    /**
     * Exception class coordinates cus 规范
     */
    private String exceptionName;

    /**
     * User-friendly exception details cus 规范
     */
    private String exceptionDetail;

    /**
     * API Path cus 规范
     */
    private String path;

    /**
     * APM Trace ID cus 规范
     */
    private String traceId;

    /**
     * Generic data container
     */
    private T data;

    public static <T> CommonBusinessVO<T> success(T data) {
        CommonBusinessVO<T> response = new CommonBusinessVO<>();
        response.setData(data);
        response.setErrCode("200");
        return response;
    }

    // Static builder method to fix compilation issue
    public static <T> Builder<T> builder() {
        return new Builder<>();
    }

    public static class Builder<T> {
        private String title;
        private String errType;
        private String errCode;
        private String errorMessage;
        private T data;

        public static <T> Builder<T> builder() {
            return new Builder<>();
        }

        public Builder<T> title(String title) {
            this.title = title;
            return this;
        }

        public Builder<T> errType(String errType) {
            this.errType = errType;
            return this;
        }

        public Builder<T> errCode(String errCode) {
            this.errCode = errCode;
            return this;
        }

        public Builder<T> errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public Builder<T> data(T data) {
            this.data = data;
            return this;
        }

        public CommonBusinessVO<T> build() {
            CommonBusinessVO<T> vo = new CommonBusinessVO<>();
            vo.setTitle(title);
            vo.setErrType(errType);
            vo.setErrCode(errCode);
            vo.setErrorMessage(errorMessage);
            vo.setData(data);
            return vo;
        }
    }
}
