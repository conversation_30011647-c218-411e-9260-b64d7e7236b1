package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

/**
 * Merchant configuration.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_merchant_config")
public class MerchantConfigEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("channel")
    private Channel channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("brand")
    private String brand;

    @TableField("is_tmall")
    private Boolean isTmall;

    @TableField("is_wosaipay_merchant")
    private Boolean isWosaipayMerchant;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("sap_gl_account1")
    private String sapGlAccount1;

    @TableField("sap_gl_account2")
    private String sapGlAccount2;

    @TableField("sap_profit_center")
    private String sapProfitCenter;

    @TableField("sap_merchant_code")
    private String sapMerchantCode;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

    @TableField("note")
    private String note;

}
