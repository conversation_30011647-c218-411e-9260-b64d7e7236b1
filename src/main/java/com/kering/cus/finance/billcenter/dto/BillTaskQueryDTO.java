package com.kering.cus.finance.billcenter.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for querying bill tasks with pagination
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BillTaskQueryDTO {
    /**
     * Current page number (starting from 1)
     */
    @NotNull(message = "Page number is required")
    @Positive(message = "Page number must be positive")
    private Integer page;

    /**
     * Page size (default 20, max 200)
     */
    @PositiveOrZero(message = "Limit must be non-negative")
    private Integer limit = 20;

    /**
     * Payment channel
     */
    private List<String> channels;

    /**
     * Brand identifier
     */
    private List<String> brands;
    /**
     * Merchant ID
     */
    private String merchantId;
    /**
     * Bill type
     */
    private String billType;
    /**
     * Task status
     */
    private String status;
    /**
     * Task status name
     */
    private String statusName;


    /**
     * Task creation date (GMT time)
     */
    private String createdDateStart;

    /**
     * Task creation end date (GMT time)
     */
    private String createdDateEnd;

    private String businessDateStart;

    private String businessDateEnd;


    /**
     * Validate parameters with business rules
     */
    public void validate() {
        if (page == null || page < 1) {
            throw new IllegalArgumentException("Page number must be at least 1");
        }
        if (limit == null || limit < 1) {
            limit = 20;
        }
        if (limit > 200) {
            throw new IllegalArgumentException("Page size cannot exceed 200");
        }
    }

    public List<String> getChannels() {
        return channels == null ? Collections.emptyList() : Collections.unmodifiableList(channels);
    }

    public List<String> getBrands() {
        return this.brands == null ? Collections.emptyList() : Collections.unmodifiableList(brands);
    }
}
