package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.BillAcquireService;
import com.kering.cus.lib.message.queue.consumer.MessageConsumer;
import com.kering.cus.lib.message.queue.consumer.annotation.MessageListener;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * Bill acquire task kafka consumer.
 * <p>
 * The @DependsOn here is to skip the Spring(&le 6.2.1) BUG,
 * BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean.
 *
 * <AUTHOR>
 * @see <a href="https://github.com/spring-projects/spring-kafka/pull/3716">spring-kafka: spring-framework-bom from 6.2.1 to 6.2.2</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/34186">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean #34186</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/33972">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create a FactoryBean #33972</a>
 * @see org.springframework.kafka.listener.KafkaMessageListenerContainer#doStart
 * @since 20250711
 */
@Profile("!local")
@Component
@DependsOn({"refreshEventListener", "springApplicationAdminRegistrar"})
@MessageListener(topic = "${core.task.acquire.topic}", groupId = "${core.task.acquire.consumer-group}")
@RequiredArgsConstructor
public class DistributedBillTaskAcquireEventConsumer implements MessageConsumer<DistributedBillTaskEvent> {
    private final BillAcquireService billAcquireService;

    /**
     * {@inheritDoc}
     */
    @Override
    public void consume(final DistributedBillTaskEvent event) {
        if (TaskState.WAIT_ACQUIRE.name().equals(event.getTaskState())
                || TaskState.ACQUIRE_FAILED.name().equals(event.getTaskState())) {
            billAcquireService.acquireIfNecessary(event.getTaskId());
        }
    }

}