package com.kering.cus.finance.billcenter.service.wechat;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.exception.BillCenterErrorCode.WECHAT_FUNDBILL_PROCESS_FAILED;
import static com.kering.cus.finance.billcenter.util.CsvParserUtil.isSummaryRow;

import com.alibaba.schedulerx.shade.org.apache.commons.collections.CollectionUtils;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.WechatFundFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WechatFundFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.CsvParserUtil;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @ClassName WechatFundProcessHandler
 * @Description WeChat fund bill processing service
 * @Date 2025-07-09 16:33
 * @Version V1.0
 **/
@Slf4j
@Service
public class WechatFundProcessHandler implements BillProcessHandler {

  @Resource
  private WechatFundFlowDAO wechatFundFlowDAO;

  /**
   * Flag for identifying summary lines
   */
  private static final String TOTAL_LINE_FLAG = "资金流水总笔数";
  private static final String DEFAULT_AMOUNT = "0.00";

  @Override
  public boolean matches(Channel channel, GrantType grantType, BillType billType) {
    return Channel.WECHAT == channel && GrantType.MERCHANT == grantType && BillType.FUND_FLOW == billType;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public List<String> process(File tempFile, BillTaskEntity billTaskEntity) {
    if (tempFile == null || !tempFile.exists() || billTaskEntity == null) {
      log.warn("Invalid parameters for processing WeChat fund file");
      return Lists.newArrayList();
    }
    if (tempFile.length() == 0) {
      return Lists.newArrayList(billTaskEntity.getMerchantId());
    }
    List<WechatFundFlowEntity> batchList = new ArrayList<>(BATCH_SIZE);
    try (InputStream fis = new FileInputStream(tempFile);
        GZIPInputStream gis = new GZIPInputStream(fis);
        BufferedReader reader = new BufferedReader(
            new InputStreamReader(gis, StandardCharsets.UTF_8))) {
      dealCsvFileToDb(billTaskEntity, batchList, reader);
      return Lists.newArrayList(billTaskEntity.getMerchantId());
    } catch (IOException e) {
      log.error("Error processing WeChat fund file: {}", tempFile.getName(), e);
      throw new BillCenterBusinessException(WECHAT_FUNDBILL_PROCESS_FAILED);
    }
  }


  @Override
  public void determineBusinessDate(BillTaskEntity task) {
    task.setBusinessDate(task.getBillStartTime());
  }
  /**
   * Process CSV file data and import into database with batch insertion
   *
   * @param billTaskEntity Bill task entity containing metadata for this import
   * @param batchList      List to accumulate records for batch insertion
   * @param reader         BufferedReader providing CSV content
   * @throws IOException if error occurs during file reading
   */
  void dealCsvFileToDb(BillTaskEntity billTaskEntity, List<WechatFundFlowEntity> batchList, BufferedReader reader) throws IOException {
    String line;
    int recordCount = 0;
    List<String> headerMap = new ArrayList<>();
    boolean processingData = true;

    while ((line = reader.readLine()) != null && processingData) {
      List<String> rowData = CsvParserUtil.parseCsvLine(line);

      // 处理标题行逻辑
      if (recordCount == 0) {
        headerMap.addAll(rowData);
      } else {
        // 检查是否为汇总行
        boolean isSummary = isSummaryRow(rowData, headerMap, "记账时间", TOTAL_LINE_FLAG);

        // 处理数据行
        if (!isSummary) {
          dealFileData(billTaskEntity, batchList, headerMap, rowData);

          // 批量插入逻辑
          if (batchList.size() >= BATCH_SIZE) {
            wechatFundFlowDAO.createBatch(batchList);
            batchList.clear();
          }
        } else {
          processingData = false; // 标记退出循环
        }
      }

      recordCount++; // 统一计数
    }

    if (CollectionUtils.isNotEmpty(batchList)) {
      wechatFundFlowDAO.createBatch(batchList);
    }
  }

  private void dealFileData(BillTaskEntity billTaskEntity, List<WechatFundFlowEntity> batchList, List<String> headerMap, List<String> rowData) {
    WechatFundFlowEntity wechatFundFlowEntity = buildEntity(rowData, billTaskEntity, headerMap);
    if (wechatFundFlowDAO.checkBusinessUnique(wechatFundFlowEntity) == null) {
      batchList.add(wechatFundFlowEntity);
    } else {
      log.error("WeChat fund bill data is duplicated, param = {}", JsonUtil.convertToString(wechatFundFlowEntity));
    }
  }

  /**
   * Build WeChat fund bill entity
   *
   * @param data      Row data
   * @param task      Bill task entity
   * @param headerMap Header field mapping
   * @return WeChat transaction bill entity
   */
  WechatFundFlowEntity buildEntity(List<String> data, BillTaskEntity task, List<String> headerMap) {
    WechatFundFlowEntity entity = new WechatFundFlowEntity();
    entity.setTaskId(task.getId());
    entity.setBrand(task.getBrand());
    entity.setChannel(task.getChannel());
    entity.setMerchantId(task.getMerchantId());
    entity.setIsSyncToBlackline(task.getIsSyncToBlackline());
    entity.setIsSyncToSap(task.getIsSyncToSap());

    entity.setBillingDate(CsvParserUtil.safeGetString(data, headerMap, "记账时间"));
    entity.setBizOrderId(CsvParserUtil.safeGetString(data, headerMap, "微信支付业务单号"));
    entity.setTransactionNo(CsvParserUtil.safeGetString(data, headerMap, "资金流水单号"));
    entity.setTransactionName(CsvParserUtil.safeGetString(data, headerMap, "业务名称"));
    entity.setTransactionType(CsvParserUtil.safeGetString(data, headerMap, "业务类型"));
    String paymentType = CsvParserUtil.safeGetString(data, headerMap, "收支类型");
    entity.setPaymentType(paymentType);

    BigDecimal amount = CsvParserUtil.safeParseBigDecimal(data, headerMap, "收支金额(元)", new BigDecimal(DEFAULT_AMOUNT));
    if ("支出".equals(paymentType)) {
      amount = amount.negate();
    }
    entity.setAmount(amount);
    entity.setBalance(CsvParserUtil.safeParseBigDecimal(data, headerMap, "账户结余(元)", new BigDecimal(DEFAULT_AMOUNT)));
    entity.setApplyUser(CsvParserUtil.safeGetString(data, headerMap, "资金变更提交申请人"));
    entity.setNote(CsvParserUtil.safeGetString(data, headerMap, "备注"));
    entity.setBizVoucherNo(CsvParserUtil.safeGetString(data, headerMap, "业务凭证号"));
    entity.setCreatedDate(ZonedDateTime.now());
    entity.setModifiedDate(ZonedDateTime.now());

    return entity;
  }
}
