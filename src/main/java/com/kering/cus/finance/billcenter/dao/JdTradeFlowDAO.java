package com.kering.cus.finance.billcenter.dao;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.mapper.JdTradeFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class JdTradeFlowDAO extends MyBatisBaseDAO<JdTradeFlowEntity, JdTradeFlowMapper, Long> {

    public List<String> findByMerchantIdAndTransactionNos(List<String> transactionNos,
                                                    String merchantId){
        if(CollectionUtils.isEmpty(transactionNos)){
            return Lists.newArrayList();
        }
        final LambdaQueryWrapper<JdTradeFlowEntity> query = Wrappers.<JdTradeFlowEntity>lambdaQuery()
                .in(JdTradeFlowEntity::getTransactionNo, transactionNos)
                .eq(JdTradeFlowEntity::getMerchantId, merchantId)
                .eq(JdTradeFlowEntity::getDeleted,false)
                .select(JdTradeFlowEntity::getTransactionNo);
        return entityMapper.selectObjs(query);
    }


    public Page<JdTradeFlowEntity> findJdTradeBillPageByParams(List<Long> taskIds, int pageSize, int pageNum){
        final LambdaQueryWrapper<JdTradeFlowEntity> query = Wrappers.<JdTradeFlowEntity>lambdaQuery()
                .in(JdTradeFlowEntity::getTaskId,taskIds)
                .eq(JdTradeFlowEntity::getDeleted, false)
                .eq(JdTradeFlowEntity::getIsSyncToBlackline,true);
        return entityMapper.selectPage(new Page<>(pageNum, pageSize), query);
    }



}

