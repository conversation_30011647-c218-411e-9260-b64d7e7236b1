package com.kering.cus.finance.billcenter.constant;

public enum WechatBillType {
  ALL("ALL"),
  SUCCESS("SUCCESS"),
  REFUND("REFUND");

  private final String value;

  WechatBillType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public static WechatBillType fromValue(String value) {
    for (WechatBillType type : WechatBillType.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid WeChat bill type: " + value);
  }
}
