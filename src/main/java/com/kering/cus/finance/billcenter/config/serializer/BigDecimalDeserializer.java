package com.kering.cus.finance.billcenter.config.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;



public class BigDecimalDeserializer extends  JsonDeserializer<BigDecimal> {

    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt)  {
        try {
            String value = p.getText();
            if (!StringUtils.hasText(value) || "null".equals(value)) {
                return BigDecimal.ZERO;
            }
            String cleanValue = value.replace(",", "");
            return new BigDecimal(cleanValue);
        } catch (Exception e) {
            throw new IllegalArgumentException("Incorrect format");
        }
    }

}
