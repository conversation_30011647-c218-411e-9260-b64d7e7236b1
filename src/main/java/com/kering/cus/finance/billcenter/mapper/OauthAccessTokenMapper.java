package com.kering.cus.finance.billcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OauthAccessTokenMapper extends BaseMapper<OauthAccessTokenEntity> {


  Optional<OauthAccessTokenEntity> findByPlatformAndMerchantId(@Param("platform") String platform,
                                                               @Param("merchantId") String merchantId);

  List<Long> findRefreshAccessTokenIds(@Param("when") ZonedDateTime when,
                                       @Param("maxRetryTimes") int maxRetryTimes,
                                       @Param("limit") int limit);

}
