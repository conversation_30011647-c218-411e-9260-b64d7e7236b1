package com.kering.cus.finance.billcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MerchantConfigMapper extends BaseMapper<MerchantConfigEntity> {

    List<MerchantConfigEntity> findByChannelAndBrandIgnoreCase(@Param("channel") Channel channel, @Param("brand") String brand);

}
