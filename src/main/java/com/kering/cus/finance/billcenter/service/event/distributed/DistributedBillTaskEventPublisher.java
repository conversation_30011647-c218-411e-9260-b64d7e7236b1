package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.lib.message.queue.producer.Producer;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;

@Profile("!local")
@Component
@RequiredArgsConstructor
public class DistributedBillTaskEventPublisher implements BillTaskEventPublisher {
    private final BillCenterConfig billCenterConfig;
    private final Producer<DistributedBillTaskEvent> producer;

    @Override
    public void publishWaitAcquireEvent(final Long taskId) {
        final String topic = billCenterConfig.getAcquireTaskTopic();
        final DistributedBillTaskEvent event = new DistributedBillTaskEvent(taskId, TaskState.WAIT_ACQUIRE.name());
        producer.sendMessage(topic, event);
    }

    @Override
    public void publishWaitAcquireEvent(final List<Long> taskIds) {
        taskIds.forEach(this::publishWaitAcquireEvent);
    }

    @Override
    public void publishWaitProcessEvent(final Long taskId) {
        final String topic = billCenterConfig.getProcessTaskTopic();
        final DistributedBillTaskEvent event = new DistributedBillTaskEvent(taskId, TaskState.WAIT_PROCESS.name());
        producer.sendMessage(topic, event);
    }

    @Override
    public void publishWaitProcessEvent(final List<Long> taskIds) {
        taskIds.forEach(this::publishWaitProcessEvent);
    }

}
