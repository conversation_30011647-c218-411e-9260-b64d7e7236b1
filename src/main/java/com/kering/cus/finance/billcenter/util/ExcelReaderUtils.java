package com.kering.cus.finance.billcenter.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;

import static com.kering.cus.finance.billcenter.util.DateUtils.YYYY_MM_DD;


@Slf4j
public class ExcelReaderUtils {

    private ExcelReaderUtils(){

    }
    public static void readInBatches(InputStream inputStream, int sheetIndex, int headerRow,
                                     int batchSize, Consumer<List<Map<String, String>>> batchConsumer) {
        if (inputStream == null || batchSize <= 0 || batchConsumer == null) {
            return;
        }

        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = getSheetSafely(workbook, sheetIndex);
            if (sheet == null) {
                return;
            }

            List<String> headers = getHeaders(sheet, headerRow);
            if (headers.isEmpty()) {
                return;
            }

            List<Map<String, String>> batch = new ArrayList<>(batchSize);
            for (int i = headerRow + 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, String> rowData = new LinkedHashMap<>();
                for (int j = 0; j < headers.size(); j++) {
                    Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    rowData.put(headers.get(j), getSafeCellValue(cell));
                }
                batch.add(rowData);

                if (batch.size() >= batchSize) {
                    batchConsumer.accept(batch);
                    batch = new ArrayList<>(batchSize);
                }
            }
            if (!batch.isEmpty()) {
                batchConsumer.accept(batch);
            }
        } catch (Exception e) {
            log.error("error reading Excel file by index", e);
            throw new IllegalStateException("Error reading Excel file by index", e);
        }
    }

    public static void readInBatches(InputStream inputStream, String sheetName, int headerRow,
                                     int batchSize, Consumer<List<Map<String, String>>> batchConsumer) {
        if (inputStream == null || sheetName == null || batchSize <= 0 || batchConsumer == null) {
            return;
        }

        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                return;
            }

            List<String> headers = getHeaders(sheet, headerRow);
            if (headers.isEmpty()) {
                return;
            }

            List<Map<String, String>> batch = new ArrayList<>(batchSize);
            for (int i = headerRow + 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, String> rowData = new LinkedHashMap<>();
                for (int j = 0; j < headers.size(); j++) {
                    Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    rowData.put(headers.get(j), getSafeCellValue(cell));
                }
                batch.add(rowData);

                if (batch.size() >= batchSize) {
                    batchConsumer.accept(batch);
                    batch = new ArrayList<>(batchSize);
                }
            }

            if (!batch.isEmpty()) {
                batchConsumer.accept(batch);
            }
        } catch (Exception e) {
            log.error("Error reading Excel file by name", e);
            throw new IllegalStateException("Error reading Excel file by name", e);
        }
    }

    private static Sheet getSheetSafely(Workbook workbook, int sheetIndex) {
        try {
            return workbook.getSheetAt(sheetIndex);
        } catch (Exception e) {
            return null;
        }
    }

    private static List<String> getHeaders(Sheet sheet, int headerRow) {
        List<String> headers = new ArrayList<>();
        if (sheet == null) {
            return headers;
        }

        Row header = sheet.getRow(headerRow);
        if (header == null) {
            return headers;
        }

        for (Cell cell : header) {
            headers.add(getSafeCellValue(cell));
        }

        if (headers.stream().allMatch(String::isEmpty)) {
            headers.clear();
            for (int i = 0; i < header.getLastCellNum(); i++) {
                headers.add("Column_" + (i + 1));
            }
        }

        return headers;
    }

    private static String getSafeCellValue(Cell cell) {
        if (cell == null || cell.getCellType() == CellType.BLANK) {
            return "";
        }
        try {
            if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
                LocalDate date = cell.getLocalDateTimeCellValue().toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);
                return date.format(formatter);
            }
            DataFormatter formatter = new DataFormatter();
            return formatter.formatCellValue(cell).trim();
        } catch (Exception e) {
            return "";
        }
    }
}
