package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.mapper.MerchantConfigMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class MerchantConfigDAO extends MyBatisBaseDAO<MerchantConfigEntity, MerchantConfigMapper, Long> {

    public MerchantConfigEntity findByChannelAndMerchantId(final Channel channel, final String merchantId) {
        final LambdaQueryWrapper<MerchantConfigEntity> query = new LambdaQueryWrapper<MerchantConfigEntity>()
                .eq(MerchantConfigEntity::getChannel, channel)
                .eq(MerchantConfigEntity::getDeleted, false)
                .eq(MerchantConfigEntity::getMerchantId, merchantId);
        return entityMapper.selectOne(query);
    }

    public MerchantConfigEntity findAnyOneByChannelAndBrand(final Channel channel, String brand) {
        final List<MerchantConfigEntity> candidates = entityMapper.findByChannelAndBrandIgnoreCase(channel, brand);
        return !CollectionUtils.isEmpty(candidates) ? candidates.iterator().next() : null;
    }
}

