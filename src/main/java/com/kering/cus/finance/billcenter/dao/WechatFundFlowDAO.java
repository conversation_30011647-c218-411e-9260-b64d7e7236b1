package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.WechatFundFlowEntity;
import com.kering.cus.finance.billcenter.mapper.WechatFundFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @ClassName WechatFundFlowDAO
 * @Description Data Access Object for WeChat fund flow records
 * @Date <unknown>
 * @Version V1.0
 **/
@Repository
public class WechatFundFlowDAO extends MyBatisBaseDAO<WechatFundFlowEntity, WechatFundFlowMapper, Long> {

  /**
   * Checks business uniqueness for WeChat fund flow records
   *
   * @param entity Entity to check uniqueness for
   * @return Existing entity if duplicate found, null otherwise
   */
  public WechatFundFlowEntity checkBusinessUnique(WechatFundFlowEntity entity) {
    LambdaQueryWrapper<WechatFundFlowEntity> query = new LambdaQueryWrapper<WechatFundFlowEntity>()
        .eq(WechatFundFlowEntity::getMerchantId, entity.getMerchantId())
        .eq(WechatFundFlowEntity::getBizOrderId, entity.getBizOrderId())
        .eq(WechatFundFlowEntity::getTransactionType, entity.getTransactionType())
        .eq(WechatFundFlowEntity::getDeleted, false);
    query.last("limit 1");
    return entityMapper.selectOne(query);
  }

  /**
   * Query count of fund bill records matching the task criteria
   *
   * @param taskId Bill task id
   * @return Number of matching records
   */
  public long selectFundBillCount(Long taskId) {
    return entityMapper.selectCount(new LambdaQueryWrapper<WechatFundFlowEntity>()
        .eq(WechatFundFlowEntity::getTaskId, taskId)
        .eq(WechatFundFlowEntity::getIsSyncToSap, true)
        .eq(WechatFundFlowEntity::getDeleted, false));
  }

  /**
   * Query paginated fund bill records matching the task criteria
   *
   * @param pageRequest Pagination parameters
   * @param taskId      Bill task id
   * @return Page of matching fund flow entities
   */
  public Page<WechatFundFlowEntity> selectFundBillPage(Page<WechatFundFlowEntity> pageRequest, Long taskId) {
    return entityMapper.selectPage(pageRequest, new LambdaQueryWrapper<WechatFundFlowEntity>()
        .eq(WechatFundFlowEntity::getTaskId, taskId)
        .eq(WechatFundFlowEntity::getIsSyncToSap, true)
        .eq(WechatFundFlowEntity::getDeleted, false));
  }
}
