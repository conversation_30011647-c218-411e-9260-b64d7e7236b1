package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * TM-Wechat bill flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_tm_wechat_bill_flow")
public class TmWechatBillFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private String channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("posting_date")
    private String postingDate;

    @TableField("transaction_no")
    private String transactionNo;

    @TableField("tm_order_id")
    private String tmOrderId;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("income")
    private BigDecimal income;

    @TableField("expense")
    private BigDecimal expense;

    @TableField("biz_desc")
    private String bizDesc;

    @TableField("note")
    private String note;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
