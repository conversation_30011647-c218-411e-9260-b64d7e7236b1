package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * JD wallet flow.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_jd_wallet_flow")
public class JdWalletFlowEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("task_id")
    private Long taskId;

    @TableField("brand")
    private String brand;

    @TableField("channel")
    private String channel;

    @TableField("merchant_id")
    private String merchantId;

    @TableField("transaction_time")
    private String transactionTime;

    @TableField("billing_time")
    private String billingTime;

    @TableField("transaction_no")
    private String transactionNo;

    @TableField("account_code")
    private String accountCode;

    @TableField("account_name")
    private String accountName;

    @TableField("currency")
    private String currency;

    @TableField("payment_type")
    private String paymentType;

    @TableField("income")
    private BigDecimal income;

    @TableField("expense")
    private BigDecimal expense;

    @TableField("balance")
    private BigDecimal balance;

    @TableField("transaction_type")
    private String transactionType;

    @TableField("merchant_order_id")
    private String merchantOrderId;

    @TableField("origin_merchant_order_id")
    private String originMerchantOrderId;

    @TableField("transaction_note")
    private String transactionNote;

    @TableField("voucher_no")
    private String voucherNo;

    @TableField("transaction_order_id")
    private String transactionOrderId;

    @TableField("biz_order_id")
    private String bizOrderId;

    @TableField("platform_order_id")
    private String platformOrderId;

    @TableField("is_afs_order")
    private Boolean isAfsOrder;

    @TableField("is_sync_to_blackline")
    private Boolean isSyncToBlackline;

    @TableField("is_sync_to_sap")
    private Boolean isSyncToSap;

}
