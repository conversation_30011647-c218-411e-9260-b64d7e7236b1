package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.constant.TaskState;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class JvmBillTaskEvent extends ApplicationEvent {
    private final TaskState taskState;

    public JvmBillTaskEvent(final Long taskId, final TaskState taskState) {
        super(taskId);
        this.taskState = taskState;
    }

    public Long getTaskId() {
        return (Long) getSource();
    }

}
