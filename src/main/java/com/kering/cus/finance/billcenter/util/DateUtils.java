package com.kering.cus.finance.billcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @ClassName BillTaskVO
 * @Description Value Object for bill task data transfer to frontend
 * @Date 2025-07-24 15:30:00
 * @Version V1.0
 **/
@Slf4j
public class DateUtils {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDD = "yyyyMMdd";


    private DateUtils() {
    }

    public static LocalDate extractDateEnhanced(String input, String[] patterns) {
        if (input == null || input.isEmpty()) {
            return null;
        }

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDate.parse(input, formatter);
            } catch (Exception e) {
                log.warn("extractDateEnhanced error,data : {}", input);
            }
        }
        return null;
    }


    public static String convertDateStr(String inputStr, String pattern) {
        if (!StringUtils.hasText(inputStr)) {
            return "";
        }
        String[] patterns = new String[]{YYYY_MM_DD_HH_MM_SS, YYYY_MM_DD, "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd", "yyyy-M-d H:mm",
                "yyyy-MM-dd HH:mm", "yyyy-MM-d HH:mm", "yyyy/MM/dd HH:mm", "yyyy/M/d HH:mm", "yyyy/M/dd HH:mm", "yyyy/M/dd H:mm", "yyyy/M/d H:mm",
                "yyyy/M/d H:m", "yyyy-M-d HH:mm"};
        LocalDate localDate = extractDateEnhanced(inputStr, patterns);
        Assert.notNull(localDate, "date is null");

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDate.format(outputFormatter);
    }

    /**
     * Format ZonedDateTime to specified pattern
     *
     * @param dateTime ZonedDateTime to format
     * @param pattern  format pattern
     * @return formatted date string or empty if null
     */
    public static String formatDateTime(ZonedDateTime dateTime, String pattern) {
        if (dateTime == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return dateTime.format(formatter);
    }

    public static String minusDay(String dateStr, String pattern, int day) {
        if (!StringUtils.hasText(dateStr)) {
            return "";
        }
        try {
            LocalDate localDate = extractDateEnhanced(dateStr, new String[]{YYYY_MM_DD_HH_MM_SS, YYYY_MM_DD});
            LocalDate newDate = localDate.minusDays(day);
            return newDate.format(DateTimeFormatter.ofPattern(pattern));
        } catch (Exception e) {
            log.warn("minusDay error, date: {}, pattern: {}", dateStr, pattern, e);
            return "";
        }
    }
}
