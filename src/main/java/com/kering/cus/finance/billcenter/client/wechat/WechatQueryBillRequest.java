package com.kering.cus.finance.billcenter.client.wechat;

import com.kering.cus.finance.billcenter.constant.WechatBillType;
import com.kering.cus.finance.billcenter.constant.WechatSignType;
import com.kering.cus.finance.billcenter.constant.WechatTarType;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName WechatQueryBillResponse
 * @Description WeChat Platform Bill Acquisition Request Parameters
 * @Date 2025-07-07 18:31
 * @Version V1.0
 **/
@Data
@Accessors(chain = true)
public class WechatQueryBillRequest implements Serializable {

  private static final long serialVersionUID = 1915124918245588545L;

  /**
   * WeChat Platform AppID
   */
  private String appId;
  /**
   * WeChat Platform AppKey
   */
  private String appKey;
  /**
   * Merchant ID
   */
  private String mchId;

  /**
   * Random String
   */
  private String nonceStr;

  /**
   * Signature
   */
  private String sign;
  /**
   * Signature Type
   */
  private WechatSignType signType;

  /**
   * Statement Date
   */
  private String billDate;

  /**
   * Bill Type
   */
  private WechatBillType billType;

  /**
   * Compressed Bill
   */
  private WechatTarType tarType;

  /**
   * Fund Account Type
   */
  private String accountType;

  /**
   * Merchant API Certificate Serial Number
   */
  private String wechatCertificateSerialNo;

  /**
   * WeChat Merchant Private Key Information
   */
  private String wechatPrivateKey;

  /**
   * WeChat Platform Public Key ID
   */
  private String wechatPublicKeyId;

  /**
   * WeChat Platform Certificate Public Key Information
   */
  private String wechatPublicKey;

}
