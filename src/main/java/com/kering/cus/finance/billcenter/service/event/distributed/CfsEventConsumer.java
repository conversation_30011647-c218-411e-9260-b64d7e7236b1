package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.service.SftpBillTaskCreateProxyService;
import com.kering.cus.lib.common.util.JsonUtil;
import com.kering.cus.lib.message.queue.consumer.MessageConsumer;
import com.kering.cus.lib.message.queue.consumer.annotation.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;

/**
 * CFS kafka consumer.
 * <p>
 * The @DependsOn here is to skip the Spring(&le 6.2.1) BUG,
 * BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean.
 *
 * <AUTHOR>
 * @see <a href="https://github.com/spring-projects/spring-kafka/pull/3716">spring-kafka: spring-framework-bom from 6.2.1 to 6.2.2</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/34186">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create bean #34186</a>
 * @see <a href="https://github.com/spring-projects/spring-framework/issues/33972">BeanCurrentlyInCreationException is thrown when multiple threads simultaneously try to create a FactoryBean #33972</a>
 * @see org.springframework.kafka.listener.KafkaMessageListenerContainer#doStart
 * @since 20250711
 */
@Slf4j
@Profile("!local")
@Component
@DependsOn({"refreshEventListener"})
@MessageListener(topic = "${KAFKA_TOPIC_CUS_BILLCENTER}", groupId = "${KAFKA_GROUPID_CUS_CG_CUS_BILL_CENTER_RSVP_EVENT_GROUP}")
public class CfsEventConsumer implements MessageConsumer<CfsEvent> {
    private static final String CFS_MQ_SOURCE = "CFS_MQ";
    private static final String UPLOAD = "upload";

    private final SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService;

    public CfsEventConsumer(final SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService) {
        this.sftpBillTaskCreateProxyService = sftpBillTaskCreateProxyService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void consume(final CfsEvent event) {
        final String stringify = JsonUtil.convertToString(event);
        log.info("receive CFS event: {}", stringify);
        if (UPLOAD.equals(event.getAction())) {
            sftpBillTaskCreateProxyService.createTaskIfNecessary(event.getVirtualPath(), ZonedDateTime.now(), CFS_MQ_SOURCE);
        } else {
            log.warn("SKIP, CFS event: {}", stringify);
        }
    }
}
