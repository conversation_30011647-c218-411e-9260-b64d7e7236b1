package com.kering.cus.finance.billcenter.service.jd;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractSapBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.SapBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import com.kering.cus.finance.billcenter.util.CsvWriterUtils;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.DEFAULT_VALUE;

@Service
@Slf4j
public class JdSapBillArchiveHandler extends AbstractSapBillArchiveHandler implements SapBillArchiveHandler {

  @Resource
  private BillTaskDAO billTaskDAO;
  @Resource
  private JdWalletFlowDAO jdWalletFlowDAO;


  public JdSapBillArchiveHandler(SftpConfig sftpConfig) {
    super(Channel.JD, BillType.FUND_FLOW, sftpConfig);
  }


  @Override
  public boolean matches(Channel channel, BillType billType) {
    return Objects.equals(channel, Channel.JD) && Objects.equals(billType, BillType.FUND_FLOW);

  }

  @Override
  public Map<String, File> archive(Long taskId, MerchantConfigEntity config) throws IOException {
    if (Objects.isNull(config.getIsSyncToSap()) || Boolean.FALSE.equals(config.getIsSyncToSap())) {
      log.warn("jd task {} is not sync to sap", taskId);
      return Collections.emptyMap();
    }
    Map<String, File> mapFile = new HashMap<>();
    try {
      BillTaskEntity task = billTaskDAO.findById(taskId).orElse(null);
      Assert.notNull(task, String.format("task not found,taskId: %s", taskId));
      List<JdWalletFlowEntity> jdWalletFlowEntityList = jdWalletFlowDAO.findJdWalletBillByParams(config.getMerchantId(), taskId, config.getBrand());
      if (CollectionUtils.isEmpty(jdWalletFlowEntityList)) {
        return Collections.emptyMap();
      }
      mapFile.put(Constants.SUFFIX_CSV, createJdCsvFile(jdWalletFlowEntityList, config));
      mapFile.put(Constants.SUFFIX_XLSX, createJdExcelFile(jdWalletFlowEntityList, config));
    } catch (Exception e) {
      log.error("jd task {} archive failed", taskId, e);
      throw new BillCenterBusinessException(BillCenterErrorCode.BILL_SAP_ARCHIVE_FAILED, e);
    }

    return mapFile;
  }

  private File createJdCsvFile(List<JdWalletFlowEntity> jdWalletFlowEntityList, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile(BillType.FUND_FLOW.name(), Constants.SUFFIX_CSV).toFile();
    AtomicInteger serialNum = new AtomicInteger(1);
    try (CsvWriterUtils writer = new CsvWriterUtils(targetFile)) {
      writer.writeHeader(getHeaders());
      List<List<JdWalletFlowEntity>> partition = ListUtils.partition(jdWalletFlowEntityList, BATCH_SIZE);
      for (List<JdWalletFlowEntity> batch : partition) {
        writer.writeBatch(getContentList(batch, config, serialNum));
      }
    }
    return targetFile;
  }


  private File createJdExcelFile(List<JdWalletFlowEntity> jdWalletFlowEntityList, MerchantConfigEntity config) throws IOException {
    File targetFile = FileUtils2.createTempFile(BillType.FUND_FLOW.name(), Constants.SUFFIX_XLSX).toFile();
    AtomicInteger serialNum = new AtomicInteger(1);
    ExcelWriterUtils writer = new ExcelWriterUtils();
    writer.createSheet("JD_SAP").writeHeader(getHeaders());
    ListUtils.partition(jdWalletFlowEntityList, BATCH_SIZE).forEach(batch -> writer.writeRows(getContentList(batch, config, serialNum)));
    writer.writeTo(targetFile);
    return targetFile;
  }


  private List<String[]> getContentList(List<JdWalletFlowEntity> jdWalletFlowEntityList, MerchantConfigEntity config, AtomicInteger serialNum) {
    List<String[]> list = Lists.newArrayList();
    for (JdWalletFlowEntity jdWalletFlowEntity : jdWalletFlowEntityList) {
      String[] lines = new String[]{
          String.valueOf(serialNum.getAndIncrement()),
          StringUtils.defaultString(jdWalletFlowEntity.getBrand()),
          StringUtils.defaultString(jdWalletFlowEntity.getChannel()),
          StringUtils.defaultString(jdWalletFlowEntity.getMerchantId()),
          StringUtils.defaultString(config.getSapProfitCenter()),
          StringUtils.defaultString(config.getSapGlAccount1()),
          StringUtils.defaultString(config.getSapGlAccount2()),
          extractMerchantCode(jdWalletFlowEntity.getAccountCode()),
          StringUtils.defaultString(jdWalletFlowEntity.getAccountCode()),
          StringUtils.defaultString(jdWalletFlowEntity.getAccountName()),
          DateUtils.convertDateStr(jdWalletFlowEntity.getTransactionTime(), DateUtils.YYYYMMDD),
          StringUtils.defaultString(jdWalletFlowEntity.getMerchantOrderId()),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(jdWalletFlowEntity.getBalance(), DEFAULT_VALUE),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(jdWalletFlowEntity.getIncome(), DEFAULT_VALUE),
          com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(jdWalletFlowEntity.getExpense(), DEFAULT_VALUE),
          StringUtils.defaultString(jdWalletFlowEntity.getTransactionNote()),
          DateUtils.convertDateStr(jdWalletFlowEntity.getBillingTime(), DateUtils.YYYYMMDD),
          StringUtils.defaultString(jdWalletFlowEntity.getPlatformOrderId())};
      list.add(lines);
    }
    return list;
  }


  @Override
  protected String getSapSftpUsername() {
    return sftpConfig.getSapCrushSftpJdUsername();
  }


  @Override
  protected String getSapSftpPassword() {
    return sftpConfig.getSapCrushSftpJdPassword();
  }

  @Override
  protected String getSapSftpPrivateKey() {
    return sftpConfig.getSapCrushSftpJdPrivateKey();
  }

  @Override
  protected String getSapSftpPrivateKeyPassphrase() {
    return sftpConfig.getSapCrushSftpJdPrivateKeyPassphrase();
  }

  @Override
  protected String getSapSftpTargetDirectory() {
    return sftpConfig.getSapCrushSftpJdArchiveDirectory();
  }


  private String[] getHeaders() {
    return new String[]{"Serial Number","Brand","Channel","Settlement Merchant ID","Profit Center","General Ledger Account",
        "Clearing Account","Merchant ID","Account Code","Account Name","Date","Merchant Order Number","Account Balance","Income Amount","Expense Amount",
        "Transaction Remarks","Bill Date","Platform Order Number"};
  }


  public static String extractMerchantCode(String input) {
    if (input == null || input.length() <= 3) {
      return "";
    }
    return input.substring(0, input.length() - 3);
  }

}
