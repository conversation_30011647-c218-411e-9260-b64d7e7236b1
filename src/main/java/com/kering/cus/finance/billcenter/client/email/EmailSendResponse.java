package com.kering.cus.finance.billcenter.client.email;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.ZonedDateTime;

@Data
public class EmailSendResponse {

    private int code;
    private String message="";
    private ZonedDateTime responseTime;
    private EmailSendApiResultVO data;

    public EmailSendApiResultVO getData() {
        if (data == null) {
            return null;
        }
        EmailSendApiResultVO emailSendApiResultVO = new EmailSendApiResultVO();
        BeanUtils.copyProperties(data, emailSendApiResultVO);
        return emailSendApiResultVO;
    }
}