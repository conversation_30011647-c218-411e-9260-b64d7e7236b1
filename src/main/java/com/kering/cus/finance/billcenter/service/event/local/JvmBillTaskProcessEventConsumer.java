package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.service.BillProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Profile("local")
@Component
@RequiredArgsConstructor
public class JvmBillTaskProcessEventConsumer implements ApplicationListener<JvmBillTaskEvent> {

    private final BillProcessService billProcessService;

    /**
     * {@inheritDoc}
     */
    @Override
    public void onApplicationEvent(final JvmBillTaskEvent event) {
        if (JvmBillTaskEvent.PROCESS.equals(event.getTaskType())) {
            billProcessService.processIfNecessary(event.getTaskId());
        }
    }

}
