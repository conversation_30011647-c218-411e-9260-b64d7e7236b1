package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.*;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.service.SftpBillTaskCreateService;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

/**
 * SFTP bill task create support.
 *
 * <AUTHOR>
 * @since 20250701
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractSftpBillTaskCreateService implements SftpBillTaskCreateService {
    protected final Channel channel;
    protected final BillTaskDAO billTaskDAO;
    protected final TransitLogDAO transitLogDAO;
    protected final MerchantConfigDAO merchantConfigDAO;

    /**
     * Create SFTP bill task if necessary.
     *
     * @param brand      the brand
     * @param merchantId the merchant id
     * @param billType   the bill type
     * @param sftpPath   the SFTP path
     * @param when       the trigger time
     * @param source     the task source
     * @return created task id list
     */
    protected Long doCreateTaskIfNecessary(final String brand, final String merchantId,
                                           final BillType billType, final String sftpPath,
                                           final ZonedDateTime when, final String source) {
        final String brandUpper = null != brand ? brand.toUpperCase() : null;
        final String taskKey = getTaskKey(channel, brandUpper, merchantId, billType, sftpPath, source);
        if (checkDuplicatedKey(taskKey)) {
            log.warn("Bill task already exists, SKIP: {}, {}", billType, sftpPath);
            return null;
        }

        MerchantConfigEntity config = findMerchantConfig(merchantId);
        config = null != config ? config : findFirstMerchantConfigByBrand(brandUpper);
        final Optional<MerchantConfigEntity> configOpt = Optional.ofNullable(config);
        final String brandToUse = configOpt.map(MerchantConfigEntity::getBrand).orElse(brand);
        final boolean isTmall = configOpt.map(MerchantConfigEntity::getIsTmall).orElse(false);
        final boolean syncToSap = configOpt.map(MerchantConfigEntity::getIsSyncToSap).orElse(false);
        final boolean syncToBlackline = configOpt.map(MerchantConfigEntity::getIsSyncToBlackline).orElse(false);

        final ZonedDateTime now = ZonedDateTime.now();
        final BillTaskEntity task = BillTaskEntity.builder()
                .brand(brandToUse)
                .channel(channel)
                .merchantId(merchantId)
                .grantType(GrantType.SFTP)
                .billType(billType)
                .billStartTime(when)
                .billEndTime(when)
                .extraParams(sftpPath)
                .traceId(generateTaskTraceId())
                .taskUniqueKey(taskKey)
                .state(TaskState.WAIT_ACQUIRE)
                .source(source)
                .isTmall(isTmall)
                .isSyncToSap(determineSyncToSap(billType, syncToSap))
                .isSyncToBlackline(determineSyncToBlackline(billType, syncToBlackline))
                .sapSyncState(SyncState.UNSET)
                .blacklineSyncState(SyncState.UNSET)
                .errorCount(0)
                .sapErrorCount(0)
                .blacklineErrorCount(0)
                .nextRunTime(now)
                .build();

        if (null == config) {
            task.setState(TaskState.ACQUIRE_FAILED);
            task.setErrorCount(999);
            task.setErrorMsg("SFTP 商户配置不存在");
        }

        EntityUtils.fill(task, now);

        final TransitLogEntity transitLog = EntityUtils.fill(createTransitLog(task), now);

        billTaskDAO.create(doPrepareBillTask(task));
        transitLogDAO.create(transitLog);
        return TaskState.WAIT_ACQUIRE.equals(task.getState()) ? task.getId() : null;
    }

    protected boolean determineSyncToSap(final BillType billType, final boolean udfSyncToSap) {
        log.trace("SKIP sonar: {}", billType);
        return udfSyncToSap;
    }

    protected boolean determineSyncToBlackline(final BillType billType, final boolean udfSyncToBlackline) {
        log.trace("SKIP sonar: {}", billType);
        return udfSyncToBlackline;
    }

    private TransitLogEntity createTransitLog(final BillTaskEntity task) {
        final boolean success = TaskState.WAIT_ACQUIRE.equals(task.getState());
        return TransitLogEntity.builder()
                .traceId(task.getTraceId())
                .title(success ? "任务创建成功" : "任务创建失败")
                .message(success ? "任务状态更新为待拉取" : task.getErrorMsg())
                .build();
    }

    protected BillTaskEntity doPrepareBillTask(final BillTaskEntity entity) {
        return entity;
    }

    private MerchantConfigEntity findMerchantConfig(final String merchantId) {
        return StringUtils.hasText(merchantId)
                ? merchantConfigDAO.findByChannelAndMerchantId(channel, merchantId)
                : null;
    }

    private MerchantConfigEntity findFirstMerchantConfigByBrand(final String brand) {
        return StringUtils.hasText(brand)
                ? merchantConfigDAO.findAnyOneByChannelAndBrand(channel, brand.toUpperCase())
                : null;
    }

    private boolean checkDuplicatedKey(final String taskKey) {
        return billTaskDAO.findExistsTaskUniqueKeys(Collections.singletonList(taskKey)).contains(taskKey);
    }

    protected String getTaskKey(final Channel channel, final String brand, final String merchantId,
                                final BillType billType, final String path, final String source) {
        return String.format("%s_%s_%s_%s_%s_%s", channel.name(), brand, merchantId, billType.name(), path, source);
    }

    protected String generateTaskTraceId() {
        return UUID.randomUUID().toString();
    }

}