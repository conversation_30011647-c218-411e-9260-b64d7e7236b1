package com.kering.cus.finance.billcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

/**
 * Bill task transit log.
 *
 * <AUTHOR>
 * @since 20250702
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("t_transit_log")
public class TransitLogEntity extends SoftDeleteMyBatisBaseEntity<Long> {

    @TableField("trace_id")
    private String traceId;

    @TableField("title")
    private String title;

    @TableField("message")
    private String message;

}
