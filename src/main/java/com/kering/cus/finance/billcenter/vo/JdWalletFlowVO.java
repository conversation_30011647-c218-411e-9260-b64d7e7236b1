package com.kering.cus.finance.billcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kering.cus.finance.billcenter.config.serializer.BigDecimalDeserializer;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class JdWalletFlowVO {
    @JsonProperty(value = "日期")
    private String transactionTime;

    @JsonProperty(value = "账单日期")
    private String billingTime;

    @JsonProperty(value = "流水号")
    private String transactionNo;

    @JsonProperty(value = "账户代码")
    private String accountCode;

    @JsonProperty(value = "账户名称")
    private String accountName;

    @JsonProperty(value = "币种")
    private String currency;

    @JsonProperty(value = "收支类型【IN:收入OUT:支出】")
    private String paymentType;

    @JsonProperty(value = "收入金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal income;

    @JsonProperty(value = "支出金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal expense;

    @JsonProperty(value = "账户余额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal balance;

    @JsonProperty(value = "交易类型")
    private String transactionType;

    @JsonProperty(value = "商户订单号")
    private String merchantOrderId;

    @JsonProperty(value = "原商户订单号")
    private String originMerchantOrderId;

    @JsonProperty(value = "交易备注")
    private String transactionNote;

    @JsonProperty(value = "记账请求号")
    private String voucherNo;

    @JsonProperty(value = "交易订单号")
    private String transactionOrderId;

    @JsonProperty(value = "业务订单号")
    private String bizOrderId;

    private String platformOrderId;


}
