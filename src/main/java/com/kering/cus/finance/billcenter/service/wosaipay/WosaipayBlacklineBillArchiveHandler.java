package com.kering.cus.finance.billcenter.service.wosaipay;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.dao.WosaipayTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.AbstractBlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.CsvWriterUtils;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.util.ExcelWriterUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.kering.cus.finance.billcenter.constant.Constants.BATCH_SIZE;
import static com.kering.cus.finance.billcenter.constant.Constants.DEFAULT_VALUE;


@Slf4j
@Service
public class WosaipayBlacklineBillArchiveHandler extends AbstractBlacklineBillArchiveHandler implements BlacklineBillArchiveHandler {

    @Resource
    private WosaipayTradeFlowDAO wosaipayTradeFlowDAO;

    public WosaipayBlacklineBillArchiveHandler(SftpConfig sftpConfig) {
        super(Channel.WOSAIPAY, sftpConfig);
    }

    @Override
    public boolean matches(Channel channel) {
        return Objects.equals(channel, Channel.WOSAIPAY);
    }

    @Override
    public boolean isGroupingByTmall() {
        return false;
    }

    @Override
    public Map<String,File> archive(Group group, long seqInGroup, List<Long> taskIds) throws IOException {
        log.info("Archive Wosaipay bill for group: {}, seq: {}, taskIds: {}", group, seqInGroup, taskIds);
        Map<String, File> fileMap = new java.util.HashMap<>();
        List<String> fileTypes = Lists.newArrayList(Constants.SUFFIX_XLSX, Constants.SUFFIX_CSV);
        for (String fileType : fileTypes) {
            File targetFile = FileUtils2.createTempFile("wosaipay", fileType).toFile();
            try {
                boolean hasData;
                if(fileType.equals(Constants.SUFFIX_XLSX)){
                    ExcelWriterUtils excelWriterUtils=new ExcelWriterUtils();
                    excelWriterUtils.createSheet("Wosaipay_Blackline").writeHeader(getHeaders());
                    hasData=createFile(taskIds, null, excelWriterUtils);
                    excelWriterUtils.writeTo(targetFile);
                }else{
                    try (CsvWriterUtils writer = new CsvWriterUtils(targetFile)) {
                        writer.writeHeader(getHeaders());
                        hasData = createFile(taskIds, writer, null);
                    }
                }
                if (!hasData) {
                    log.warn("no wosaipay bill,taskIds ：{} ", taskIds);
                    FileUtils.deleteQuietly(targetFile);
                }else{
                    fileMap.put(fileType, targetFile);
                }
            }catch (Exception e) {
                log.error("Archive Wosaipay bill failed,taskIds ：{} ", taskIds, e);
                FileUtils.deleteQuietly(targetFile);
                throw new BillCenterBusinessException(BillCenterErrorCode.BILL_BLACKLINE_ARCHIVE_FAILED,e);
            }

        }
        return fileMap;
    }



    private boolean createFile(List<Long> taskIds,CsvWriterUtils writer, ExcelWriterUtils excelWriterUtils) throws IOException {
        Page<WosaipayTradeFlowEntity> batch;
        int pageNum=1;
        boolean hasData = false;
        do {
            batch = wosaipayTradeFlowDAO.findWosaipayBillByTaskIds(taskIds, BATCH_SIZE, pageNum);
            if (!CollectionUtils.isEmpty(batch.getRecords())) {
                List<String[]> list = batch.getRecords().stream().map(this::getLines).toList();
                if(Objects.nonNull(writer)){
                    writer.writeBatch(list);
                }else{
                    excelWriterUtils.writeRows(list);
                }
                hasData=true;
            }
            pageNum++;
        } while (batch.hasNext());
        return hasData;
    }

    private String[] getHeaders() {
        return new String[]{"Brand", "Channel", "Settlement Merchant ID", "Merchant Name", "Merchant ID", "Store Name",
                "Store Number", "Merchant Store Number", "Transaction Date", "Transaction Time", "Merchant Order Number", "Merchant Serial Number",
                "Light POS Order Number", "Light POS Serial Number",
                "Light POS Original Order Number", "Light POS Original Serial Number", "Shouqianba Order Number", "Payment Channel",
                "Payment Channel Merchant ID", "Payment Channel Order Number", "Product Name", "Transaction Type", "Transaction Mode", "Order Source",
                "Transaction Scenario", "Transaction Status", "Payment Account", "Currency Type", "Transaction Amount", "Shouqianba Merchant Discount",
                "Shouqianba Merchant Discount Type", "Shouqianba Subsidy Discount",
                "Payment Channel Institution Discount", "Payment Channel Merchant Prepaid Discount", "Payment Channel Merchant Non-prepaid Discount",
                "Consumer's Actual Payment Amount", "Deduction Rate (%)", "Handling Fee", "Actual Received Amount", "Installment Periods",
                "Installment Fee Rate", "Installment Handling Fee", "Allocation Amount", "Settlement Amount", "Terminal Number",
                "Merchant Terminal Number", "Terminal Name", "Terminal Type", "Device Number", "Operator", "Cashier", "Remarks", "Extension 1",
                "Extension 2", "Business Order Number", "Business Remarks",
                "Security Deposit", "Original Transaction Serial Number of Merchant in External System", "Reserved Field 3"};
    }

    private String[] getLines(WosaipayTradeFlowEntity entity) {
        return new String[]{
                StringUtils.defaultString(entity.getBrand()),
                StringUtils.defaultString(entity.getChannel()),
                StringUtils.defaultString(entity.getMerchantId()),
                StringUtils.defaultString(entity.getMerchantName()),
                StringUtils.defaultString(entity.getMerchantId()),
                StringUtils.defaultString(entity.getStoreName()),
                StringUtils.defaultString(entity.getStoreCode()),
                StringUtils.defaultString(entity.getMerchantStoreCode()),
                DateUtils.convertDateStr(entity.getTransactionDate(), DateUtils.YYYYMMDD),
                StringUtils.defaultString(entity.getTransactionTime()),
                StringUtils.defaultString(entity.getMerchantOrderId()),
                StringUtils.defaultString(entity.getMerchantTransactionNo()),
                StringUtils.defaultString(entity.getPosOrderId()),
                StringUtils.defaultString(entity.getPosTransactionNo()),
                StringUtils.defaultString(entity.getPosOriginOrderId()),
                StringUtils.defaultString(entity.getPosOriginTransactionNo()),
                StringUtils.defaultString(entity.getWosaypayOrderId()),
                StringUtils.defaultString(entity.getPaymentChannel()),
                StringUtils.defaultString(entity.getPaymentChannelMerchantId()),
                StringUtils.defaultString(entity.getPaymentChannelOrderId()),
                StringUtils.defaultString(entity.getSkuName()),
                StringUtils.defaultString(entity.getTransactionType()),
                StringUtils.defaultString(entity.getTradeMode()),
                StringUtils.defaultString(entity.getOrderSource()),
                StringUtils.defaultString(entity.getOrderSence()),
                StringUtils.defaultString(entity.getTradeStatus()),
                StringUtils.defaultString(entity.getPaymentAccount()),
                StringUtils.defaultString(entity.getCurrency()),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getTransactionAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getMerchantDiscountAmount(), DEFAULT_VALUE),
                StringUtils.defaultString(entity.getMerchantDiscountType()),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getSubsidyAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getPaymentChannelDiscountAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getRechargeDiscountAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getNonRechargeDiscountAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getActualPaidAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getTransactionRate(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getServiceCharge(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getActualRecvAmount(), DEFAULT_VALUE),
                String.valueOf(entity.getInstallmentNum()),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getInstallmentFeeRate(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getInstallmentFee(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getSplitAmount(), DEFAULT_VALUE),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getSettlementAmount(), DEFAULT_VALUE),
                StringUtils.defaultString(entity.getTermNo()),
                StringUtils.defaultString(entity.getMerchantTermNo()),
                StringUtils.defaultString(entity.getTermName()),
                StringUtils.defaultString(entity.getTermType()),
                StringUtils.defaultString(entity.getDeviceNo()),
                StringUtils.defaultString(entity.getOperator()),
                StringUtils.defaultString(entity.getCashier()),
                StringUtils.defaultString(entity.getNote()),
                StringUtils.defaultString(entity.getExt1()),
                StringUtils.defaultString(entity.getExt2()),
                StringUtils.defaultString(entity.getBizOrderId()),
                StringUtils.defaultString(entity.getBizNote()),
                com.kering.cus.finance.billcenter.util.StringUtils.bigDecimalToString(entity.getDeposit(), DEFAULT_VALUE),
                StringUtils.defaultString(entity.getOuterOriginTransactionNo()),
                StringUtils.defaultString(entity.getReserve3())
        };
    }

    @Override
    protected String getBlacklineSftpTargetDirectory() {
        return sftpConfig.getBlacklineSftpWosaipayArchiveDirectory();
    }
}
