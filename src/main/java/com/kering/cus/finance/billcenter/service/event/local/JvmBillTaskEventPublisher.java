package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;

@Profile("local")
@Component
@RequiredArgsConstructor
public class JvmBillTaskEventPublisher implements BillTaskEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public void publishWaitAcquireEvent(final Long taskId) {
        eventPublisher.publishEvent(new JvmBillTaskEvent(taskId, TaskState.WAIT_ACQUIRE));
    }

    @Override
    public void publishWaitAcquireEvent(final List<Long> taskIds) {
        taskIds.forEach(this::publishWaitAcquireEvent);
    }

    @Override
    public void publishWaitProcessEvent(Long taskId) {
        eventPublisher.publishEvent(new JvmBillTaskEvent(taskId, TaskState.WAIT_PROCESS));
    }

    @Override
    public void publishWaitProcessEvent(List<Long> taskIds) {
        taskIds.forEach(this::publishWaitProcessEvent);
    }

}
