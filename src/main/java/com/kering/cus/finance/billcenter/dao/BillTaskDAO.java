package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dto.BillTaskDTO;
import com.kering.cus.finance.billcenter.dto.BillTaskQueryDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.mapper.BillTaskMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Repository
public class BillTaskDAO extends MyBatisBaseDAO<BillTaskEntity, BillTaskMapper, Long> {

  private static final String NEXT_RUN_TIME = "next_run_time";
  private static final String SAP_NEXT_RUN_TIME = "sap_next_run_time";

  public List<String> findExistsTaskUniqueKeys(final List<String> taskKeys) {
    if (CollectionUtils.isEmpty(taskKeys)) {
      return Collections.emptyList();
    }
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .in(BillTaskEntity::getTaskUniqueKey, taskKeys)
        .eq(BillTaskEntity::getDeleted, false)
        .select(BillTaskEntity::getTaskUniqueKey);
    return entityMapper.selectObjs(query);
  }

  public List<BillTaskEntity> findWaitSyncToSapTasksByChannel(final Channel channel,
      final ZonedDateTime since,
      final ZonedDateTime until,
      final int maxRetryTimes, final int limit) {
    final Page<BillTaskEntity> pageCriteria = Page.<BillTaskEntity>of(1, limit)
        .addOrder(OrderItem.asc(SAP_NEXT_RUN_TIME));
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getChannel, channel)
        .eq(BillTaskEntity::getDeleted, false)
        .eq(BillTaskEntity::getIsSyncToSap, true)
        .in(BillTaskEntity::getSapSyncState, SyncState.WAITING, SyncState.FAILED, SyncState.RUNNING)
        .lt(BillTaskEntity::getSapErrorCount, maxRetryTimes)
        .ge(BillTaskEntity::getSapNextRunTime, since)
        .le(BillTaskEntity::getSapNextRunTime, until);
    return entityMapper.selectList(pageCriteria, query);
  }

  public int updateNextRunTimeByIdAndVersion(final ZonedDateTime nextRunTime,
      final Long taskId, final Long version) {
    final BillTaskEntity patch = new BillTaskEntity();
    patch.setId(taskId);
    patch.setVersion(version);

    patch.setNextRunTime(nextRunTime);
    return entityMapper.updateById(patch);
  }

  public int updateToFailedByIdAndVersion(final TaskState taskState,
      final int errorCount, final String errorMsg,
      final ZonedDateTime nextRunTime,
      final Long taskId, final Long version) {
    final BillTaskEntity patch = new BillTaskEntity();
    patch.setId(taskId);
    patch.setVersion(version);

    patch.setState(taskState);
    patch.setErrorCount(errorCount);
    patch.setErrorMsg(errorMsg);
    patch.setNextRunTime(nextRunTime);
    return entityMapper.updateById(patch);
  }

  public int updateToAcquireSuccessByIdAndVersion(final String originalBillUrl,
      final ZonedDateTime nextRunTime,
      final Long taskId, final long version) {
    final BillTaskEntity patch = new BillTaskEntity();
    patch.setId(taskId);
    patch.setVersion(version);

    patch.setState(TaskState.WAIT_PROCESS);
    patch.setErrorCount(0);
    patch.setErrorMsg("OK");
    patch.setNextRunTime(nextRunTime);
    patch.setOriginBillUrl(originalBillUrl);
    return entityMapper.updateById(patch);
  }

  public int updateToSyncSkipByIdAndVersion(final String message, final Long taskId, final long version) {
    final BillTaskEntity patch = new BillTaskEntity();
    patch.setId(taskId);
    patch.setVersion(version);

    patch.setState(TaskState.SYNC_SKIP);
    patch.setSapSyncState(SyncState.SKIP);
    patch.setBlacklineSyncState(SyncState.SKIP);
    patch.setErrorCount(0);
    patch.setErrorMsg(message);
    patch.setErrorMsg("OK");
    patch.setOriginBillUrl(null);
    return entityMapper.updateById(patch);
  }

  @SuppressWarnings("java:S107")
  public int updateMerchantIdTaskStateAndSyncStateByIdAndVersion(final String merchantId,
      final TaskState state,
      final SyncState sapSyncState,
      final SyncState blacklineSyncState,
      final ZonedDateTime nextRunTime,
     final BillTaskEntity task) {
    final BillTaskEntity patch = new BillTaskEntity();
    patch.setId(task.getId());
    patch.setVersion(task.getVersion());
    patch.setBusinessDate(task.getBusinessDate());
    patch.setState(state);
    patch.setSapSyncState(sapSyncState);
    patch.setBlacklineSyncState(blacklineSyncState);
    patch.setErrorCount(0);
    patch.setErrorMsg("OK");
    patch.setNextRunTime(nextRunTime);
    patch.setSapErrorCount(0);
    patch.setSapNextRunTime(nextRunTime);
    patch.setBlacklineErrorCount(0);
    patch.setBlacklineNextRunTime(nextRunTime);
    patch.setMerchantId(merchantId);
    return entityMapper.updateById(patch);
  }

  public int updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(final SyncState syncState,
      final ZonedDateTime nextRunTime,
      final Long taskId, final List<SyncState> syncStatus) {
    return entityMapper.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(syncState, nextRunTime, taskId, syncStatus);
  }

  public int updateSapSyncStateErrorAndNextRunTimeByIdAndSyncStatus(final SyncState syncState,
      final int errorCount,
      final String errorMsg,
      final ZonedDateTime nextRunTime,
      final Long taskId, final List<SyncState> syncStatus) {
    final BillTaskDTO parmas = BillTaskDTO.builder().newSyncState(syncState).errorCount(errorCount)
        .errorMsg(errorMsg).taskId(taskId).syncStatus(syncStatus).nextRunTime(nextRunTime).build();
    return updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus(parmas);
  }


  public int updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus(final BillTaskDTO billTaskDTO) {
    return entityMapper.updateSapSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncStatus(billTaskDTO);
  }

  public long countSapSyncSuccessTasksByChannelMerchantIdAndCreatedDate(final Channel channel,
      final String merchantId,
      final ZonedDateTime startTime,
      final ZonedDateTime endTime) {
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getDeleted, false)
        .eq(BillTaskEntity::getChannel, channel)
        .eq(BillTaskEntity::getMerchantId, merchantId)
        .eq(BillTaskEntity::getSapSyncState, SyncState.SUCCESS)
        .ge(BillTaskEntity::getCreatedDate, startTime)
        .le(BillTaskEntity::getCreatedDate, endTime);
    return entityMapper.selectCount(query);
  }

  public List<BillTaskEntity> findWaitAcquireTasks(final ZonedDateTime since,
      final ZonedDateTime until,
      final int maxRetryTimes, final int limit) {
    final Page<BillTaskEntity> pageCriteria = Page.<BillTaskEntity>of(1, limit)
        .addOrder(OrderItem.asc(NEXT_RUN_TIME));
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getDeleted, false)
        .in(BillTaskEntity::getState, TaskState.WAIT_ACQUIRE, TaskState.ACQUIRE_FAILED)
        .lt(BillTaskEntity::getErrorCount, maxRetryTimes)
        .ge(BillTaskEntity::getNextRunTime, since)
        .le(BillTaskEntity::getNextRunTime, until);
    return entityMapper.selectList(pageCriteria, query);
  }

  public List<BillTaskEntity> findWaitProcessTasks(final ZonedDateTime since,
      final ZonedDateTime until,
      final int maxRetryTimes, final int limit) {
    final Page<BillTaskEntity> pageCriteria = Page.<BillTaskEntity>of(1, limit)
        .addOrder(OrderItem.asc(NEXT_RUN_TIME));
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getDeleted, false)
        .in(BillTaskEntity::getState, TaskState.WAIT_PROCESS, TaskState.PROCESS_FAILED)
        .lt(BillTaskEntity::getErrorCount, maxRetryTimes)
        .ge(BillTaskEntity::getNextRunTime, since)
        .le(BillTaskEntity::getNextRunTime, until);
    return entityMapper.selectList(pageCriteria, query);
  }

  public List<BillTaskEntity> findBlacklineTasksByChannelAndCreateTime(final Channel channel,
      final ZonedDateTime since,
      final ZonedDateTime until) {
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getChannel, channel)
        .eq(BillTaskEntity::getDeleted, false)
        .eq(BillTaskEntity::getIsSyncToBlackline, true)
        .ge(BillTaskEntity::getCreatedDate, since)
        .le(BillTaskEntity::getCreatedDate, until);
    return entityMapper.selectList(query);
  }

  public int updateBlacklineSyncStateByIdAndSyncStatus(final SyncState newSyncState,
      final List<Long> taskIds, final List<SyncState> oldSyncStatus) {
    return entityMapper.updateBlacklineSyncStateByIdAndSyncStatus(newSyncState, taskIds, oldSyncStatus);
  }

  public int updateBlacklineSyncStateErrorNextRunTimeAndArchiveUrlByIdAndSyncState(final BillTaskDTO billTaskDTO) {
    return entityMapper.updateBlacklineSyncStateErrorAndArchiveUrlByIdAndSyncState(billTaskDTO);
  }

  public int updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(final TaskState taskState,
      final ZonedDateTime finishTime,
      final List<SyncState> sapSyncStatus,
      final List<SyncState> blacklineSyncStatus,
      final ZonedDateTime since,
      final ZonedDateTime until,
      final List<TaskState> ignoreTaskStatus) {
    return entityMapper.updateTaskStateAndFinishedDateBySapSyncStatusBlacklineSyncStatusAndCreateTime(
        taskState, finishTime, sapSyncStatus, blacklineSyncStatus, since, until, ignoreTaskStatus
    );
  }


  public Page<BillTaskEntity> findTasksByStatus(final LocalDate date,
      final TaskState state,
      final SyncState syncState, int pageSize, int pageNum) {
    final LambdaQueryWrapper<BillTaskEntity> query = Wrappers.<BillTaskEntity>lambdaQuery()
        .eq(BillTaskEntity::getDeleted, false)
        .ge(BillTaskEntity::getCreatedDate, date);
    if (Objects.nonNull(state)) {
      query.eq(BillTaskEntity::getState, state);
    }
    if (Objects.nonNull(syncState)) {
      query.eq(BillTaskEntity::getSapSyncState, syncState)
          .eq(BillTaskEntity::getIsSyncToSap, true);
    }
    return entityMapper.selectPage(new Page<>(pageNum, pageSize), query);
  }


  public List<BillTaskEntity> findTasksByBlacklineStatus(final LocalDate date, final SyncState state) {
    return entityMapper.findTasksByBlacklineStatus(date, state);
  }

  /**
   * Query bill tasks with pagination
   *
   * @param page     pagination parameter
   * @param queryDTO query conditions
   * @return paginated result
   */
  public Page<BillTaskEntity> queryTasks(Page<BillTaskEntity> page, BillTaskQueryDTO queryDTO) {
    LambdaQueryWrapper<BillTaskEntity> query = new LambdaQueryWrapper<>();

    if (!CollectionUtils.isEmpty(queryDTO.getChannels())) {
      query.in(BillTaskEntity::getChannel, queryDTO.getChannels());
    }

    if (queryDTO.getMerchantId() != null && !queryDTO.getMerchantId().isEmpty()) {
      query.like(BillTaskEntity::getMerchantId, queryDTO.getMerchantId());
    }

    if (!CollectionUtils.isEmpty(queryDTO.getBrands())) {
      query.in(BillTaskEntity::getBrand, queryDTO.getBrands());
    }

    if (queryDTO.getBillType() != null && !queryDTO.getBillType().isEmpty()) {
      query.eq(BillTaskEntity::getBillType, queryDTO.getBillType());
    }

    if (queryDTO.getStatus() != null && !queryDTO.getStatus().isEmpty()) {
      query.eq(BillTaskEntity::getState, queryDTO.getStatus());
    }

    if (queryDTO.getCreatedDateStart() != null) {
      query.ge(BillTaskEntity::getCreatedDate, queryDTO.getCreatedDateStart());
    }

    if (queryDTO.getCreatedDateEnd() != null) {
      query.lt(BillTaskEntity::getCreatedDate, queryDTO.getCreatedDateEnd());
    }

    if (StringUtils.hasText(queryDTO.getBusinessDateStart())) {
      query.ge(BillTaskEntity::getBusinessDate, queryDTO.getBusinessDateStart());
    }
    if (StringUtils.hasText(queryDTO.getBusinessDateEnd())) {
      query.lt(BillTaskEntity::getBusinessDate, queryDTO.getBusinessDateEnd());
    }

    query.eq(BillTaskEntity::getDeleted, false);
    query.orderByDesc(BillTaskEntity::getCreatedDate);
    return entityMapper.selectPage(page, query);
  }
}

