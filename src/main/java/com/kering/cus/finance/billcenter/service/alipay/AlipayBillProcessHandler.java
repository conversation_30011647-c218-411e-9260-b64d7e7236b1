package com.kering.cus.finance.billcenter.service.alipay;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kering.cus.finance.billcenter.config.AlipayBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.AlipayBillConverter;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import com.kering.cus.finance.billcenter.util.ZipUtils;
import com.kering.cus.finance.billcenter.vo.AlipayBillFlowVO;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.time.ZonedDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;


@Slf4j
@Service
public class AlipayBillProcessHandler implements BillProcessHandler {

    @Resource
    private AlipayBillFlowDAO alipayBillFlowDAO;
    @Resource
    private AlipayBillConverter alipayBillConverter;

    private static final Integer BATCH_SIZE = 100;


    public static final String SEPARATOR_P = "P";
    @Resource
    private AlipayBillConfig alipayBillConfig;
    @Resource
    private TransactionTemplate transactionTemplate;


    @Override
    public boolean matches(Channel channel, GrantType grantType, BillType billType) {
        return Objects.equals(channel.getName(), Channel.ALIPAY.getName()) && Objects.equals(grantType, GrantType.ISV);
    }

    @Override
    public List<String> process(File tempFile, BillTaskEntity task) {

        if (!Objects.equals(task.getChannel().getName(), Channel.ALIPAY.getName())) {
            return Collections.emptyList();
        }
        log.info("Alipay bill processing starts,merchantId :{}", task.getMerchantId());
        if (!ZipUtils.isZipFile(tempFile)) {
            log.warn("The format of Alipay bill file is incorrect,filename :{},merchantId : {}", tempFile.getName(), task.getMerchantId());
            return Collections.emptyList();
        }
        try {
            Path safePath = getSafePath();
            ZipUtils.unzip(tempFile, safePath, Charset.forName(Constants.CHARSET_GBK));
            try (Stream<Path> stream = Files.walk(safePath)) {
                stream.filter(Files::isRegularFile)
                        .filter(path -> isBillsFile(path.getFileName().toString())).forEach(path -> parsingBillFile(task, path));
            }
            FileUtils.deleteDirectory(safePath.toFile());
        }catch (Exception e){
            log.error("Alipay bill processing error", e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED,e);
        }
        log.info("Alipay bill processing completed,merchantId :{}", task.getMerchantId());
        return Collections.emptyList();
    }

    @Override
    public void determineBusinessDate(BillTaskEntity task) {
        task.setBusinessDate(task.getBillStartTime());
    }


    private void parsingBillFile(BillTaskEntity task, Path path) {
        List<AlipayBillFlowEntity> alipaybillList = Lists.newArrayList();
        Set<String> accountNos = Sets.newHashSet();

        try (CsvReaderUtils reader = CsvReaderUtils.builder().headerLine(4).charset(Charset.forName(Constants.CHARSET_GBK)).build()) {
            reader.open(path);
            reader.forEach(row -> {
                AlipayBillFlowVO alipayBillFlowVO = JsonUtil.convertValue(parseAndFormatMap(row), AlipayBillFlowVO.class);
                if (org.springframework.util.StringUtils.hasText(alipayBillFlowVO.getAccountTransactionNo())
                        && alipayBillFlowVO.getAccountTransactionNo().startsWith(Constants.SUFFIX_BILL_START)) {
                    return;
                }
                if (!accountNos.contains(alipayBillFlowVO.getAccountTransactionNo())) {
                    //Remove duplicates and add in bulk
                    accountNos.add(alipayBillFlowVO.getAccountTransactionNo());
                    analyzeAccountTransactionNo(task.getIsTmall(), alipayBillFlowVO);
                    alipaybillList.add(alipayBillConverter.toAlipayBillFlowEntity(task, alipayBillFlowVO));
                }
                if (alipaybillList.size() == BATCH_SIZE) {
                    saveAlipayBillList(alipaybillList);
                    alipaybillList.clear();
                }
            });
            if (!CollectionUtils.isEmpty(alipaybillList)) {
                saveAlipayBillList(alipaybillList);
            }
        } catch (IOException e) {
            log.error("reader alipay file error", e);
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_PROCESS_FAILED,e);
        }
    }

    private boolean isBillsFile(String fileName) {
        return fileName.contains(alipayBillConfig.getBillsSuffix());
    }

    private void saveAlipayBillList(List<AlipayBillFlowEntity> alipaybillList) {
        transactionTemplate.execute(status -> {
            try {
                List<String> accountNos = alipaybillList.stream().map(AlipayBillFlowEntity::getAccountTransactionNo).toList();
                List<String> accountTransactionNoList = alipayBillFlowDAO.findByAccountTransactionNos(accountNos, alipaybillList.get(0).getMerchantId());
                List<AlipayBillFlowEntity> list = alipaybillList.stream().filter(e -> !accountTransactionNoList.contains(e.getAccountTransactionNo())).toList();
                if (!CollectionUtils.isEmpty(list)) {
                    alipayBillFlowDAO.createBatch(list);
                }
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("save alipay bill error", e);
                throw new IllegalStateException(e);
            }
        });

    }


    private void analyzeAccountTransactionNo(Boolean isTmall, AlipayBillFlowVO alipayBillFlowVO) {
        List<String> list = Arrays.asList(alipayBillConfig.getTransactionTypes().split(Constants.SYMBOL_COMMA));
        if (list.contains(alipayBillFlowVO.getTransactionType()) &&
               !org.springframework.util.StringUtils.hasText(alipayBillFlowVO.getBizBaseOrderId())
                && org.springframework.util.StringUtils.hasText(alipayBillFlowVO.getMerchantOrderId())
                && (alipayBillFlowVO.getMerchantOrderId().startsWith("T200P")
                || alipayBillFlowVO.getMerchantOrderId().startsWith("T10000P"))
                &&  Boolean.TRUE.equals(isTmall)) {
            String bizBaseOrderId = alipayBillFlowVO.getMerchantOrderId().
                    substring(alipayBillFlowVO.getMerchantOrderId().indexOf(SEPARATOR_P) + 1);
            alipayBillFlowVO.setBizBaseOrderId(bizBaseOrderId);


        }
    }


    private Map<String, String> parseAndFormatMap(Map<String, String> map) {
        Map<String, String> headMap = Maps.newHashMap();
        map.forEach((k, v) -> headMap.put(StringUtils.trimAndRemoveSpecialChars(k), StringUtils.trimAndRemoveSpecialChars(v)));
        return headMap;

    }


    private Path getSafePath() throws IOException {
        Path safeBaseDir = Paths.get(System.getProperty("java.io.tmpdir"), "alipay").toAbsolutePath().normalize();
        Path targetPath = safeBaseDir.resolve(UUID.randomUUID().toString()).normalize();
        if(!Files.exists(targetPath)){
            Files.createDirectories(targetPath);
        }
        return targetPath;
    }

}
