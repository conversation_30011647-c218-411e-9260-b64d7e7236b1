package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;

import java.io.File;
import java.io.IOException;
import java.util.Map;

public interface SapBillArchiveHandler {

    boolean matches(final Channel channel, final BillType billType);

    /**
     * Archive data to file.
     *
     * @param taskId the task id
     * @param config the merchant config
     * @return null if no data, otherwise data written file
     * @throws IOException if io error occurs
     */
    Map<String,File> archive(final Long taskId, final MerchantConfigEntity config) throws IOException;

    /**
     * Send archive file to SAP.
     *
     * @param filename   the file name
     * @param targetFile the archive file
     * @param config     the merchant config
     * @throws IOException if io error occurs
     */
    void sendToSap(final String filename, final File targetFile, final MerchantConfigEntity config) throws IOException;

}
