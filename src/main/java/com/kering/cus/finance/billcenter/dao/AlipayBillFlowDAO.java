package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.mapper.AlipayBillFlowMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Repository
public class AlipayBillFlowDAO extends MyBatisBaseDAO<AlipayBillFlowEntity, AlipayBillFlowMapper, Long> {

   public List<String> findByAccountTransactionNos( List<String> accountTransactionNos,
                                                         String merchantId){
       if(CollectionUtils.isEmpty(accountTransactionNos)){
           return Collections.emptyList();
       }
       LambdaQueryWrapper<AlipayBillFlowEntity> query = Wrappers.<AlipayBillFlowEntity>lambdaQuery()
               .in(AlipayBillFlowEntity::getAccountTransactionNo, accountTransactionNos)
               .eq(AlipayBillFlowEntity::getMerchantId, merchantId)
               .eq(AlipayBillFlowEntity::getDeleted,false)
               .select(AlipayBillFlowEntity::getAccountTransactionNo);
       return entityMapper.selectObjs(query);

   }



    public List<AlipayBillFlowEntity> findAlipayBillByParams(String merchantId, Long taskId, String brand){
        final LambdaQueryWrapper<AlipayBillFlowEntity> query = Wrappers.<AlipayBillFlowEntity>lambdaQuery()
                .eq(AlipayBillFlowEntity::getMerchantId,merchantId)
                .eq(AlipayBillFlowEntity::getTaskId,taskId)
                .eq(AlipayBillFlowEntity::getDeleted, false)
                .eq(AlipayBillFlowEntity::getBrand,brand)
                .eq(AlipayBillFlowEntity::getIsSyncToSap,true);
        return entityMapper.selectList(query);
    }


    public Page<AlipayBillFlowEntity> findAlipayBillByParams(List<Long> taskIds, Boolean isTmall,int pageSize,int pageNum){
        final LambdaQueryWrapper<AlipayBillFlowEntity> query = Wrappers.<AlipayBillFlowEntity>lambdaQuery()
                .in(AlipayBillFlowEntity::getTaskId,taskIds)
                .eq(AlipayBillFlowEntity::getDeleted, false)
                .eq(AlipayBillFlowEntity::getIsSyncToBlackline,true)
                .eq(AlipayBillFlowEntity::getIsTmall,isTmall);
        return entityMapper.selectPage(new Page<>(pageNum, pageSize), query);
    }


}
