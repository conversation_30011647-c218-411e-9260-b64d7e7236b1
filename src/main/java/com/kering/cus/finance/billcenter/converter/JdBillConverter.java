package com.kering.cus.finance.billcenter.converter;

import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.vo.JdTradeFlowVO;
import com.kering.cus.finance.billcenter.vo.JdWalletFlowVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Mapper(componentModel = "spring", imports = {StringUtils.class, Date.class, Optional.class, BigDecimal.class, Objects.class})
public abstract class JdBillConverter {

    @Mapping(target = "taskId", source = "task.id")
    @Mapping(target = "brand", source = "task.brand")
    @Mapping(target = "channel", source = "task.channel")
    @Mapping(target = "merchantId", source = "task.merchantId")
    @Mapping(target = "isSyncToSap", source = "task.isSyncToSap")
    @Mapping(target = "transactionTime", source = "billFlow.transactionTime")
    @Mapping(target = "billingTime", source = "billFlow.billingTime")
    @Mapping(target = "transactionNo", source = "billFlow.transactionNo")
    @Mapping(target = "accountCode", source = "billFlow.accountCode")
    @Mapping(target = "accountName", source = "billFlow.accountName")
    @Mapping(target = "currency", source = "billFlow.currency")
    @Mapping(target = "paymentType", source = "billFlow.paymentType")
    @Mapping(target = "income", source = "billFlow.income")
    @Mapping(target = "expense", expression = "java(Optional.ofNullable(billFlow).map(JdWalletFlowVO::getExpense).map(BigDecimal::negate).orElse(null))")
    @Mapping(target = "balance", source = "billFlow.balance")
    @Mapping(target = "transactionType", source = "billFlow.transactionType")
    @Mapping(target = "originMerchantOrderId", source = "billFlow.originMerchantOrderId")
    @Mapping(target = "transactionNote", source = "billFlow.transactionNote")
    @Mapping(target = "voucherNo", source = "billFlow.voucherNo")
    @Mapping(target = "transactionOrderId", source = "billFlow.transactionOrderId")
    @Mapping(target = "bizOrderId", source = "billFlow.bizOrderId")
    @Mapping(target = "platformOrderId", source = "billFlow.platformOrderId")
    @Mapping(target = "isAfsOrder", expression = "java(Optional.ofNullable(billFlow).map(JdWalletFlowVO::getPlatformOrderId).map(StringUtils::hasText).orElse(false))")
    public abstract JdWalletFlowEntity toJdWalletFlowEntity(BillTaskEntity task, JdWalletFlowVO billFlow);



    @Mapping(target = "taskId", source = "task.id")
    @Mapping(target = "brand", source = "task.brand")
    @Mapping(target = "channel", source = "task.channel")
    @Mapping(target = "merchantId", source = "task.merchantId")
    @Mapping(target = "isSyncToBlackline", source = "task.isSyncToBlackline")
    @Mapping(target = "isSyncToSap", source = "task.isSyncToSap")
    @Mapping(target = "orderId", source = "billFlow.orderId")
    @Mapping(target = "transactionNo", source = "billFlow.transactionNo")
    @Mapping(target = "transactionType", source = "billFlow.transactionType")
    @Mapping(target = "skuCode", source = "billFlow.skuCode")
    @Mapping(target = "merchantOrderId", source = "billFlow.merchantOrderId")
    @Mapping(target = "skuName", source = "billFlow.skuName")
    @Mapping(target = "settlementStatus", source = "billFlow.settlementStatus")
    @Mapping(target = "occurTime", source = "billFlow.occurTime")
    @Mapping(target = "chargeableTime", source = "billFlow.chargeableTime")
    @Mapping(target = "settlementTime", source = "billFlow.settlementTime")
    @Mapping(target = "feeItem", source = "billFlow.feeItem")
    @Mapping(target = "amount", source = "billFlow.amount")
    @Mapping(target = "currency", source = "billFlow.currency")
    @Mapping(target = "merchantPaymentType", source = "billFlow.merchantPaymentType")
    @Mapping(target = "settlementNote", source = "billFlow.settlementNote")
    @Mapping(target = "shopCode", source = "billFlow.shopCode")
    @Mapping(target = "jdStoreCode", source = "billFlow.jdStoreCode")
    @Mapping(target = "brandStoreCode", source = "billFlow.brandStoreCode")
    @Mapping(target = "storeName", source = "billFlow.storeName")
    @Mapping(target = "note", source = "billFlow.note")
    @Mapping(target = "paymentType", source = "billFlow.paymentType")
    @Mapping(target = "skuQty", source = "billFlow.skuQty")
    @Mapping(target = "billingDate", source = "billFlow.billingDate")
    public abstract JdTradeFlowEntity toJdTradeFlowEntity(BillTaskEntity task, JdTradeFlowVO billFlow);

}
