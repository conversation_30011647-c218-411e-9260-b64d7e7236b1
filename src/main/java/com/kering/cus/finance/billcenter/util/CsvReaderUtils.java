package com.kering.cus.finance.billcenter.util;

import org.springframework.util.CollectionUtils;


import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Features:
 * 1. Supports specifying the header line (0-based index)
 * 2. Supports streaming processing for large files (not loaded into memory all at once)
 * 3. Supports defining the column index to stop reading (0-based index)
 * 4. Returns data in Map<String, String> format (key is the header name, value is the corresponding field)
 * 5. Supports customizable separators, quote characters, and character encodings
 * 6. Automatically handles quoted fields and escape characters
 */
public class CsvReaderUtils implements AutoCloseable{


    private final char separator;
    private final char quote;
    private final Charset charset;
    private final int headerLine;
    private final boolean trimValues;
    private final boolean ignoreEmptyLines;

    private BufferedReader reader;
    private String[] headers;
    private long currentLineNumber;

    /**
     * Creates an instance using the builder pattern
     */
    public static Builder builder() {
        return new Builder();
    }

    private CsvReaderUtils(Builder builder) {
        this.separator = builder.separator;
        this.quote = builder.quote;
        this.charset = builder.charset;
        this.headerLine = builder.headerLine;
        this.trimValues = builder.trimValues;
        this.ignoreEmptyLines = builder.ignoreEmptyLines;
    }

    /**
     * Opens the CSV file and initializes the reader
     */
    public void open(Path path) throws IOException {
        this.reader =new BufferedReader(new InputStreamReader(
                new FileInputStream(path.toFile()), charset));
        this.currentLineNumber = 0;

        locateHeaderLine();
    }

    /**
     /**
      * Locates the header line and reads it
      */
    private void locateHeaderLine() throws IOException {
        String line;
        while (currentLineNumber <= headerLine && (line = reader.readLine()) != null) {
            if (currentLineNumber == headerLine) {
                headers = parseLine(line);
                if (trimValues) {
                    headers = Arrays.stream(headers)
                            .map(String::trim)
                            .toArray(String[]::new);
                }
            }
            currentLineNumber++;
        }

        if (headers == null) {
            throw new IOException("File does not contain header line " + headerLine);
        }
    }

    /**
     /**
      * Reads the next row of data (returns in Map format)
      */
    public Map<String, String> readNext() throws IOException {
        if (reader == null) {
            throw new IllegalStateException("CSV file not opened. Call open() first.");
        }

        String line;
        while ((line = reader.readLine()) != null) {
            currentLineNumber++;

            if (ignoreEmptyLines && line.trim().isEmpty()) {
                continue;
            }

            String[] values = parseLine(line);
            if (trimValues) {
                values = Arrays.stream(values)
                        .map(String::trim)
                        .toArray(String[]::new);
            }

            return createRowMap(values);
        }

        return Collections.emptyMap();
    }

    /**
     * Processes the entire file in a streaming manner
     */
    public void forEach(Consumer<Map<String, String>> consumer) throws IOException {
        Map<String, String> row;
        while (!CollectionUtils.isEmpty((row = readNext()))) {
            consumer.accept(row);
        }
    }

    /**
     * Converts the value array to a Map (using headers as keys)
     */
    private Map<String, String> createRowMap(String[] values) {
        Map<String, String> rowMap = new LinkedHashMap<>();
        for (int i = 0; i < Math.min(headers.length, values.length); i++) {
            rowMap.put(headers[i], values[i]);
        }
        return rowMap;
    }



    private String[] parseLine(final String line) {
        // Input validation
        if (line == null) {
            throw new IllegalArgumentException("Input line cannot be null");
        }

        final List<String> values = new ArrayList<>();
        boolean inQuotes = false;
        final StringBuilder currentField  = new StringBuilder();

        for (int i = 0; i < line.length(); i++) {
            final char currentChar = line.charAt(i);

            if (inQuotes) {
                handleQuotedCharacter(line, i, currentField );
                // Check if the current quote is not escaped and closes the quoted section
                if (currentChar == quote && !isEscapedQuote(line, i)) {
                    inQuotes = false;
                }
            } else {
                handleUnquotedCharacter(currentChar, values, currentField );
                // Detect opening quote
                if (currentChar == quote) {
                    inQuotes = true;
                }
            }
        }

        // Add the last field
        values.add(currentField.toString());

        return values.toArray(new String[0]);
    }

    /**
     * Handles characters when inside quoted section.
     */
    private void handleQuotedCharacter(String line, int currentIndex, StringBuilder currentField ) {
        char currentChar = line.charAt(currentIndex);

        if (currentChar == quote && isEscapedQuote(line, currentIndex)) {
            currentField .append(quote);
        } else if (currentChar != quote) {
            currentField .append(currentChar);
        }
    }

    /**
     * Handles characters when outside quoted section.
     */
    private void handleUnquotedCharacter(char currentChar, List<String> values, StringBuilder currentField ) {
        if (currentChar == separator) {
            values.add(currentField .toString());
            currentField .setLength(0); // Clear the buffer more efficiently than new StringBuilder()
        } else {
            currentField .append(currentChar);
        }
    }

    /**
     * Checks if the quote at given position is escaped (i.e., followed by another quote).
     */
    private boolean isEscapedQuote(String line, int quotePosition) {
        return quotePosition + 1 < line.length() && line.charAt(quotePosition + 1) == quote;
    }
    /**
     * Closes the reader
     */
    @Override
    public void close() throws IOException {
        if (reader != null) {
            reader.close();
        }
    }


    public static class Builder {
        private char separator = ',';
        private char quote = '"';
        private Charset charset = StandardCharsets.UTF_8;
        private int headerLine = 0;
        private boolean trimValues = true;
        private boolean ignoreEmptyLines = true;

        public Builder separator(char separator) {
            this.separator = separator;
            return this;
        }

        public Builder quote(char quote) {
            this.quote = quote;
            return this;
        }

        public Builder charset(Charset charset) {
            this.charset = charset;
            return this;
        }

        public Builder headerLine(int headerLine) {
            this.headerLine = headerLine;
            return this;
        }

        public Builder trimValues(boolean trimValues) {
            this.trimValues = trimValues;
            return this;
        }

        public Builder ignoreEmptyLines(boolean ignoreEmptyLines) {
            this.ignoreEmptyLines = ignoreEmptyLines;
            return this;
        }

        public CsvReaderUtils build() {
            return new CsvReaderUtils(this);
        }
    }



}
