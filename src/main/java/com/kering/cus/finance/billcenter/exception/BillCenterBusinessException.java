package com.kering.cus.finance.billcenter.exception;

import com.kering.cus.lib.common.exception.BusinessException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public class BillCenterBusinessException extends BusinessException {

    public BillCenterBusinessException(BillCenterErrorCode demoErrorCode){
        super(demoErrorCode.getCode(), demoErrorCode.getMessage());
    }

    public BillCenterBusinessException(BillCenterErrorCode demoErrorCode, final Throwable cause){
        super(demoErrorCode.getCode(), demoErrorCode.getMessage(), cause);
    }

}
