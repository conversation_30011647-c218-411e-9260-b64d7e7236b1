package com.kering.cus.finance.billcenter.util;

import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class BizPreconditions {

    public static void checkTrue(final boolean expr, final BillCenterErrorCode errorCode) {
        if (!expr) {
            throw new BillCenterBusinessException(errorCode);
        }
    }

    public static String checkHasText(final String text, final BillCenterErrorCode errorCode) {
        if (!StringUtils.hasText(text)) {
            throw new BillCenterBusinessException(errorCode);
        }
        return text;
    }

    public static <T> T checkNotNull(final T obj, final BillCenterErrorCode errorCode) {
        if (obj == null) {
            throw new BillCenterBusinessException(errorCode);
        }
        return obj;
    }

}
