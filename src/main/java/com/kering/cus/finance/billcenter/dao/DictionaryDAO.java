package com.kering.cus.finance.billcenter.dao;

import com.kering.cus.finance.billcenter.entity.DictionaryEntity;
import com.kering.cus.finance.billcenter.mapper.DictionaryMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class DictionaryDAO extends MyBatisBaseDAO<DictionaryEntity, DictionaryMapper, Long> {

  public List<DictionaryEntity> findByDicGroup(String dicGroup) {
    return entityMapper.selectList(new LambdaQueryWrapper<DictionaryEntity>()
        .eq(DictionaryEntity::getDicGroup, dicGroup)
        .eq(DictionaryEntity::getIsEnabled, true)
        .eq(DictionaryEntity::getDeleted, false));
  }

  /**
   * Get dictionary name by group and key
   *
   * @param dicGroup dictionary group name
   * @param dicKey   dictionary key
   * @return DictionaryEntity dictionary name if found, null otherwise
   */
  public DictionaryEntity findNameByGroupAndKey(String dicGroup, String dicKey) {
    LambdaQueryWrapper<DictionaryEntity> queryWrapper = new LambdaQueryWrapper<DictionaryEntity>()
        .eq(DictionaryEntity::getDicGroup, dicGroup)
        .eq(DictionaryEntity::getDicKey, dicKey)
        .eq(DictionaryEntity::getIsEnabled, true)
        .eq(DictionaryEntity::getDeleted, false);
    return entityMapper.selectOne(queryWrapper);
  }
}
