package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.DictionaryService;
import com.kering.cus.finance.billcenter.vo.CommonBusinessVO;
import com.kering.cus.finance.billcenter.vo.DictionaryVO;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName DictionaryController
 * @Description Data dictionary query controller
 * @Date 2025-07-23 18:19:36
 * @Version V1.0
 **/
@RestController
@RequestMapping("api")
public class DictionaryController {

  @Resource
  private DictionaryService dictionaryService;

  @GetMapping("dictionaries/{dicGroup}")
  @Operation(summary = "Query dictionary data by group")
  @Probe(event = "queryDictionaryData", group = "controller")
  public ResponseEntity<CommonBusinessVO<List<DictionaryVO>>> queryDictionaryData(
      @PathVariable("dicGroup") @ProbeDimension(name = "group") String dicGroup) {
    return ResponseEntity.ok(CommonBusinessVO.success(dictionaryService.getDictionaryData(dicGroup)));
  }

}
