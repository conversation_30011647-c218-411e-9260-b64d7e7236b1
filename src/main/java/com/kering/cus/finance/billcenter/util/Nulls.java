package com.kering.cus.finance.billcenter.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class Nulls {

    /**
     * ZERO for integer.
     */
    public static final Integer ZERO_INT = 0;

    /**
     * ZERO for long.
     */
    public static final Long ZERO_LONG = 0L;

    /**
     * ZERO for double.
     */
    public static final Double ZERO_DOUBLE = 0D;

    /**
     * ZERO for long.
     */
    public static final BigDecimal ZERO_BIG_DECIMAL = BigDecimal.ZERO;

    /**
     * ZERO for Boolean.
     */
    public static final Boolean ZERO_BOOLEAN = false;

    public static Integer nvl(final Integer integer) {
        return nvl(integer, ZERO_INT);
    }

    public static Long nvl(final Long value) {
        return nvl(value, ZERO_LONG);
    }

    public static Double nvl(final Double value) {
        return nvl(value, ZERO_DOUBLE);
    }

    public static BigDecimal nvl(final BigDecimal value) {
        return nvl(value, ZERO_BIG_DECIMAL);
    }

    public static Boolean nvl(final Boolean value) {
        return nvl(value, ZERO_BOOLEAN);
    }

    public static <T> List<T> nvl(final List<T> value) {
        return null != value ? value : Collections.emptyList();
    }

    public static <K, V> Map<K, V> nvl(final Map<K, V> value) {
        return null != value ? value : Collections.emptyMap();
    }

    public static <T> T nvl(final T value, final T def) {
        return null != value ? value : def;
    }

    public static <T> T nvl(final T value, final Supplier<T> defSupplier) {
        return null != value ? value : defSupplier.get();
    }

    public static ZonedDateTime nvl(final ZonedDateTime value, final ZonedDateTime... candidates) {
        if (null != value) {
            return value;
        }
        for (final ZonedDateTime candidate : candidates) {
            if (null != candidate) {
                return candidate;
            }
        }
        return null;
    }

    public static Integer nvlAsInt(final Long value) {
        return nvlAsInt(value, ZERO_INT);
    }

    public static Integer nvlAsInt(final Long value, final int def) {
        return null != value ? value.intValue() : def;
    }

    public static Integer nvlAsInt(final String value) {
        if (!StringUtils.isNumeric(value)) {
            return ZERO_INT;
        }
        return !value.trim().isEmpty() ? Integer.valueOf(value) : ZERO_INT;
    }

    public static Long nvlAsLong(final Integer value) {
        return null != value ? Long.valueOf(value) : ZERO_LONG;
    }

    public static Long nvlAsLong(final Long value, final Long def) {
        return null != value ? value : def;
    }

}