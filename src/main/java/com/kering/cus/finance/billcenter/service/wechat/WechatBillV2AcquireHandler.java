package com.kering.cus.finance.billcenter.service.wechat;


import static com.kering.cus.finance.billcenter.constant.Constants.CHARACTER_ENCODING_UTF8;
import static com.kering.cus.finance.billcenter.constant.Constants.CSV_GZ;

import com.alibaba.schedulerx.shade.org.apache.commons.lang.ObjectUtils;
import com.kering.cus.finance.billcenter.client.wechat.WechatPayUtils;
import com.kering.cus.finance.billcenter.client.wechat.WechatQueryBillRequest;
import com.kering.cus.finance.billcenter.config.WechatBillConfig;
import com.kering.cus.finance.billcenter.config.WechatV2MerchantAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.constant.WechatAccountV2Type;
import com.kering.cus.finance.billcenter.constant.WechatBillType;
import com.kering.cus.finance.billcenter.constant.WechatSignType;
import com.kering.cus.finance.billcenter.constant.WechatTarType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import com.kering.cus.finance.billcenter.service.support.BillAcquireHandler;
import com.kering.cus.finance.billcenter.util.DateUtil;
import com.kering.cus.lib.common.util.JsonUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.eclipse.jetty.util.StringUtil;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.JDOMException;
import org.jdom2.output.Format;
import org.jdom2.output.XMLOutputter;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName WechatBillV2AcquireHandler
 * @Description Handling of WeChat Transaction Bill Tasks
 * @Date 2025-07-07 17:40
 * @Version V1.0
 **/
@Service
@Slf4j
public class WechatBillV2AcquireHandler implements BillAcquireHandler {
  private static final String FILENAME_FORMAT = "%s_%s.%s";

  @Resource
  AppSecretService appSecretService;

  @Resource
  WechatBillConfig wechatBillConfig;


  @Override
  public boolean matches(Channel channel, GrantType grantType, BillType billType, String apiVersion) {
    return Channel.WECHAT == channel && GrantType.MERCHANT == grantType && (
        BillType.TRADE_FLOW == billType ||
            BillType.FUND_FLOW == billType) && "v2".equals(apiVersion);
  }

  @Override
  public String acquireTo(BillTaskEntity billTaskEntity, File targetFile) {
    log.info("Starting to process WeChat Pay Platform V2 bill task, param = {}", JsonUtil.convertToString(billTaskEntity));
    WechatV2MerchantAppProperties wechatV2MerchantAppProperties = appSecretService.getMerchantAppSecret(billTaskEntity.getExtraParams(),
        billTaskEntity.getChannel().toString(), billTaskEntity.getMerchantId(),
        WechatV2MerchantAppProperties.class);
    if (wechatV2MerchantAppProperties == null || StringUtil.isBlank(wechatV2MerchantAppProperties.getAppId()) || StringUtil.isBlank(
        wechatV2MerchantAppProperties.getApiCertificate()) || StringUtil.isBlank(wechatV2MerchantAppProperties.getAppSecret()) || StringUtil.isBlank(
        wechatV2MerchantAppProperties.getApiPrivateKey())) {
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_BILL_KMS_ACQUIRE_FAILED);
    }
    // Call WeChat platform interface to obtain bill
    acquireWechatV2TradeBill(billTaskEntity, wechatV2MerchantAppProperties, targetFile);

    final String merchantId = billTaskEntity.getMerchantId();
    final String timestamp = DateUtil.format(billTaskEntity.getBillStartTime(), DateUtil.DATE_FORMATTER_FIRST);
    return String.format(FILENAME_FORMAT, merchantId, timestamp, CSV_GZ);
  }

  /**
   * Call WeChat platform interface to obtain bill
   *
   * @param billTaskEntity                Bill task entity
   * @param wechatV2MerchantAppProperties WeChat merchant authorization information (KMS)
   * @param targetFile                    Bill saved file
   */
  void acquireWechatV2TradeBill(BillTaskEntity billTaskEntity, WechatV2MerchantAppProperties wechatV2MerchantAppProperties, File targetFile) {
    // Build WeChat transaction bill acquisition parameters
    WechatQueryBillRequest wechatQueryBillRequest = new WechatQueryBillRequest().setBillType(WechatBillType.ALL)
        .setSignType(WechatSignType.HMACSHA256).setTarType(WechatTarType.GZIP)
        .setNonceStr(WechatPayUtils.generateNonceStr()).setAppKey(wechatV2MerchantAppProperties.getAppSecret())
        .setAppId(wechatV2MerchantAppProperties.getAppId())
        .setBillDate(billTaskEntity.getBillStartTime() != null ? DateUtil.format(billTaskEntity.getBillStartTime(),
            DateUtil.DATE_FORMATTER_FIRST) : DateUtil.format(billTaskEntity.getBillEndTime(),
            DateUtil.DATE_FORMATTER_FIRST)).setMchId(billTaskEntity.getMerchantId()).setAccountType(WechatAccountV2Type.BASIC.getValue());
    CloseableHttpClient httpClient = null;
    try {
      httpClient = WechatPayUtils.createHttpClient(wechatV2MerchantAppProperties.getApiPrivateKey(), billTaskEntity.getMerchantId(),
          wechatV2MerchantAppProperties.getApiCertificate());
      downloadWechatV2Bill(wechatQueryBillRequest, httpClient
          , billTaskEntity.getBillType(),
          targetFile);
    } catch (Exception e) {
      log.error("WeChat V2 bill download failed, param = {}, error = {}", JsonUtil.convertToString(wechatQueryBillRequest), e);
      throw new BillCenterBusinessException(BillCenterErrorCode.WECHAT_BILL_ACQUIRE_FAILED, e);
    } finally {
      try {
        WechatPayUtils.closeHttpClient(httpClient);
      } catch (IOException e) {
        log.error("WeChat Pay Platform V2 bill download failed to close httpClient", e);
      }
    }
  }

  /**
   * Download WeChat Pay bill
   * <p>
   * Download specified date's bill and save as file according to parameters
   *
   * @param wechatQueryBillRequest WeChat query bill request parameters
   * @param httpClient             HTTP client
   * @param billFile               Bill saved file
   * @param billType               Bill type
   * @throws Exception If error occurs during download (including network error, parameter error etc.)
   */
  void downloadWechatV2Bill(WechatQueryBillRequest wechatQueryBillRequest, CloseableHttpClient httpClient, BillType billType,
      File billFile) throws Exception {
    // Select corresponding interface URL
    String wechatBillHost = wechatBillConfig.getWechatBillHost();
    String url =
        Objects.equals(billType, BillType.TRADE_FLOW) ? wechatBillHost + wechatBillConfig.getWechatTradeBillUrlV2()
            : wechatBillHost + wechatBillConfig.getWechatFundBillUrlV2();

    // Build request XML
    Document requestDoc = buildBillRequestXml(wechatQueryBillRequest, billType);

    // Create HTTP request
    HttpPost httpPost = new HttpPost(url);
    httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");

    // Set request body
    String requestXml = new XMLOutputter(Format.getCompactFormat().setEncoding(CHARACTER_ENCODING_UTF8)).outputString(requestDoc);
    log.info("WeChat Pay Platform V2 bill acquisition started, param = {}", requestXml);
    httpPost.setEntity(new StringEntity(requestXml, CHARACTER_ENCODING_UTF8));

    // Execute request
    try (CloseableHttpResponse response = WechatPayUtils.executeWithRetry(httpPost, null, httpClient)) {
      int statusCode = response.getStatusLine().getStatusCode();
      HttpEntity entity = response.getEntity();
      if (statusCode < 200 || statusCode >= 300) {
        throw new IOException(String.format(
            "WeChat Pay Platform V2 bill application failed [Status Code: %d]", statusCode));
      }
      if (entity == null) {
        throw new IOException("WeChat Pay Platform V2 bill download returned no content");
      }
      String contentType = entity.getContentType() != null ? entity.getContentType().getValue() : "";
      log.info("wechat v2 acquire bill ,contentType = {}", contentType);
      boolean isXmlResponse = !contentType.contains("application/x-gzip");
      // Handle XML format error response
      if (isXmlResponse) {
        handleXmlErrorResponse(entity);
        return;
      }
      // File processing
      saveHttpEntityToFile(billFile, entity);
    }
  }

  /**
   * Save HTTP response content to file
   *
   * @param billFile Saved bill file
   * @param entity   HTTP response entity
   * @throws IOException
   */
  static void saveHttpEntityToFile(File billFile, HttpEntity entity) throws IOException {
    // Optimize large file handling
    final int BUFFER_SIZE = 1024 * 1024;

    try (InputStream inputStream = entity.getContent();
        FileOutputStream outputStream = new FileOutputStream(billFile)) {
      byte[] buffer = new byte[BUFFER_SIZE];
      int bytesRead;
      long totalRead = 0;
      long fileSize = entity.getContentLength();
      while ((bytesRead = inputStream.read(buffer)) != -1) {
        outputStream.write(buffer, 0, bytesRead);
        totalRead += bytesRead;
        if (fileSize > 0) {
          int progress = (int) ((totalRead * 100) / fileSize);
          if (progress % 10 == 0) {
            log.info("WeChat Platform V2 Bill Download Progress: " + progress + "%");
          }
        }
      }
      outputStream.flush();
    }
  }

  /**
   * Handle WeChat platform returned XML error response
   *
   * @param entity HTTP response entity
   * @throws IOException
   * @throws JDOMException
   */
  static void handleXmlErrorResponse(HttpEntity entity) throws IOException, JDOMException {
    String errorXml = EntityUtils.toString(entity, CHARACTER_ENCODING_UTF8);
    Document errorDoc;
    try {
      errorDoc = WechatPayUtils.parseXml(errorXml);
    } catch (JDOMException | IOException e) {
      return;
    }
    if (errorDoc == null) {
      return;
    }
    Element errorRoot = errorDoc.getRootElement();
    String returnCode = errorRoot.getChildText("return_code");
    String resultCode = errorRoot.getChildText("result_code");
    if (!"SUCCESS".equals(returnCode)) {
      String errCode = errorRoot.getChildText("error_code");
      String errMsg = errorRoot.getChildText("return_msg");
      String errorResult = errCode + " - " + errMsg;
      log.error("WeChat Pay Platform Bill V2 Communication Error: " + errorResult);
      if ((StringUtil.isNotBlank(errCode) && ObjectUtils.notEqual(errCode, "20002")) || (StringUtil.isNotBlank(errMsg) && ObjectUtils.notEqual(errMsg,
          "No Bill Exist"))) {
        throw new IOException(errorResult);
      }
    }
    if (!"SUCCESS".equals(resultCode)) {
      String errCode = errorRoot.getChildText("err_code");
      String errMsg = errorRoot.getChildText("err_code_des");
      String errorResult = errCode + " - " + errMsg;
      log.error("WeChat Pay Platform Bill V2 Business Error: " + errorResult);
      if (StringUtil.isNotBlank(errCode) && ObjectUtils.notEqual(errCode, "NO_BILL_EXIST")) {
        throw new IOException(errorResult);
      }
    }
  }

  /**
   * Build bill download request XML document
   * <p>
   * Build XML request body according to provided parameters
   *
   * @param wechatQueryBillRequest Query bill request parameters
   * @param billType               Bill type
   * @return Built XML document object
   */
  Document buildBillRequestXml(WechatQueryBillRequest wechatQueryBillRequest, BillType billType)
      throws NoSuchAlgorithmException, InvalidKeyException {
    Document doc = new Document();
    Element root = new Element("xml");
    doc.setRootElement(root);

    // Basic parameters
    root.addContent(new Element("appid").setText(wechatQueryBillRequest.getAppId()));
    root.addContent(new Element("mch_id").setText(wechatQueryBillRequest.getMchId()));
    root.addContent(new Element("nonce_str").setText(wechatQueryBillRequest.getNonceStr()));
    root.addContent(new Element("bill_date").setText(wechatQueryBillRequest.getBillDate()));
    if (wechatQueryBillRequest.getBillType() != null) {
      root.addContent(new Element("bill_type").setText(wechatQueryBillRequest.getBillType().getValue()));
    }
    if (wechatQueryBillRequest.getSignType() != null) {
      root.addContent(new Element("sign_type").setText(wechatQueryBillRequest.getSignType().getValue()));
    }
    if (wechatQueryBillRequest.getTarType() != null) {
      root.addContent(new Element("tar_type").setText(wechatQueryBillRequest.getTarType().getValue()));
    }
    if (billType == BillType.FUND_FLOW && wechatQueryBillRequest.getAccountType() != null) {
      root.addContent(new Element("account_type").setText(wechatQueryBillRequest.getAccountType()));
    }

    // Generate signature
    root.addContent(new Element("sign").setText(WechatPayUtils.generateV2Signature(root, wechatQueryBillRequest.getSignType().getValue(),
        wechatQueryBillRequest.getAppKey())));

    return doc;
  }
}

