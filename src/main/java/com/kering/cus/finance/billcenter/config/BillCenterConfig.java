package com.kering.cus.finance.billcenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
public class BillCenterConfig {

  @Value("${core.alipay.callback.url}")
  private String alipayCallbackUrl;

  @Value("${SECRET_CONFIG_PATH_FINANCE_BILLCENTER}")
  private String secretAccessRootPath;

  @Value("${STORAGE_OSS_BUCKET}")
  private String storageBucket;

  @Value("${core.retry.max-times:3}")
  private int maxRetryTimes;

  @Value("${core.retry.backoff:false}")
  private boolean isBackoffRetry;

  @Value("${KAFKA_TOPIC_CUS_BILLCENTER}")
  private String cfsTopic;

  @Value("${KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_ACQUIRE_TASK}")
  private String acquireTaskTopic;

  @Value("${core.task.acquire.timeout-minutes:5}")
  private int acquireTaskTimeoutMinutes;

  @Value("${KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_PROCESS_TASK}")
  private String processTaskTopic;

  @Value("${KAFKA_TOPIC_CUS_MQ_CUS_BILL_CENTER_PUSH_TO_SAP_TASK}")
  private String pushToSapTopic;

  @Value("${core.task.process.timeout-minutes:10}")
  private int processTaskTimeoutMinutes;

  @Value("${core.task.archive.timeout-minutes:15}")
  private int archiveTaskTimeoutMinutes;

}
