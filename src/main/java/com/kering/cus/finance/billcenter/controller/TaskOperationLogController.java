package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.TaskOperationLogService;
import com.kering.cus.finance.billcenter.vo.CommonBusinessVO;
import com.kering.cus.finance.billcenter.vo.TaskOperationLogVO;
import com.kering.cus.lib.log.annotation.Probe;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for task operation log query interface
 */
@Slf4j
@RestController
@RequestMapping("api")
@Tag(name = "Task Operation Log", description = "Task operation log query API")
public class TaskOperationLogController {

    @Resource
    private TaskOperationLogService taskOperationLogService;

    @GetMapping("trace-logs/{traceId}")
    @Operation(summary = "Get task operation logs by trace ID", description = "Query task operation logs by trace ID")
    @Probe(event = "getTaskOperationLogs", group = "controller")
    public ResponseEntity<CommonBusinessVO<List<TaskOperationLogVO>>> getTaskOperationLogs(
        @Parameter(name = "traceId", in = ParameterIn.PATH, description = "Trace ID of the task log", required = true)
        @PathVariable("traceId") @Valid String traceId) {

        return ResponseEntity.ok(CommonBusinessVO.success(taskOperationLogService.getTaskOperationLogs(traceId)));
    }
}
