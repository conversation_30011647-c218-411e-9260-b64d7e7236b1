package com.kering.cus.finance.billcenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.mapper.TransitLogMapper;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public class TransitLogDAO extends MyBatisBaseDAO<TransitLogEntity, TransitLogMapper, Long> {

  public List<TransitLogEntity> getTaskOperationLogs(String traceId) {
    LambdaQueryWrapper<TransitLogEntity> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(TransitLogEntity::getTraceId, traceId)
        .eq(TransitLogEntity::getDeleted, false);
    queryWrapper.orderByDesc(TransitLogEntity::getCreatedDate).orderByDesc(TransitLogEntity::getId);
    return entityMapper.selectList(queryWrapper);
  }
}

