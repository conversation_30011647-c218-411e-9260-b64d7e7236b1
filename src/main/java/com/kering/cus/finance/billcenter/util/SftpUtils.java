package com.kering.cus.finance.billcenter.util;

import com.jcraft.jsch.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.StringTokenizer;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class SftpUtils {
    public static final String PATH_SEPARATOR = "/";
    private static final int NO_SUCH_FILE_ID = 2;

    public static ChannelSftp connect(final String host, final int port,
                                      final String username, final String password,
                                      final byte[] privateKey, final byte[] passphrase,
                                      final int connectTimeout) throws IOException {
        return connect(new JSch(), host, port, username, password, privateKey, passphrase, connectTimeout);
    }

    @SuppressWarnings("java:S107")
    static ChannelSftp connect(final JSch jsch,
                               final String host, final int port,
                               final String username, final String password,
                               final byte[] privateKey, final byte[] passphrase,
                               final int connectTimeout) throws IOException {   // NOSONAR
        try {
            if (null != privateKey && privateKey.length > 0) {
                jsch.addIdentity(username, privateKey, null, passphrase);
            }

            final Session session = jsch.getSession(username, host, port);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setPassword(password);

            session.connect(connectTimeout);

            final ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            return sftp;
        } catch (final JSchException ex) {
            throw new IOException(String.format("Server - %s refused connection on port - %s: %s", host, port, ex.getMessage()), ex);
        }
    }

    public static void disconnect(final ChannelSftp sftp) {
        if (null == sftp) {
            return;
        }
        try {
            try {
                if (!sftp.isClosed()) {
                    sftp.disconnect();
                }
            } finally {
                final Session session = sftp.getSession();
                if (null != session && session.isConnected()) {
                    session.disconnect();
                }
            }
        } catch (final JSchException skip) {
            log.warn("Server - disconnect error: {}", skip.getMessage());
        }
    }

    public static boolean mkdirs(final ChannelSftp sftp, final String absPath) throws IOException {
        final StringTokenizer tokenizer = new StringTokenizer(absPath, PATH_SEPARATOR, false);
        try {
            StringBuilder buff = new StringBuilder();
            while (tokenizer.hasMoreElements()) {
                buff.append(PATH_SEPARATOR).append(tokenizer.nextElement());

                final String pathToUse = buff.toString();
                final SftpATTRS stat = stat0(sftp, pathToUse);
                if (null == stat) {
                    if (!mkdirQuietly(sftp, pathToUse)) {
                        return false;
                    }
                } else if (!stat.isDir()) {
                    throw new IOException("File already exists and is not directory: " + pathToUse);
                }
            }
            return true;
        } catch (final SftpException e) {
            throw new IOException(e);
        }
    }

    private static boolean mkdirQuietly(final ChannelSftp sftp, final String absPath) {
        try {
            sftp.mkdir(absPath);
            return true;
        } catch (final SftpException ex2) {
            // ignore
            return false;
        }
    }

    private static SftpATTRS stat0(final ChannelSftp sftp, final String absPath) throws SftpException {
        try {
            return sftp.stat(absPath);
        } catch (final SftpException e) {
            if (NO_SUCH_FILE_ID == e.id) {
                return null;
            }
            throw e;
        }
    }

}
