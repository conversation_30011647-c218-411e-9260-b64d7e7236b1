package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.util.DateUtils;
import com.kering.cus.finance.billcenter.vo.TaskOperationLogVO;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * Implementation of task operation log service
 */
@Service
public class TaskOperationLogService {

  @Resource
  private TransitLogDAO transitLogDAO;


  public List<TaskOperationLogVO> getTaskOperationLogs(String traceId) {

    List<TransitLogEntity> entities = transitLogDAO.getTaskOperationLogs(traceId);

    return entities.stream()
        .map(entity -> {
          TaskOperationLogVO vo = new TaskOperationLogVO();
          vo.setId(entity.getId())
              .setTitle(entity.getTitle())
              .setMessage(entity.getMessage())
              .setCreatedDate(
                  entity.getCreatedDate() != null ? DateUtils.formatDateTime(entity.getCreatedDate(), DateUtils.YYYY_MM_DD_HH_MM_SS) : null)
              .setTraceId(entity.getTraceId());
          return vo;
        })
        .toList();
  }
}
