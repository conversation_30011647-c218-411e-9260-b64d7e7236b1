package com.kering.cus.finance.billcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kering.cus.finance.billcenter.config.serializer.BigDecimalDeserializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WosaipayTradeFlowVO {

    @JsonProperty(value = "商户名")
    private String merchantName;

    @JsonProperty(value = "商户号")
    private String merchantId;


    @JsonProperty(value = "门店名")
    private String storeName;

    @JsonProperty(value = "门店号")
    private String storeCode;

    @JsonProperty(value = "商户门店号")
    private String merchantStoreCode;

    @JsonProperty(value = "交易日期")
    private String transactionDate;

    @JsonProperty(value = "交易时间")
    private String transactionTime;

    @JsonProperty(value = "商户订单号")
    private String merchantOrderId;

    @JsonProperty(value = "商户流水号")
    private String merchantTransactionNo;

    @JsonProperty(value = "轻POS订单号")
    private String posOrderId;

    @JsonProperty(value = "轻POS流水号")
    private String posTransactionNo;

    @JsonProperty(value = "轻POS原始订单号")
    private String posOriginOrderId;

    @JsonProperty(value = "轻POS原始流水号")
    private String posOriginTransactionNo;

    @JsonProperty(value = "收钱吧订单号")
    private String wosaypayOrderId;

    @JsonProperty(value = "收款通道")
    private String paymentChannel;

    @JsonProperty(value = "收款通道商户号")
    private String paymentChannelMerchantId;

    @JsonProperty(value = "收款通道订单号")
    private String paymentChannelOrderId;

    @JsonProperty(value = "商品名")
    private String skuName;

    @JsonProperty(value = "交易类型")
    private String transactionType;

    @JsonProperty(value = "交易模式")
    private String tradeMode;

    @JsonProperty(value = "订单来源")
    private String orderSource;

    @JsonProperty(value = "交易场景")
    private String orderSence;

    @JsonProperty(value = "交易状态")
    private String tradeStatus;

    @JsonProperty(value = "付款账户")
    private String paymentAccount;

    @JsonProperty(value = "币种/货币类型")
    private String currency;

    @JsonProperty(value = "交易金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal transactionAmount;

    @JsonProperty(value = "收钱吧商户优惠")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal merchantDiscountAmount;

    @JsonProperty(value = "收钱吧商户优惠类型")
    private String merchantDiscountType;

    @JsonProperty(value = "收钱吧补贴优惠")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal subsidyAmount;

    @JsonProperty(value = "收款通道机构优惠")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal paymentChannelDiscountAmount;

    @JsonProperty(value = "收款通道商户预充值优惠")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal rechargeDiscountAmount;

    @JsonProperty(value = "收款通道商户免充值优惠")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal nonRechargeDiscountAmount;

    @JsonProperty(value = "消费者实付金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal actualPaidAmount;

    @JsonProperty(value = "扣率%")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal transactionRate;

    @JsonProperty(value = "手续费")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal serviceCharge;

    @JsonProperty(value = "实收金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal actualRecvAmount;

    @JsonProperty(value = "分期期数")
    private Integer installmentNum;

    @JsonProperty(value = "分期手续费率")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal installmentFeeRate;

    @JsonProperty(value = "分期手续费")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal installmentFee;

    @JsonProperty(value = "分账金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal splitAmount;

    @JsonProperty(value = "结算金额")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal settlementAmount;

    @JsonProperty(value = "终端号")
    private String termNo;

    @JsonProperty(value = "商户终端号")
    private String merchantTermNo;

    @JsonProperty(value = "终端名称")
    private String termName;

    @JsonProperty(value = "终端类型")
    private String termType;

    @JsonProperty(value = "设备号")
    private String deviceNo;

    @JsonProperty(value = "操作员")
    private String operator;

    @JsonProperty(value = "收银员")
    private String cashier;

    @JsonProperty(value = "备注")
    private String note;

    @JsonProperty(value = "扩展一")
    private String ext1;

    @JsonProperty(value = "扩展二")
    private String ext2;

    @JsonProperty(value = "业务订单号")
    private String bizOrderId;

    @JsonProperty(value = "业务备注")
    private String bizNote;

    @JsonProperty(value = "保证金")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal deposit;

    @JsonProperty(value = "外部系统商户原交易流水号")
    private String outerOriginTransactionNo;

    @JsonProperty(value = "预留字段3")
    private String reserve3;


}
