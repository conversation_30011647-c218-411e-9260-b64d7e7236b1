package com.kering.cus.finance.billcenter.client.wechat;


import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.annotations.Expose;
import freework.net.Http;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.SignatureException;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.JDOMException;
import org.jdom2.input.SAXBuilder;

/**
 * <AUTHOR>
 * @ClassName WechatPayUtils
 * @Description WeChat Pay Platform Utility Class
 * @Date 2025-07-07 18:00
 * @Version V1.0
 **/

@Slf4j
public class WechatPayUtils {

  private WechatPayUtils() {
  }

  private static final char[] SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();
  private static final SecureRandom random = new SecureRandom();
  private static final String BEGIN_PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----";
  private static final String END_PRIVATE_KEY = "-----END PRIVATE KEY-----";
  private static final String SHA_WITH_RSA = "SHA256withRSA";
  private static final String PKCS = "PKCS12";

  private static final Gson gson = new GsonBuilder()
      .disableHtmlEscaping()
      .addSerializationExclusionStrategy(new ExclusionStrategy() {
        @Override
        public boolean shouldSkipField(FieldAttributes fieldAttributes) {
          final Expose expose = fieldAttributes.getAnnotation(Expose.class);
          return expose != null && !expose.serialize();
        }

        @Override
        public boolean shouldSkipClass(Class<?> aClass) {
          return false;
        }
      })
      .addDeserializationExclusionStrategy(new ExclusionStrategy() {
        @Override
        public boolean shouldSkipField(FieldAttributes fieldAttributes) {
          final Expose expose = fieldAttributes.getAnnotation(Expose.class);
          return expose != null && !expose.deserialize();
        }

        @Override
        public boolean shouldSkipClass(Class<?> aClass) {
          return false;
        }
      })
      .create();

  /**
   * Converts an Object to a JSON string
   */
  public static String toJson(Object object) {
    return gson.toJson(object);
  }


  public static <T> T fromJson(String json, Class<T> classOfT) throws JsonSyntaxException {
    return gson.fromJson(json, classOfT);
  }

  /**
   * Reads a PKCS#8 formatted private key string and loads it into a PrivateKey object.
   *
   * @param keyString the content of the private key file, starting with -----BEGIN PRIVATE KEY-----
   * @return the generated PrivateKey object
   */

  public static PrivateKey loadPrivateKeyFromString(String keyString) {
    try {
      keyString = keyString.replace(BEGIN_PRIVATE_KEY, "").replace(END_PRIVATE_KEY, "").replaceAll("\\s+", "");
      return KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyString)));
    } catch (NoSuchAlgorithmException e) {
      throw new UnsupportedOperationException(e);
    } catch (InvalidKeySpecException e) {
      throw new IllegalArgumentException(e);
    }
  }

  /**
   * Create a random string of specified length with a character set of [0-9a-zA-Z], which can be used for security-related purposes
   */
  public static String createNonce(int length) {
    char[] buf = new char[length];
    for (int i = 0; i < length; ++i) {
      buf[i] = SYMBOLS[random.nextInt(SYMBOLS.length)];
    }
    return new String(buf);
  }

  /**
   * Signs the specified message using the private key with the given algorithm
   *
   * @param message    The string to be signed
   * @param algorithm  The signing algorithm, such as SHA256withRSA
   * @param privateKey The private key object used for signing
   * @return The signing result
   */
  public static String sign(String message, String algorithm, PrivateKey privateKey) {
    byte[] sign;
    try {
      Signature signature = Signature.getInstance(algorithm);
      signature.initSign(privateKey);
      signature.update(message.getBytes(StandardCharsets.UTF_8));
      sign = signature.sign();
    } catch (NoSuchAlgorithmException e) {
      throw new UnsupportedOperationException("The current Java environment does not support " + algorithm, e);
    } catch (InvalidKeyException e) {
      throw new IllegalArgumentException(algorithm + " signature uses an illegal privateKey.", e);
    } catch (SignatureException e) {
      throw new IllegalStateException("An error occurred during the sign process.", e);
    }
    return Base64.getEncoder().encodeToString(sign);
  }

  /**
   * Verifies the signature using the public key with a specific algorithm
   *
   * @param message   The string to be signed
   * @param signature The signature content to be verified
   * @param algorithm The signing algorithm, e.g., SHA256withRSA
   * @param publicKey The public key object used for signature verification
   * @return The public key object used for signature verification
   */
  public static boolean verify(String message, String signature, String algorithm, PublicKey publicKey) {
    try {
      Signature sign = Signature.getInstance(algorithm);
      sign.initVerify(publicKey);
      sign.update(message.getBytes(StandardCharsets.UTF_8));
      return sign.verify(Base64.getDecoder().decode(signature));
    } catch (SignatureException e) {
      return false;
    } catch (InvalidKeyException e) {
      throw new IllegalArgumentException("verify uses an illegal publickey.", e);
    } catch (NoSuchAlgorithmException e) {
      throw new UnsupportedOperationException("The current Java environment does not support" + algorithm, e);
    }
  }

  /**
   * Construct the Authorization signature according to WeChat Pay APIv3 request signature rules
   *
   * @param mchid               Construct the Authorization signature according to WeChat Pay APIv3 request signature rules
   * @param certificateSerialNo Merchant API certificate serial number
   * @param privateKey          Merchant API certificate private key
   * @param method              HTTP method of the requested interface, please use uppercase, such as GET, POST, PUT, DELETE
   * @param uri                 URL of the requested interface
   * @param body                Body of the requested interface
   * @return Constructed WeChat Pay APIv3 Authorization header
   */
  public static String buildAuthorization(String mchid, String certificateSerialNo, PrivateKey privateKey, String method, String uri, String body) {
    String nonce = createNonce(32);
    long timestamp = Instant.now().getEpochSecond();

    StringBuilder messageBuilder = new StringBuilder();
    messageBuilder.append(method != null ? method : "").append("\n");
    messageBuilder.append(uri != null ? uri : "").append("\n");
    messageBuilder.append(timestamp).append("\n");
    messageBuilder.append(nonce).append("\n");
    messageBuilder.append(body != null ? body : "").append("\n");
    String message = messageBuilder.toString();

    String signature = sign(message, SHA_WITH_RSA, privateKey);

    return String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",signature=\"%s\"," + "timestamp=\"%d\",serial_no=\"%s\"", mchid,
        nonce, signature, timestamp, certificateSerialNo);
  }

  /**
   * Perform URL encoding on the parameter
   *
   * @param content The content of the parameter
   * @return The encoded content
   */
  public static String urlEncode(String content) {
    try {
      return URLEncoder.encode(content, StandardCharsets.UTF_8.name());
    } catch (UnsupportedEncodingException e) {
      throw new IllegalStateException(e);
    }
  }

  /**
   * Perform URL encoding on the parameter Map to generate a QueryString
   *
   * @param params Query parameter Map
   * @return QueryString
   */
  public static String urlEncode(Map<String, Object> params) {
    if (params == null || params.isEmpty()) {
      return "";
    }

    int index = 0;
    StringBuilder result = new StringBuilder();
    for (Map.Entry<String, Object> entry : params.entrySet()) {
      result.append(entry.getKey()).append("=").append(urlEncode(entry.getValue().toString()));
      index++;
      if (index < params.size()) {
        result.append("&");
      }
    }
    return result.toString();
  }

  /**
   * Creates a WeChat Pay-specific HTTP client
   *
   * @param wechatPrivateKey PEM-formatted string of the WeChat merchant or platform private key (including BEGIN/END markers)
   * @param mchId            WeChat merchant ID
   * @param pemCertificate   PEM-formatted certificate string (including BEGIN/END markers)
   * @return Configured CloseableHttpClient instance
   */
  public static CloseableHttpClient createHttpClient(String wechatPrivateKey, String mchId, String pemCertificate) {
    try {
      RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(30000).setSocketTimeout(60000).setConnectionRequestTimeout(30000)
          .build();
      if (StringUtils.isBlank(pemCertificate)) {
        return HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
      }
      SSLContext sslContext;
      if (StringUtils.isNotBlank(wechatPrivateKey)) {
        PrivateKey privateKey = loadPrivateKey(wechatPrivateKey);
        Certificate certificate = generateCertificateFromPEM(pemCertificate);

        java.security.KeyStore keyStore = java.security.KeyStore.getInstance(PKCS);
        keyStore.load(null, null);
        keyStore.setCertificateEntry("wechat", certificate);

        java.security.KeyStore.PrivateKeyEntry privateKeyEntry = new java.security.KeyStore.PrivateKeyEntry(privateKey,
            new Certificate[]{certificate});
        keyStore.setEntry(mchId, privateKeyEntry, new java.security.KeyStore.PasswordProtection(mchId.toCharArray()));

        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        kmf.init(keyStore, (mchId + "").toCharArray());

        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(kmf.getKeyManagers(), new TrustManager[]{Http.TRUST_ALL_TRUST_MANAGER}, null);
      } else {
        Certificate certificate = generateCertificateFromPEM(pemCertificate);

        java.security.KeyStore keyStore = java.security.KeyStore.getInstance(PKCS);
        keyStore.load(null, null);
        keyStore.setCertificateEntry("wechat", certificate);

        KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
        kmf.init(keyStore, mchId.toCharArray());
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(kmf.getKeyManagers(), new TrustManager[]{Http.TRUST_ALL_TRUST_MANAGER}, null);
      }
      return HttpClients.custom().setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext)).setDefaultRequestConfig(requestConfig).build();
    } catch (Exception e) {
      log.error("Failed to create WeChat platform HTTP client", e);
      throw new IllegalStateException("Failed to create WeChat platform HTTP client", e);
    }
  }

  /**
   * Load a private key
   *
   * @param pemPrivateKey PEM-formatted private key string
   * @return PrivateKey object
   * @throws NoSuchAlgorithmException if loading fails
   */
  private static PrivateKey loadPrivateKey(String pemPrivateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
    String keyData = pemPrivateKey.replace(BEGIN_PRIVATE_KEY, "").replace(END_PRIVATE_KEY, "").replaceAll("\\s+", "");

    byte[] decodedBytes = Base64.getDecoder().decode(keyData);
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedBytes);
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    return keyFactory.generatePrivate(keySpec);
  }

  /**
   * Execute HTTP request with retry mechanism
   *
   * @param httpPost HTTP POST request object
   * @return Server response object
   * @throws Exception If failed after maximum retries
   */
  public static CloseableHttpResponse executeWithRetry(HttpPost httpPost, HttpGet httpGet, CloseableHttpClient httpClient) throws Exception {
    int retryCount = 0;
    int maxRetryCount = 0;
    Exception lastException = null;

    while (retryCount <= maxRetryCount) {
      try {
        if (httpPost != null) {
          return httpClient.execute(httpPost);
        }
        return httpClient.execute(httpGet);
      } catch (Exception e) {
        lastException = e;
        // Log error message in English while preserving Chinese context
        log.error("WeChat payment platform bill HTTP request failed, reached maximum retry attempts. Please check network or try again later", e);
        if (retryCount >= maxRetryCount) {
          log.error(
              "The HTTP request for the WeChat Pay platform bill failed and has reached the maximum number of retries. Please check your network or try again later.",
              e);
          break;
        }
        retryCount++;
        Thread.sleep(2000);
      }
    }
    throw new IllegalStateException("WeChat payment platform reached maximum retry count, HTTP request failed", lastException);
  }


  /**
   * Close the HTTP client
   * <p>
   * Release the resources of the HTTP client. It is recommended to call this method after use.
   *
   * @throws IOException if an I/O error occurs during closing
   */
  public static void closeHttpClient(CloseableHttpClient httpClient) throws IOException {
    if (httpClient != null) {
      httpClient.close();
    }
  }

  /**
   * Parses an XML string into a Document object
   *
   * @param xml The XML string to be parsed
   * @return The parsed Document object
   * @throws JDOMException if the XML format is incorrect
   * @throws IOException   if an I/O error occurs while reading the XML content
   */
  public static Document parseXml(String xml) throws JDOMException, IOException {
    SAXBuilder saxBuilder = new SAXBuilder();
    saxBuilder.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
    saxBuilder.setFeature("http://xml.org/sax/features/external-general-entities", false);
    saxBuilder.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
    saxBuilder.setExpandEntities(false);
    return saxBuilder.build(new StringReader(xml));
  }

  /**
   * Generate a random string
   * <p>
   * Generates a 32-character random string for use as the nonce_str parameter in requests
   *
   * @return 32-character random string
   */
  public static String generateNonceStr() {
    return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
  }

  /**
   * Generate request signature
   * <p>
   * Calculate the signature for request parameters according to WeChat Pay signature rules
   *
   * @param root     XML root element containing all request parameters
   * @param signType Signature type, supporting "HMAC-SHA256" and "MD5"
   * @return The calculated signature string
   * @throws IllegalArgumentException if the signature type is not supported
   */
  public static String generateV2Signature(Element root, String signType, String apiKey) throws NoSuchAlgorithmException, InvalidKeyException {
    List<Element> elements = root.getChildren();

    Map<String, String> paramMap = new TreeMap<>();
    for (Element element : elements) {
      String name = element.getName();
      String value = element.getText();
      if (value != null && !value.isEmpty() && !"sign".equals(name)) {
        paramMap.put(name, value);
      }
    }

    StringBuilder sb = new StringBuilder();
    for (Map.Entry<String, String> entry : paramMap.entrySet()) {
      if (sb.length() > 0) {
        sb.append("&");
      }
      sb.append(entry.getKey()).append("=").append(entry.getValue());
    }

    sb.append("&key=").append(apiKey);
    String signSource = sb.toString();

    if ("HMAC-SHA256".equals(signType)) {
      return generateHmacSha256Signature(signSource, apiKey);
    } else {
      throw new IllegalArgumentException("The signature type is not supported for obtaining WeChat V2 bills: " + signType);
    }
  }

  /**
   * Generates a signature using the HMAC-SHA256 algorithm
   *
   * @param data Data to be signed
   * @param key  Signing key
   * @return Generated signature string (uppercase)
   * @throws NoSuchAlgorithmException if an error occurs during signature generation
   */
  public static String generateHmacSha256Signature(String data, String key) throws NoSuchAlgorithmException, InvalidKeyException {
    Mac mac = Mac.getInstance("HmacSHA256");
    SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
    mac.init(secretKeySpec);
    byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    return Hex.encodeHexString(hash).toUpperCase();
  }

  /**
   * Generate a certificate object from a PEM-formatted private key
   *
   * @param pemCertificate PEM-formatted certificate string (including BEGIN/END markers)
   * @return The generated certificate object
   */
  public static Certificate generateCertificateFromPEM(String pemCertificate) {
    try {
      String certData = pemCertificate.replace("-----BEGIN CERTIFICATE-----", "").replace("-----END CERTIFICATE-----", "").replaceAll("\\s+", "");

      byte[] decodedBytes = Base64.getDecoder().decode(certData);
      CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
      return certFactory.generateCertificate(new ByteArrayInputStream(decodedBytes));
    } catch (Exception e) {
      // Translate exception messages while preserving technical terms
      throw new IllegalArgumentException("PEM certificate parsing failed", e);
    }
  }

}
