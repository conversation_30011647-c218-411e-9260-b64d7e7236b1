package com.kering.cus.finance.billcenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.kering.cus.finance.billcenter.config.serializer.BigDecimalDeserializer;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class AlipayBillFlowVO {

    @JsonProperty(value = "账务流水号")
    private String accountTransactionNo;

    @JsonProperty(value = "业务流水号")
    private String bizTransactionNo;

    @JsonProperty(value = "商户订单号")
    private String merchantOrderId;

    @JsonProperty(value = "商品名称")
    private String skuName;

    @JsonProperty(value = "发生时间")
    private String transactionTime;

    @JsonProperty(value = "对方账号")
    private String reciprocalAccount;

    @JsonProperty(value = "收入金额（+元）")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal income;

    @JsonProperty(value = "支出金额（-元）")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal expense;

    @JsonProperty(value = "账户余额（元）")
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal balance;

    @JsonProperty(value = "交易渠道")
    private String transactionChannel;

    @JsonProperty(value = "业务类型")
    private String transactionType;

    @JsonProperty(value = "备注")
    private String note;

    @JsonProperty(value = "业务描述")
    private String bizDesc;

    @JsonProperty(value = "业务订单号")
    private String bizOrderId;

    @JsonProperty(value = "业务基础订单号")
    private String bizBaseOrderId;

    @JsonProperty(value = "业务账单来源")
    private String bizBillSource;

    private String finalBizBaseOrderId;

}
