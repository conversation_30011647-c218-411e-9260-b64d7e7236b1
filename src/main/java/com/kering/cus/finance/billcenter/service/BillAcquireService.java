package com.kering.cus.finance.billcenter.service;

import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.BillAcquireHandler;
import com.kering.cus.finance.billcenter.util.BizPreconditions;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.Errors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Lazy
@Service
public class BillAcquireService {
    private static final String UNKNOWN = "unknown";
    private static final DateTimeFormatter FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    private static final List<TaskState> ALLOWED_STATUS = Arrays.asList(
            TaskState.WAIT_ACQUIRE, TaskState.ACQUIRE_FAILED
    );

    private final BillCenterConfig billCenterConfig;
    private final BillTaskDAO billTaskDAO;
    private final TransitLogDAO transitLogDAO;
    private final MerchantGrantConfigDAO merchantGrantConfigDAO;
    private final AppStorageService appStorageService;
    private final List<BillAcquireHandler> billAcquireHandlers;
    private final BillTaskEventPublisher billTaskEventPublisher;

    /**
     * 尝试执行账单获取任务.
     *
     * @param taskId 任务ID
     * @return 是否成功执行
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean acquireIfNecessary(final Long taskId) {
        final BillTaskEntity task = billTaskDAO.findById(taskId).orElse(null);
        if (null == task) {
            log.warn("Acquire task not found: id = {}", taskId);
            return false;
        }

        final ZonedDateTime now = ZonedDateTime.now();
        final String traceId = task.getTraceId();
        final TaskState taskState = task.getState();
        final ZonedDateTime nextRunTime = task.getNextRunTime();
        if (!ALLOWED_STATUS.contains(taskState) || nextRunTime.isAfter(now)) {
            log.warn("Acquire task is already running: id = {}", taskId);
            return false;
        }

        final ZonedDateTime nextRunTimeToUse = now.plusMinutes(billCenterConfig.getAcquireTaskTimeoutMinutes());
        final int updated = billTaskDAO.updateNextRunTimeByIdAndVersion(nextRunTimeToUse, taskId, task.getVersion());
        if (updated <= 0) {
            log.warn("Acquire task is already running: acquire lock failed, id = {}", taskId);
            return false;
        }

        logTransitLog(traceId, "开始拉取账单", "开始拉取账单");

        File tempFile = null;
        try {
            final MerchantGrantConfigEntity merchant = merchantGrantConfigDAO.findByChannelAndMerchantId(
                    task.getChannel(), task.getMerchantId()
            );
            BizPreconditions.checkNotNull(merchant, BillCenterErrorCode.MERCHANT_NOT_FOUND);

            final BillAcquireHandler billAcquireHandler = determineBillAcquireHandler(
                    task.getChannel(), task.getGrantType(), task.getBillType(), merchant.getApiVersion()
            );
            if (null == billAcquireHandler) {
                final String errorMsg = String.format("no %s found", BillAcquireHandler.class.getSimpleName());
                log.warn("Acquire task failed: {}, id = {}", errorMsg, taskId);

                final int errorCount = billCenterConfig.getMaxRetryTimes() + 1;
                billTaskDAO.updateToFailedByIdAndVersion(
                        TaskState.ACQUIRE_FAILED, errorCount, errorMsg,
                        nextRunTimeToUse,
                        taskId, task.getVersion() + 1
                );

                logTransitLog(traceId, "账单拉取失败", errorMsg);
                return false;
            }

            tempFile = Files.createTempFile(task.getBillType().name(), ".tmp").toFile();
            tempFile.deleteOnExit();

            final String suffix = billAcquireHandler.acquireTo(task, tempFile);
            if (null == suffix) {
                throw new BillCenterBusinessException(BillCenterErrorCode.BILL_ACQUIRE_FAILED);
            }

            final String ossPath = generateOssPath(task, suffix);
            try (final FileInputStream in = new FileInputStream(tempFile)) {
                appStorageService.writeStream(ossPath, in);
            }

            final int rows = billTaskDAO.updateToAcquireSuccessByIdAndVersion(ossPath, ZonedDateTime.now(), taskId, task.getVersion() + 1);
            Preconditions.checkState(rows == 1, "Update task to success failed, id = {}", taskId);

            logTransitLog(traceId, "账单拉取完成", "状态更新为待解析");
            dispatchTask(taskId);

            return true;
        } catch (final RuntimeException | IOException thrown) {
            final Throwable cause = Throwables.getRootCause(thrown);
            final String message = cause.getMessage();
            final int errorCount = Optional.ofNullable(task.getErrorCount()).map(c -> c + 1).orElse(1);
            final long delay = billCenterConfig.isBackoffRetry() ? (long) Math.pow(2, errorCount) : 0;
            final ZonedDateTime nextRetryTime = ZonedDateTime.now().plusMinutes(delay);

            billTaskDAO.updateToFailedByIdAndVersion(
                    TaskState.ACQUIRE_FAILED, errorCount, message,
                    nextRetryTime,
                    taskId, task.getVersion() + 1
            );

            logTransitLog(traceId, "账单拉取失败", message);

            if (thrown instanceof BillCenterBusinessException bizThrown) {
                throw bizThrown;
            }
            throw new BillCenterBusinessException(BillCenterErrorCode.BILL_ACQUIRE_FAILED, cause);
        } finally {
            if (null != tempFile && !tempFile.delete()) {   // NOSONAR
                log.warn("Failed to delete temp file: {}", tempFile.getAbsolutePath());
            }
        }
    }

    private void dispatchTask(final Long taskId) {
        log.info("Dispatch acquire task: {}", taskId);
        billTaskEventPublisher.publishWaitProcessEvent(taskId);
    }

    private void logTransitLog(final String traceId, final String title, final String message) {
        final TransitLogEntity startAcquireLog = TransitLogEntity.builder()
                .traceId(traceId)
                .title(title)
                .message(Errors.format(message, 200))
                .build();
        EntityUtils.fill(startAcquireLog, ZonedDateTime.now());
        transitLogDAO.create(startAcquireLog);
    }

    String generateOssPath(final BillTaskEntity task, final String suffix) {
        /*-
         * 根据约定 OSS 路径如下:
         * ${brand}/original/${year}/${month}/${day}/${merchant}/${bill-type}/${original_filename}.${suffix}.
         */
        final String brand = task.getBrand();
        final String merchantId = task.getMerchantId();
        final BillType billType = task.getBillType();
        final String brandToUse = StringUtils.hasText(brand) ? brand : UNKNOWN;
        final String merchantIdToUse = StringUtils.hasText(merchantId) ? merchantId : UNKNOWN;
        final ZonedDateTime now = ZonedDateTime.now();
        final String filenameTimestamp = now.format(FILENAME_DATE_FORMATTER);

        return String.format(
                "%s/original/%s/%s/%s/%s/%s/%s_%s.%s",
                brandToUse, now.getYear(), now.getMonth().getValue(), now.getDayOfMonth(),
                merchantIdToUse, billType, merchantIdToUse, filenameTimestamp, suffix
        );
    }

    BillAcquireHandler determineBillAcquireHandler(final Channel channel, final GrantType grantType,
                                                   final BillType billType, final String apiVersion) {
        return billAcquireHandlers.stream()
                .filter(handler -> handler.matches(channel, grantType, billType, apiVersion))
                .findFirst()
                .orElse(null);
    }

}