package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.Channel;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface BlacklineBillArchiveHandler {
    enum Group {
        NONE,
        TMALL,
        NON_TMALL
    }

    boolean matches(final Channel channel);

    boolean isGroupingByTmall();

    /**
     * Archive data to file.
     *
     * @param taskIds the task id list
     * @return null if no data, otherwise data written file
     * @throws IOException if io error occurs
     */
    Map<String,File> archive(final Group group, final long seqInGroup, final List<Long> taskIds) throws IOException;

    void sendToBlackline(final Map<String, File> filenameToFile) throws IOException;

}
