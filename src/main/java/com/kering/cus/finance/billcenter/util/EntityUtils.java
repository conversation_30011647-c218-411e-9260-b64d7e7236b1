package com.kering.cus.finance.billcenter.util;

import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZonedDateTime;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class EntityUtils {
    public static <K extends Serializable, T extends SoftDeleteMyBatisBaseEntity<K>> T fill(final T entity,
                                                                                            final ZonedDateTime createDate) {
        entity.setCreatedDate(createDate);
        entity.setModifiedDate(ZonedDateTime.now());
        entity.setDeleted(Boolean.FALSE);
        entity.setVersion(0L);
        return entity;
    }
}