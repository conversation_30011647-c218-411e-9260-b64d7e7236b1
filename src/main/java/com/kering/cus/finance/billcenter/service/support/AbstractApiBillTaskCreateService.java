package com.kering.cus.finance.billcenter.service.support;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.*;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import com.kering.cus.finance.billcenter.service.ApiBillTaskCreateService;
import com.kering.cus.finance.billcenter.util.BizPreconditions;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Oauth2 grant API bill task create support.
 *
 * <AUTHOR>
 * @since 20250701
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractApiBillTaskCreateService implements ApiBillTaskCreateService {
    private static final int MAX_BATCH_SIZE = 500;

    protected final Channel channel;
    protected final BillTaskDAO billTaskDAO;
    protected final TransitLogDAO transitLogDAO;
    protected final MerchantConfigDAO merchantConfigDAO;
    private final MerchantGrantConfigDAO merchantGrantConfigDAO;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(final Channel channel) {
        return Objects.equals(this.channel, channel);
    }

    /**
     * Creates all merchant bill task in given channel.
     *
     * @param when   the trigger time
     * @param source the task source
     * @return created task id list
     */
    @Override
    @Transactional
    public List<Long> createTaskIfNecessary(final ZonedDateTime when, final String source) {
        final List<MerchantGrantConfigEntity> merchants = findEnabledMerchantConfigs(channel);
        return createTaskIfNecessary(merchants, getSupportedBillTypes(), when, source);
    }

    /**
     * Creates given merchant bill task in given channel.
     *
     * @param merchantId the merchant id
     * @param billTypes  the bill type
     * @param when       the trigger time
     * @param source     the task source
     * @return created task id list
     */
    @Override
    @Transactional
    public List<Long> createTaskIfNecessary(final String merchantId, final List<BillType> billTypes,
                                            final ZonedDateTime when, final String source) {
        final MerchantGrantConfigEntity merchant = this.findEnabledMerchantConfig(channel, merchantId);
        BizPreconditions.checkNotNull(merchant, BillCenterErrorCode.MERCHANT_NOT_FOUND);

        return createTaskIfNecessary(Collections.singletonList(merchant), billTypes, when, source);
    }


    /**
     * Creates bill tasks for given merchants.
     *
     * @param merchants merchants
     * @param billTypes expected bill types
     * @param when      the trigger time
     * @param source    the task source
     * @return created task id list
     */
    private List<Long> createTaskIfNecessary(final List<MerchantGrantConfigEntity> merchants,
                                             final List<BillType> billTypes,
                                             final ZonedDateTime when, final String source) {
        if (CollectionUtils.isEmpty(merchants)) {
            log.info("No granted merchant found for '{}'", channel);
            return Collections.emptyList();
        }

        final List<String> taskKeys = Lists.newArrayListWithExpectedSize(merchants.size() * 2);
        final List<BillTaskEntity> tasksToCreate = Lists.newArrayListWithExpectedSize(merchants.size() * 2);
        for (final MerchantGrantConfigEntity merchant : merchants) {
            final String merchantId = merchant.getMerchantId();
            final GrantType grantType = merchant.getGrantType();
            final String kmsBrandId = merchant.getKmsBrandId();
            final List<BillTaskEntity> taskRequests = doCreateTasksIfNecessary(
                    channel, merchantId, grantType, kmsBrandId, billTypes, when, source
            );
            if (CollectionUtils.isEmpty(taskRequests)) {
                continue;
            }
            tasksToCreate.addAll(taskRequests);
            taskKeys.addAll(taskRequests.stream().map(BillTaskEntity::getTaskUniqueKey).toList());
        }

        final List<String> duplicateKeys = checkDuplicateKeys(taskKeys);
        tasksToCreate.removeIf(task -> duplicateKeys.contains(task.getTaskUniqueKey()));
        if (CollectionUtils.isEmpty(tasksToCreate)) {
            return Collections.emptyList();
        }

        final ZonedDateTime now = ZonedDateTime.now();
        final List<TransitLogEntity> transitLogs = tasksToCreate.stream()
                .map(task -> EntityUtils.fill(createTransitLog(task), now)).toList();

        Lists.partition(tasksToCreate, MAX_BATCH_SIZE).forEach(billTaskDAO::createBatch);
        Lists.partition(transitLogs, MAX_BATCH_SIZE).forEach(transitLogDAO::createBatch);
        return tasksToCreate.stream().filter(e -> TaskState.WAIT_ACQUIRE.equals(e.getState())).map(BillTaskEntity::getId).toList();
    }

    protected List<BillTaskEntity> doCreateTasksIfNecessary(final Channel channel,
                                                            final String merchantId, final GrantType grantType,
                                                            final String kmsBrandId, final List<BillType> billTypes,
                                                            final ZonedDateTime when, final String source) {
        // merchant mapping config.
        final MerchantConfigEntity config = merchantConfigDAO.findByChannelAndMerchantId(channel, merchantId);
        final Optional<MerchantConfigEntity> configOpt = Optional.ofNullable(config);
        final String brand = configOpt.map(MerchantConfigEntity::getBrand).orElse(null);
        final boolean isTmall = configOpt.map(MerchantConfigEntity::getIsTmall).orElse(false);
        final boolean syncToSap = configOpt.map(MerchantConfigEntity::getIsSyncToSap).orElse(false);
        final boolean syncToBlackline = configOpt.map(MerchantConfigEntity::getIsSyncToBlackline).orElse(false);
        final boolean isWosaipayMerchant = configOpt.map(MerchantConfigEntity::getIsWosaipayMerchant).orElse(false);
        final List<BillType> typesToCreate = determineBillTypes(billTypes, isWosaipayMerchant);

        // create bill tasks.
        final ZonedDateTime now = ZonedDateTime.now();
        final ZonedDateTime endTime = when.toLocalDate().atStartOfDay(when.getZone());
        final ZonedDateTime startTime = endTime.minusDays(1);

        final List<BillTaskEntity> tasks = Lists.newArrayListWithExpectedSize(typesToCreate.size());
        for (final BillType billType : typesToCreate) {
            final BillTaskEntity task = new BillTaskEntity();
            task.setChannel(channel);
            task.setBrand(brand);
            task.setMerchantId(merchantId);
            task.setGrantType(grantType);
            task.setExtraParams(kmsBrandId);

            task.setBillType(billType);
            task.setBillStartTime(startTime);
            task.setBillEndTime(endTime);

            task.setTraceId(generateTaskTraceId());
            task.setTaskUniqueKey(getTaskKey(channel, merchantId, billType, when, source));

            task.setState(TaskState.WAIT_ACQUIRE);
            task.setSource(source);

            task.setIsTmall(isTmall);
            task.setIsSyncToSap(determineSyncToSap(billType, syncToSap));
            task.setIsSyncToBlackline(determineSyncToBlackline(billType, syncToBlackline));
            task.setSapSyncState(SyncState.UNSET);
            task.setBlacklineSyncState(SyncState.UNSET);

            task.setErrorCount(0);
            task.setNextRunTime(now);
            task.setSapErrorCount(0);
            task.setBlacklineErrorCount(0);

            if (null == config) {
                task.setState(TaskState.ACQUIRE_FAILED);
                task.setErrorCount(999);
                task.setErrorMsg("API 商户配置不存在");
            }

            tasks.add(EntityUtils.fill(task, now));
        }
        return tasks;
    }

    protected boolean determineSyncToSap(final BillType billType, final boolean udfSyncToSap) {
        log.trace("SKIP sonar billType: {}", billType);
        return udfSyncToSap;
    }

    protected boolean determineSyncToBlackline(final BillType billType, final boolean udfSyncToBlackline) {
        log.trace("SKIP sonar: {}", billType);
        return udfSyncToBlackline;
    }

    protected List<BillType> determineBillTypes(final List<BillType> billTypes, final boolean isWosaipayMerchant) {
        log.trace("SKIP sonar: {}", isWosaipayMerchant);
        return billTypes;
    }

    private TransitLogEntity createTransitLog(final BillTaskEntity task) {
        final boolean success = TaskState.WAIT_ACQUIRE.equals(task.getState());
        return TransitLogEntity.builder()
                .traceId(task.getTraceId())
                .title(success ? "任务创建成功" : "任务创建失败")
                .message(success ? "任务状态更新为待拉取" : task.getErrorMsg())
                .build();
    }

    private List<String> checkDuplicateKeys(final List<String> taskKeys) {
        return !CollectionUtils.isEmpty(taskKeys)
                ? billTaskDAO.findExistsTaskUniqueKeys(taskKeys)
                : Collections.emptyList();
    }


    private MerchantGrantConfigEntity findEnabledMerchantConfig(final Channel channel, final String merchantId) {
        return merchantGrantConfigDAO.findEnabledMerchantGrantConfig(
                channel, Arrays.asList(GrantType.ISV, GrantType.MERCHANT), merchantId
        );
    }

    private List<MerchantGrantConfigEntity> findEnabledMerchantConfigs(final Channel channel) {
        return merchantGrantConfigDAO.findEnabledMerchantGrantConfigs(
                channel, Arrays.asList(GrantType.ISV, GrantType.MERCHANT)
        );
    }

    protected String getTaskKey(final Channel channel, final String merchantId,
                                final BillType billType, final ZonedDateTime when, final String source) {
        final String timestamp = when.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        return String.format("%s_%s_%s_%s_%s", channel.name(), merchantId, billType.name(), timestamp, source);
    }

    protected String generateTaskTraceId() {
        return UUID.randomUUID().toString();
    }

    protected abstract List<BillType> getSupportedBillTypes();

}