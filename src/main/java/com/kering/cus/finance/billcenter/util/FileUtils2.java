package com.kering.cus.finance.billcenter.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public abstract class FileUtils2 {

    public static Path createTempFile(final String prefix, final String suffix) throws IOException {
        final Path tempDirectory = getTempDirectory();
        if (!Files.exists(tempDirectory)) {
            Files.createDirectories(tempDirectory);
        }
        final String name = prefix + UUID.randomUUID() + suffix;
        return Files.createFile(tempDirectory.resolve(name));
    }

    private static Path getTempDirectory() {
        return Paths.get(System.getProperty("java.io.tmpdir"));
    }

}
