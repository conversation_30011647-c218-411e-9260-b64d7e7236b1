package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.WechatTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WechatTradeFlowEntity;
import com.kering.cus.finance.billcenter.util.CsvParserUtil;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

@ExtendWith(MockitoExtension.class)
class WechatTradeProcessHandlerTest {

  @InjectMocks
  private WechatTradeProcessHandler handler;

  @Mock
  private WechatTradeFlowDAO wechatTradeFlowDAO;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void givenWechatChannelAndMerchantGrantTypeAndTradeBillType_whenMatchesInvoked_thenReturnsTrue() {
    assertTrue(handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.TRADE_FLOW));
    assertFalse(handler.matches(Channel.ALIPAY, GrantType.MERCHANT, BillType.TRADE_FLOW));
    assertFalse(handler.matches(Channel.WECHAT, GrantType.ISV, BillType.TRADE_FLOW));
    assertFalse(handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.FUND_FLOW));
  }

  @Test
  void givenInvalidParameters_whenProcessInvoked_thenReturnsEmptyList() {
    File tempFile = null;
    BillTaskEntity billTaskEntity = null;
    List<String> result = handler.process(tempFile, billTaskEntity);
    assertTrue(CollectionUtils.isEmpty(result));
  }

  @Test
  void givenDuplicatedData_whenBuildEntityInvoked_thenThrowsBillCenterBusinessException() {
    List<String> data = Lists.newArrayList("2025-01-01 12:00:00", "appid1", "merchant1");
    BillTaskEntity task = new BillTaskEntity();
    task.setMerchantId("test");

    assertNotNull(data);
  }

  @Test
  void givenValidData_whenBuildEntityInvoked_thenReturnsEntity() {
    List<String> data = Lists.newArrayList("2025-01-01 12:00:00", "appid1", "merchant1");
    List<String> headerMap = Lists.newArrayList("交易时间", "公众账号ID", "商户号");
    BillTaskEntity task = new BillTaskEntity();
    task.setMerchantId("test");


    WechatTradeFlowEntity entity = handler.buildEntity(data, task, headerMap);
    assertNotNull(entity);
    assertEquals("2025-01-01 12:00:00", entity.getTransactionTime());
    assertEquals("appid1", entity.getAppId());
    assertEquals("merchant1", entity.getMainMerchant());
  }

  @Test
  void givenIOException_whenDealCsvFileToDbInvoked_thenThrowsException() throws IOException {
    BillTaskEntity billTaskEntity = new BillTaskEntity();
    List<WechatTradeFlowEntity> batchList = new ArrayList<>(Constants.BATCH_SIZE);
    BufferedReader reader = mock(BufferedReader.class);

    when(reader.readLine()).thenThrow(new IOException("Read error"));

    assertThrows(IOException.class, () -> handler.dealCsvFileToDb(billTaskEntity, batchList, reader));
  }

  @Test
  void givenValidCsvFile_whenDealCsvFileToDbInvoked_thenInsertDataSuccessfully() throws IOException {
    BillTaskEntity billTaskEntity = new BillTaskEntity();
    List<WechatTradeFlowEntity> batchList = new ArrayList<>(Constants.BATCH_SIZE);
    BufferedReader reader = mock(BufferedReader.class);

    List<String> headerMap = Lists.newArrayList("交易时间", "公众账号ID", "商户号");
    List<String> rowData1 = Lists.newArrayList("2025-01-01 10:00:00", "appid1", "merchant1");
    List<String> rowData2 = Lists.newArrayList("2025-01-02 11:00:00", "appid2", "merchant2");
    List<String> summaryRow = Lists.newArrayList("总交易单数", "100", "200");

    try (MockedStatic<CsvParserUtil> csvParserUtilMockedStatic = mockStatic(CsvParserUtil.class)) {
      when(reader.readLine())
          .thenReturn("交易时间,公众账号ID,商户号,应结订单金额,代金券金额,退款金额,充值券退款,手续费,订单金额,申请退款金额")
          .thenReturn("2025-01-01 10:00:00,appid1,merchant1,100.00,10.00,20.00,5.00,2.00,120.00,15.00")
          .thenReturn("2025-01-02 11:00:00,appid2,merchant2,200.00,20.00,30.00,8.00,3.00,230.00,20.00")
          .thenReturn("总交易单数,100,200")
          .thenReturn(null);

      csvParserUtilMockedStatic.when(
              () -> CsvParserUtil.parseCsvLine("交易时间,公众账号ID,商户号,应结订单金额,代金券金额,退款金额,充值券退款,手续费,订单金额,申请退款金额"))
          .thenReturn(headerMap);
      csvParserUtilMockedStatic.when(
          () -> CsvParserUtil.parseCsvLine("2025-01-01 10:00:00,appid1,merchant1,100.00,10.00,20.00,5.00,2.00,120.00,15.00")).thenReturn(rowData1);
      csvParserUtilMockedStatic.when(
          () -> CsvParserUtil.parseCsvLine("2025-01-02 11:00:00,appid2,merchant2,200.00,20.00,30.00,8.00,3.00,230.00,20.00")).thenReturn(rowData2);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.parseCsvLine("总交易单数,100,200")).thenReturn(summaryRow);

      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(rowData1, headerMap, "交易时间", "总交易单数")).thenReturn(false);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(rowData2, headerMap, "交易时间", "总交易单数")).thenReturn(false);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(summaryRow, headerMap, "交易时间", "总交易单数")).thenReturn(true);

      // 模拟 CsvParserUtil.safeParseBigDecimal 方法
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "应结订单金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("100.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "代金券金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("10.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "退款金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("20.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "充值券退款", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("5.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "手续费", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("2.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "订单金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("120.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "申请退款金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("15.00"));

      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "应结订单金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("200.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "代金券金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("20.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "退款金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("30.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "充值券退款", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("8.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "手续费", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("3.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "订单金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("230.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "申请退款金额", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("20.00"));

      handler.dealCsvFileToDb(billTaskEntity, batchList, reader);
      assertNotNull(reader);
    }
  }
}
