package com.kering.cus.finance.billcenter.service.jd;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.Collections;
import java.util.List;
import org.springframework.util.CollectionUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JdSapBillArchiveHandlerTest {

    @InjectMocks
    private JdSapBillArchiveHandler handler;

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private JdWalletFlowDAO jdWalletFlowDAO;


    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

    }



    @Test
    void givenJDAndFUND_FLOW_whenMatches_thenReturnsTrue() {
        assertTrue(handler.matches(Channel.JD, BillType.FUND_FLOW));
        assertFalse(handler.matches(Channel.ALIPAY, BillType.FUND_FLOW));
        assertFalse(handler.matches(Channel.JD, BillType.TRADE_FLOW));
    }


    @Test
    void givenIsSyncToSapFalse_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(false);

        Map<String, File> result = handler.archive(1L, config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void givenTaskNotFound_whenArchive_thenThrowsException() {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);

        when(billTaskDAO.findById(1L)).thenReturn(java.util.Optional.empty());

        assertThrows(BillCenterBusinessException.class, () -> handler.archive(1L, config));
    }



    @Test
    void givenNoData_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setMerchantId("M123");
        config.setBrand("B1");

        BillTaskEntity task = new BillTaskEntity();
        task.setId(123L);
        when(billTaskDAO.findById(123L)).thenReturn(java.util.Optional.of(task));
        when(jdWalletFlowDAO.findJdWalletBillByParams("M123", 123L, "B1")).thenReturn(Collections.emptyList());

        Map<String, File> result = handler.archive(123L, config);
        assertTrue(CollectionUtils.isEmpty(result));
    }


    @Test
    void givenDataEmpty_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setSapProfitCenter("PC123");
        config.setSapGlAccount1("GL1");
        config.setSapGlAccount2("GL2");
        config.setMerchantId("M123");
        config.setBrand("BRAND1");

        BillTaskEntity task = new BillTaskEntity();
        task.setId(1L);
        when(billTaskDAO.findById(1L)).thenReturn(java.util.Optional.of(task));
        when(jdWalletFlowDAO.findJdWalletBillByParams("M123", 1L, "BRAND1")).thenReturn(Collections.emptyList());

        Map<String, File> result = handler.archive(1L, config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void givenDataExists_whenArchive_thenCreatesFile() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setSapProfitCenter("PC123");
        config.setSapGlAccount1("GL1");
        config.setSapGlAccount2("GL2");
        config.setMerchantId("M123");
        config.setBrand("BRAND1");

        BillTaskEntity task = new BillTaskEntity();
        task.setId(1L);
        when(billTaskDAO.findById(1L)).thenReturn(java.util.Optional.of(task));

        JdWalletFlowEntity entity = new JdWalletFlowEntity();
        entity.setBrand("BRAND1");
        entity.setChannel("JD");
        entity.setMerchantId("M123");
        entity.setAccountCode("AC123");
        entity.setAccountName("Account Name");
        entity.setTransactionTime("2024-01-01 00:00:00");
        entity.setMerchantOrderId("ORDER123");
        entity.setBalance(BigDecimal.valueOf(100.00));
        entity.setIncome(BigDecimal.valueOf(50.00));
        entity.setExpense(BigDecimal.valueOf(50.00));
        entity.setTransactionNote("Note");
        entity.setBillingTime("2024-01-01");
        entity.setPlatformOrderId("P123");

        List<JdWalletFlowEntity> list = Lists.newArrayList(entity);
        when(jdWalletFlowDAO.findJdWalletBillByParams("M123", 1L, "BRAND1")).thenReturn(list);

        Map<String, File> result = handler.archive(1L, config);
        assertNotNull(result);
        File file = result.get(".csv");
        assertTrue(file.exists());

        Files.deleteIfExists(file.toPath());
        Files.deleteIfExists(result.get(".xlsx").toPath());
    }

    @Test
    void givenLargeDataBatch_whenArchive_thenWritesInBatches() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setSapProfitCenter("PC123");
        config.setSapGlAccount1("GL1");
        config.setSapGlAccount2("GL2");
        config.setMerchantId("M123");
        config.setBrand("BRAND1");

        BillTaskEntity task = new BillTaskEntity();
        task.setId(1L);
        when(billTaskDAO.findById(1L)).thenReturn(java.util.Optional.of(task));

        List<JdWalletFlowEntity> list = Lists.newArrayList();
        for (int i = 0; i < 1500; i++) {
            JdWalletFlowEntity entity = new JdWalletFlowEntity();
            entity.setBrand("BRAND1");
            entity.setChannel("JD");
            entity.setMerchantId("M123");
            entity.setAccountCode("AC123");
            entity.setAccountName("Account Name");
            entity.setTransactionTime("2024-01-01 00:00:00");
            entity.setMerchantOrderId("ORDER123");
            entity.setBalance(BigDecimal.valueOf(100.00));
            entity.setIncome(BigDecimal.valueOf(50.00));
            entity.setExpense(BigDecimal.valueOf(50.00));
            entity.setTransactionNote("Note");
            entity.setBillingTime("2024-01-01");
            entity.setPlatformOrderId("P123");
            list.add(entity);
        }

        when(jdWalletFlowDAO.findJdWalletBillByParams("M123", 1L, "BRAND1")).thenReturn(list);

        Map<String, File> result = handler.archive(1L, config);
        assertNotNull(result);
        File file = result.get(".csv");
        assertTrue(file.exists());

        Files.deleteIfExists(file.toPath());
        Files.deleteIfExists(result.get(".xlsx").toPath());
    }


}
