package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.WechatTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.WechatTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler.Group;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

@ExtendWith(MockitoExtension.class)
class WechatTradeBillPushBlackLineHandlerTest {

  @InjectMocks
  private WechatTradeBillPushBlackLineHandler handler;

  @Mock
  private WechatTradeFlowDAO wechatTradeFlowDAO;

  @Mock
  private SftpConfig sftpConfig;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void givenWECHATAndTRADE_FLOW_whenIsGroupingByTmall_thenReturnsFalse() {
    assertFalse(handler.isGroupingByTmall());
  }

  @Test
  void givenNoData_whenArchive_thenReturnsNull() throws IOException {
    Group group = Mockito.mock(Group.class);
    List<Long> taskIds = new ArrayList<>();
    taskIds.add(1L);

    when(wechatTradeFlowDAO.selectTradeBillCount(taskIds)).thenReturn(0L);

    Map<String, File> result = handler.archive(group, 1L, taskIds);
    assertTrue(CollectionUtils.isEmpty(result));
  }

  @Test
  void givenDataExists_whenArchive_thenCreatesFile() throws IOException {
    Group group = Mockito.mock(Group.class);
    List<Long> taskIds = new ArrayList<>();
    taskIds.add(1L);

    when(wechatTradeFlowDAO.selectTradeBillCount(taskIds)).thenReturn(1L);

    Page<WechatTradeFlowEntity> page = new Page<>();
    List<WechatTradeFlowEntity> records = new ArrayList<>();
    WechatTradeFlowEntity entity = new WechatTradeFlowEntity();
    entity.setBrand("BRAND1");
    entity.setChannel(Channel.WECHAT);
    entity.setMerchantId("M123");
    entity.setOrderAmount(BigDecimal.valueOf(100.00));
    records.add(entity);
    page.setRecords(records);

    when(wechatTradeFlowDAO.selectTradeBillPage(any(Page.class), eq(taskIds))).thenReturn(page);



      Map<String, File> result = handler.archive(group, 1L, taskIds);
      assertNotNull(result);
      File file = result.get(".csv");
      assertTrue(file.exists());
      for (Entry<String, File> entry : result.entrySet()) {
        File value = entry.getValue();
        Files.deleteIfExists(value.toPath());
      }

  }

  @Test
  void givenArchiveThrowsException_whenArchive_thenThrowsBillCenterBusinessException() {
    Group group = Mockito.mock(Group.class);
    List<Long> taskIds = new ArrayList<>();
    taskIds.add(1L);

    when(wechatTradeFlowDAO.selectTradeBillCount(taskIds)).thenReturn(1L);
    when(wechatTradeFlowDAO.selectTradeBillPage(any(Page.class), eq(taskIds))).thenThrow(new RuntimeException("Test exception"));

    assertThrows(BillCenterBusinessException.class, () -> handler.archive(group, 1L, taskIds));
  }

  @Test
  void givenValidRecord_whenFormatCsvLine_thenReturnsFormattedLine() {
    WechatTradeFlowEntity wechatTradeFlowEntity = new WechatTradeFlowEntity();
    wechatTradeFlowEntity.setBrand("BRAND1");
    wechatTradeFlowEntity.setChannel(Channel.WECHAT);
    wechatTradeFlowEntity.setMerchantId("M123");
    wechatTradeFlowEntity.setOrderAmount(BigDecimal.valueOf(100.00));

    String line = handler.formatCsvLine(wechatTradeFlowEntity);
    assertNotNull(line);
    assertTrue(line.contains("BRAND1"));
    assertTrue(line.contains("WECHAT"));
    assertTrue(line.contains("M123"));
    assertTrue(line.contains("100.00"));
  }
}
