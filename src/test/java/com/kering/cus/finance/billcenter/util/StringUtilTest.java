package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class StringUtilTest {

    @Test
    void givenNullInput_whenTrimAndRemoveSpecialChars_thenReturnsNull() {
        assertNull(StringUtils.trimAndRemoveSpecialChars(null));
    }

    @Test
    void givenEmptyString_whenTrimAndRemoveSpecialChars_thenReturnsEmptyString() {
        assertEquals("", StringUtils.trimAndRemoveSpecialChars(""));
    }


    @Test
    void givenStringWithSpecialChars_whenTrimAndRemoveSpecialChars_thenReturnsTrimmedString() {
        String input = "  abc\"\\t\\r\\n\\\\\uFEFF  ";
        assertEquals("abc", StringUtils.trimAndRemoveSpecialChars(input));
    }

    @Test
    void givenStringStartsWithEqual_whenTrimAndRemoveSpecialChars_thenReturnsRemovedFirstEqual() {
        String input = "  =abc  ";
        assertEquals("abc", StringUtils.trimAndRemoveSpecialChars(input));
    }

    @Test
    void givenStringStartsWithEqualNoTrim_whenTrimAndRemoveSpecialChars_thenReturnsRemovedFirstEqual() {
        String input = "=abc";
        assertEquals("abc", StringUtils.trimAndRemoveSpecialChars(input));
    }

    @Test
    void givenStringWithEqualInside_whenTrimAndRemoveSpecialChars_thenReturnsOriginalString() {
        String input = "abc=def";
        assertEquals("abc=def", StringUtils.trimAndRemoveSpecialChars(input));
    }


    @Test
    void givenStringWithMultipleEquals_whenTrimAndRemoveSpecialChars_thenReturnsStringWithFirstEqualRemoved() {
        String input = "  =abc=def  ";
        assertEquals("abc=def", StringUtils.trimAndRemoveSpecialChars(input));
    }


}
