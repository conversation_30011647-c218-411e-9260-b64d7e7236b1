package com.kering.cus.finance.billcenter.util;

import com.jcraft.jsch.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static com.kering.cus.finance.billcenter.util.SftpUtils.connect;
import static com.kering.cus.finance.billcenter.util.SftpUtils.disconnect;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SftpUtilsTest {

    @Mock
    private JSch jsch;
    @Mock
    private Session session;
    @Mock
    private ChannelSftp sftp;

    @Test
    void shouldThrowIOException_whenConnectError() {
        IOException exception = assertThrows(IOException.class, () ->
                connect(null, 22, "user", "pass", new byte[]{1}, null, 1000));

        assertTrue(exception.getMessage().contains("refused"));
    }

    @Test
    void shouldThrowIOException_whenHostIsNull() throws Exception {
        JSchException jschException = new JSchException("Connection refused");
        when(jsch.getSession(anyString(), eq(null), anyInt())).thenThrow(jschException);

        IOException exception = assertThrows(IOException.class, () ->
                connect(jsch, null, 22, "user", "pass", null, null, 1000));

        assertTrue(exception.getMessage().contains("Connection refused"));
    }

    @Test
    void shouldConnectSuccessfullyWithPrivateKey() throws Exception {
        byte[] privateKey = "private_key".getBytes();
        byte[] passphrase = "passphrase".getBytes();

        when(jsch.getSession(anyString(), anyString(), anyInt())).thenReturn(session);
        when(session.openChannel("sftp")).thenReturn(sftp);

        ChannelSftp result = connect(jsch, "host", 22, "user", "pass", privateKey, passphrase, 1000);

        assertNotNull(result);
        verify(jsch).addIdentity(anyString(), eq(privateKey), eq(null), eq(passphrase));
        verify(session).connect(1000);
        verify(sftp).connect();
    }

    @Test
    void shouldDisconnectChannelAndSessionSuccessfully() throws JSchException {
        when(sftp.isClosed()).thenReturn(false);
        when(sftp.getSession()).thenReturn(session);
        when(session.isConnected()).thenReturn(true);

        disconnect(sftp);

        verify(sftp).disconnect();
        verify(session).disconnect();
    }

    @Test
    void shouldDisconnectChannelSuccessfully() throws JSchException {
        when(sftp.isClosed()).thenReturn(false);
        when(sftp.getSession()).thenReturn(session);
        when(session.isConnected()).thenReturn(false);

        disconnect(sftp);

        verify(sftp).disconnect();
    }

    @Test
    void shouldHandleNullChannelOnDisconnect() {
        disconnect(null);
        verifyNoInteractions(sftp);
    }

    @Test
    void shouldThrowIOException_whenConnectTimeout() throws Exception {
        JSchException jschException = new JSchException("timeout");
        when(jsch.getSession(anyString(), anyString(), anyInt())).thenThrow(jschException);

        IOException exception = assertThrows(IOException.class, () ->
                connect(jsch, "host", 22, "user", "pass", null, null, 1000));

        assertTrue(exception.getMessage().contains("timeout"));
        verify(jsch).getSession(anyString(), anyString(), anyInt());
    }

    @Test
    void shouldCreateDirectoriesSuccessfully_whenPathDoesNotExist() throws Exception {
        SftpException ex = new SftpException(2, "NoSuchFile");
        when(sftp.stat("/dir1")).thenThrow(ex);

        assertTrue(SftpUtils.mkdirs(sftp, "/dir1/dir2"));
        verify(sftp, times(2)).stat(anyString());
        verify(sftp).mkdir("/dir1");
        verify(sftp).mkdir("/dir1/dir2");
    }

    @Test
    void shouldThrowException_whenFileExistsButNotDirectory() throws Exception {
        SftpATTRS attrs = mock(SftpATTRS.class);
        when(attrs.isDir()).thenReturn(false);

        when(sftp.stat("/existing")).thenReturn(attrs);

        IOException exception = assertThrows(IOException.class, () ->
                SftpUtils.mkdirs(sftp, "/existing"));

        assertTrue(exception.getMessage().contains("File already exists and is not directory"));
        verify(sftp).stat("/existing");
    }

    @Test
    void shouldHandleSftpException_whenCreatingDirectoryFails() throws Exception {
        when(sftp.stat("/dir1")).thenReturn(null);
        doThrow(new SftpException(4, "Permission denied")).when(sftp).mkdir("/dir1");

        assertFalse(SftpUtils.mkdirs(sftp, "/dir1"));
        verify(sftp).stat("/dir1");
        verify(sftp).mkdir("/dir1");
    }

}
