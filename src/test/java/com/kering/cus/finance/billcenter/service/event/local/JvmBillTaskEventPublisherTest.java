package com.kering.cus.finance.billcenter.service.event.local;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JvmBillTaskEventPublisherTest {

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private JvmBillTaskEventPublisher jvmBillTaskEventPublisher;

    @BeforeEach
    void setUp() {
        jvmBillTaskEventPublisher = new JvmBillTaskEventPublisher(eventPublisher);
    }

    @Test
    void shouldPublishWaitAcquireEventForSingleTaskId() {
        // Arrange
        Long taskId = 1L;

        // Act
        jvmBillTaskEventPublisher.publishWaitAcquireEvent(taskId);

        // Assert
        verify(eventPublisher).publishEvent(any(JvmBillTaskEvent.class));
    }

    @Test
    void shouldPublishWaitProcessEventForSingleTaskId() {
        // Arrange
        Long taskId = 1L;

        // Act
        jvmBillTaskEventPublisher.publishWaitProcessEvent(taskId);

        // Assert
        verify(eventPublisher).publishEvent(any(JvmBillTaskEvent.class));
    }

    @Test
    void shouldPublishMultipleWaitAcquireEvents() {
        // Arrange
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);

        // Act
        jvmBillTaskEventPublisher.publishWaitAcquireEvent(taskIds);

        // Assert
        verify(eventPublisher, times(3)).publishEvent(any(JvmBillTaskEvent.class));
    }

    @Test
    void shouldPublishMultipleWaitProcessEvents() {
        // Arrange
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);

        // Act
        jvmBillTaskEventPublisher.publishWaitProcessEvent(taskIds);

        // Assert
        verify(eventPublisher, times(3)).publishEvent(any(JvmBillTaskEvent.class));
    }
}