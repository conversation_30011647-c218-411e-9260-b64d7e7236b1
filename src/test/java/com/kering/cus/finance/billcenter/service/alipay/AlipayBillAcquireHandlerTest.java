package com.kering.cus.finance.billcenter.service.alipay;

import com.alipay.api.request.AlipayDataDataserviceBillDownloadurlQueryRequest;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.kering.cus.finance.billcenter.config.AlipayIsvAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.OauthAccessTokenDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alipay.api.DefaultAlipayClient;


@ExtendWith(MockitoExtension.class)
class AlipayBillAcquireHandlerTest {

    @InjectMocks
    private AlipayBillAcquireHandler handler;

    @Mock
    private AppSecretService appSecretService;

    @Mock
    private OauthAccessTokenDAO oauthAccessTokenDAO;


    private BillTaskEntity task;

    @Mock
    private HttpClient mockHttpClient;



    @BeforeEach
     void setUp()  {
        MockitoAnnotations.openMocks(this);
        task = new BillTaskEntity();
        task.setChannel(Channel.ALIPAY);
        task.setMerchantId("test_merchant");
        task.setBillStartTime(ZonedDateTime.now());
    }

    // matches 方法测试
    @Test
     void testMatches_ShouldReturnTrue_WhenCorrectParams() {
        boolean result = handler.matches(Channel.ALIPAY, GrantType.ISV, BillType.TRADE_FLOW, "v1");
        assertTrue(result);
    }

    @Test
     void testMatches_ShouldReturnFalse_WhenWrongChannel() {
        boolean result = handler.matches(Channel.WECHAT, GrantType.ISV, BillType.TRADE_FLOW, "v1");
        assertFalse(result);
    }



    @Test
     void testAcquireTo_ShouldReturnExtension_WhenSuccess() throws IOException, InterruptedException {

        when(appSecretService.getIsvAppSecret(anyString(), any())).thenReturn(Mockito.mock(AlipayIsvAppProperties.class));

        OauthAccessTokenEntity tokenEntity = mock(OauthAccessTokenEntity.class);
        when(oauthAccessTokenDAO.findByPlatformAndMerchantId(any(), anyString())).thenReturn(java.util.Optional.of(tokenEntity));
        String downUrl="http://dwbillcenter.alipay.com/downloadBillFile.resource?bizType=X&pid=X&fileType=X&bizDates=X&downloadFileName=X&fileId=X";
        // 模拟支付宝响应
        AlipayDataDataserviceBillDownloadurlQueryResponse alipayResponse = new AlipayDataDataserviceBillDownloadurlQueryResponse();
        alipayResponse.setCode("10000");
        alipayResponse.setBillDownloadUrl(downUrl);

        File targetFile = mock(File.class);
        Path mock1 = mock(Path.class);
        when(targetFile.toPath()).thenReturn(mock1);


        try (MockedConstruction<DefaultAlipayClient> mocked = mockConstruction(DefaultAlipayClient.class,
                (mock, context) -> when(mock.execute(any(AlipayDataDataserviceBillDownloadurlQueryRequest.class))).thenReturn(alipayResponse))) {

            when(mockHttpClient.send(any(HttpRequest.class), any())).thenReturn(mock(HttpResponse.class));

            String extension = handler.acquireTo(task, targetFile);
            assertNotNull(extension);
        }

    }




    @Test
     void testAcquireTo_ShouldReturnNull_WhenNotAlipayChannel() {
        task.setChannel(Channel.WECHAT);
        String result = handler.acquireTo(task, mock(File.class));
        assertNull(result);
    }

    @Test
     void testAcquireTo_ShouldThrowException_WhenConfigIsNull() {
        when(appSecretService.getIsvAppSecret(anyString(), any())).thenReturn(null);

        assertThrows(
                BillCenterBusinessException.class,
                () -> handler.acquireTo(task, mock(File.class)),
                "Expected acquireTo() to throw BillCenterBusinessException when appSecret is null"
        );

    }

    @Test
     void testAcquireTo_ShouldThrowException_WhenTokenIsNull() {
        AlipayIsvAppProperties config = new AlipayIsvAppProperties();
        when(appSecretService.getIsvAppSecret(anyString(), any())).thenReturn(config);
        when(oauthAccessTokenDAO.findByPlatformAndMerchantId(any(), anyString())).thenReturn(java.util.Optional.empty());

        assertThrows(BillCenterBusinessException.class, () -> handler.acquireTo(task, mock(File.class)));
    }



}
