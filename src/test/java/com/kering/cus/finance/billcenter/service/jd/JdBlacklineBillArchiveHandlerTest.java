package com.kering.cus.finance.billcenter.service.jd;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.JdTradeFlowDAO;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.springframework.util.CollectionUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JdBlacklineBillArchiveHandlerTest {

    @Mock
    private JdTradeFlowDAO jdTradeFlowDAO;

    @Mock
    private JdWalletFlowDAO jdWalletFlowDAO;


    @InjectMocks
    private JdBlacklineBillArchiveHandler handler;

    private List<Long> taskIds;

    @BeforeEach
    void setUp() {
        taskIds = Arrays.asList(1L, 2L);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenJdChannel_whenMatches_thenReturnsTrue() {
        boolean result = handler.matches(Channel.JD);
        assertTrue(result);
    }

    @Test
    void givenNonJdChannel_whenMatches_thenReturnsFalse() {
        boolean result = handler.matches(Channel.WOSAIPAY);
        assertFalse(result);
    }

    @Test
    void givenAnyInput_whenIsGroupingByTmall_thenReturnsFalse() {
        boolean result = handler.isGroupingByTmall();
        assertFalse(result);
    }

    @Test
    void givenTaskIdsWithTradeAndWalletData_whenArchive_thenCreatesFile() throws IOException {
        JdTradeFlowEntity tradeEntity = createTestTradeEntity();
        Page<JdTradeFlowEntity> tradePage = new Page<>();
        tradePage.setRecords(Collections.singletonList(tradeEntity));

        JdWalletFlowEntity walletEntity = createTestWalletEntity();
        Page<JdWalletFlowEntity> walletPage = new Page<>();
        walletPage.setRecords(Collections.singletonList(walletEntity));

        when(jdTradeFlowDAO.findJdTradeBillPageByParams(anyList(), anyInt(), anyInt()))
                .thenReturn(tradePage)
                .thenReturn(new Page<>());
        when(jdWalletFlowDAO.findJdWalletBillPageByParams(anyList(), anyString(), anyInt(), anyInt()))
                .thenReturn(walletPage)
                .thenReturn(new Page<>());


        Map<String, File> result = handler.archive(null, 1L, taskIds);

        assertNotNull(result);
    }

    @Test
    void givenTaskIdsWithOnlyTradeData_whenArchive_thenCreatesFile() throws IOException {
        JdTradeFlowEntity tradeEntity = createTestTradeEntity();
        Page<JdTradeFlowEntity> tradePage = new Page<>();
        tradePage.setRecords(Collections.singletonList(tradeEntity));

        when(jdTradeFlowDAO.findJdTradeBillPageByParams(anyList(), anyInt(), anyInt()))
                .thenReturn(tradePage)
                .thenReturn(new Page<>());


        Map<String, File> result = handler.archive(null, 2L, taskIds);

        assertNotNull(result);
    }

    @Test
    void givenTaskIdsWithNoData_whenArchive_thenReturnsNull() throws IOException {
        when(jdTradeFlowDAO.findJdTradeBillPageByParams(anyList(), anyInt(), anyInt()))
                .thenReturn(new Page<>());
        when(jdWalletFlowDAO.findJdWalletBillPageByParams(anyList(), anyString(), anyInt(), anyInt()))
                .thenReturn(new Page<>());

        Map<String, File> result = handler.archive(null, 1L, taskIds);

        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void givenDaoException_whenArchive_thenThrowsBusinessException()  {
        when(jdTradeFlowDAO.findJdTradeBillPageByParams(anyList(), anyInt(), anyInt()))
                .thenThrow(new IllegalArgumentException("Database error"));

        assertThrows(BillCenterBusinessException.class, () -> {
            handler.archive(null, 1L, taskIds);
        });
    }


    @Test
    void givenValidNoteWithPattern_whenExtractNotInfo_thenReturnsExtractedInfo() {
        String note = "其他信息售后服务单号123456，对应订单号789012更多内容";
        String result = JdBlacklineBillArchiveHandler.extractNotInfo(note);
        assertEquals("售后服务单号123456，对应订单号789012", result);
    }

    @Test
    void givenNoteWithoutPattern_whenExtractNotInfo_thenReturnsEmptyString() {
        String note = "普通备注信息";
        String result = JdBlacklineBillArchiveHandler.extractNotInfo(note);
        assertEquals("", result);
    }

    @Test
    void givenEmptyNote_whenExtractNotInfo_thenReturnsEmptyString() {
        String result = JdBlacklineBillArchiveHandler.extractNotInfo("");
        assertEquals("", result);

        result = JdBlacklineBillArchiveHandler.extractNotInfo(null);
        assertEquals("", result);
    }

    private JdTradeFlowEntity createTestTradeEntity() {
        JdTradeFlowEntity entity = new JdTradeFlowEntity();
        entity.setBrand("测试品牌");
        entity.setChannel("JD");
        entity.setMerchantId("merchant123");
        entity.setOrderId("order123");
        entity.setTransactionNo("trans123");
        entity.setTransactionType("交易类型");
        entity.setSkuCode("sku123");
        entity.setMerchantOrderId("merchantOrder123");
        entity.setSkuName("商品名称");
        entity.setSettlementStatus("已结算");
        entity.setOccurTime("2025-01-01");
        entity.setChargeableTime("2025-01-01");
        entity.setSettlementTime("2025-01-01");
        entity.setFeeItem("费用项");
        entity.setAmount(new BigDecimal("100.00"));
        entity.setCurrency("CNY");
        entity.setMerchantPaymentType("应付");
        entity.setSettlementNote("结算备注");
        entity.setShopCode("shop123");
        entity.setJdStoreCode("jdStore123");
        entity.setBrandStoreCode("brandStore123");
        entity.setStoreName("门店名称");
        entity.setNote("备注");
        entity.setPaymentType("支出");
        entity.setBillingDate("2023-01-01");
        entity.setSkuQty(1);
        return entity;
    }

    private JdWalletFlowEntity createTestWalletEntity() {
        JdWalletFlowEntity entity = new JdWalletFlowEntity();
        entity.setBrand("测试品牌");
        entity.setChannel("JD");
        entity.setMerchantId("merchant123");
        entity.setPlatformOrderId("platformOrder123");
        entity.setBillingTime("2025-01-01");
        entity.setExpense(new BigDecimal("50.00"));
        entity.setTransactionNote("售后服务单号123456，对应订单号789012");
        return entity;
    }
}
