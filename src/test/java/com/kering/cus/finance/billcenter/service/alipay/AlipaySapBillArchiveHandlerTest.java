package com.kering.cus.finance.billcenter.service.alipay;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;
import org.springframework.util.CollectionUtils;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AlipaySapBillArchiveHandlerTest {

    @InjectMocks
    private AlipaySapBillArchiveHandler handler;

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private AlipayBillFlowDAO alipayBillFlowDAO;


    private static final Long TASK_ID = 123L;
    private static final String MERCHANT_ID = "M123";
    private static final String BRAND = "BRAND1";
    private static final String SAP_PROFIT_CENTER = "PC123";
    private static final String SAP_GL_ACCOUNT1 = "GL123";
    private static final String SAP_GL_ACCOUNT2 = "GL456";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

    }

    @Test
    void givenConfigIsSyncToSapNull_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(null);

        Map<String, File> archive = handler.archive(TASK_ID, config);

        assertTrue(CollectionUtils.isEmpty(archive));
    }

    @Test
    void givenConfigIsSyncToSapIsFalse_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(false);

        Map<String, File> archive = handler.archive(TASK_ID, config);

        assertTrue(CollectionUtils.isEmpty(archive));
    }

    @Test
    void givenTaskNotFound_whenArchive_thenThrowsException() {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);

        when(billTaskDAO.findById(TASK_ID)).thenReturn(Optional.empty());

        assertThrows(IllegalArgumentException.class, () -> handler.archive(TASK_ID, config));

    }

    @Test
    void givenNoAlipayBillData_whenArchive_thenReturnsNull() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setSapProfitCenter(SAP_PROFIT_CENTER);
        config.setSapGlAccount1(SAP_GL_ACCOUNT1);
        config.setSapGlAccount2(SAP_GL_ACCOUNT2);

        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId(MERCHANT_ID);
        task.setBrand(BRAND);
        when(billTaskDAO.findById(anyLong())).thenReturn(Optional.of(task));
        when(alipayBillFlowDAO.findAlipayBillByParams(MERCHANT_ID, TASK_ID, BRAND)).thenReturn(Collections.emptyList());

        Map<String, File> result = handler.archive(TASK_ID, config);

        assertTrue(CollectionUtils.isEmpty(result));
    }


    @Test
    void givenDataExists_whenArchive_thenCreatesFile() throws IOException {
        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsSyncToSap(true);
        config.setSapProfitCenter("PC123");
        config.setSapGlAccount1("GL1");
        config.setSapGlAccount2("GL2");
        config.setMerchantId("M123");
        config.setBrand("BRAND1");

        BillTaskEntity task = new BillTaskEntity();
        task.setId(1L);
        task.setBrand("BRAND1");
        task.setMerchantId("M123");

        when(billTaskDAO.findById(1L)).thenReturn(java.util.Optional.of(task));

        AlipayBillFlowEntity entity = new AlipayBillFlowEntity();
        entity.setBrand("BRAND1");
        entity.setChannel("ALIPAY");
        entity.setMerchantId("M123");
        entity.setTransactionTime("2024-01-01 00:00:00");
        entity.setMerchantOrderId("ORDER123");
        entity.setBalance(BigDecimal.valueOf(100.00));
        entity.setIncome(BigDecimal.valueOf(50.00));
        entity.setExpense(BigDecimal.valueOf(50.00));

        List<AlipayBillFlowEntity> list = Lists.newArrayList(entity);
        when(alipayBillFlowDAO.findAlipayBillByParams(task.getMerchantId(), 1L, task.getBrand())).thenReturn(list);

        Map<String, File> result = handler.archive(1L, config);
        assertNotNull(result);
        File file = result.get(".csv");
        assertTrue(file.exists());

        Files.deleteIfExists(file.toPath());
        Files.deleteIfExists(result.get(".xlsx").toPath());
    }


}
