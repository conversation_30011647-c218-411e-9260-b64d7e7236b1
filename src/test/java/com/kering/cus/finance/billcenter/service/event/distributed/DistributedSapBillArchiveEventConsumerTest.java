package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.service.SapBillArchiveService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DistributedSapBillArchiveEventConsumerTest {

    @Mock
    private SapBillArchiveService sapBillArchiveService;

    @InjectMocks
    private DistributedSapBillArchiveEventConsumer sapBillArchiveEventConsumer;

    @BeforeEach
    void setUp() {
        sapBillArchiveEventConsumer = new DistributedSapBillArchiveEventConsumer(sapBillArchiveService);
    }

    @Test
    void givenPushToSap_whenConsumeEvent_thenCallsProcess() {
        // Arrange
        Long taskId = 1L;
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskType()).thenReturn("PUSH_TO_SAP");
        when(event.getTaskId()).thenReturn(taskId);

        // Act
        sapBillArchiveEventConsumer.consume(event);

        // Assert
        verify(sapBillArchiveService).archiveIfNecessary(taskId);
    }

    @Test
    void givenNullEvent_whenConsumeEvent_thenThrowsException() {
        // Arrange
        DistributedBillTaskEvent event = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            sapBillArchiveEventConsumer.consume(event);
        });
    }

    @Test
    void givenNullState_whenConsumeEvent_thenDoesNotCallProcess() {
        // Arrange
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskType()).thenReturn(null);

        // Act
        sapBillArchiveEventConsumer.consume(event);

        // Assert
        verify(sapBillArchiveService, never()).archiveIfNecessary(anyLong());
    }
}