package com.kering.cus.finance.billcenter.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dto.BillTaskQueryDTO;
import com.kering.cus.finance.billcenter.service.*;
import com.kering.cus.finance.billcenter.vo.BillTaskVO;
import com.kering.cus.finance.billcenter.vo.CommonBusinessVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillTaskControllerTest {
    private static final String API_SOURCE = "API";
    private static final Long TEST_TASK_ID = 123L;
    private static final String TEST_MERCHANT_ID = "test_merchant";
    private static final String TEST_SFTP_PATH = "/path/to/file.csv";
    private static final long TEST_BILL_DATE = Instant.now().toEpochMilli();

    @Mock
    private BillCenterConfig billCenterConfig;
    @Mock
    private BillAcquireService billAcquireService;
    @Mock
    private ApiBillTaskCreateProxyService apiTaskCreateProxyService;
    @Mock
    private SftpBillTaskCreateProxyService sftpTaskCreateProxyService;
    @Mock
    private BillProcessService billProcessService;
    @Mock
    private SapBillArchiveService sapBillArchiveService;
    @Mock
    private BlacklineBillArchiveService blacklineBillArchiveService;
    @Mock
    private BillTaskService billTaskService;

    @InjectMocks
    private BillTaskController billTaskController;

    @Test
    void givenValidChannel_whenCreateApiBillTasksByChannel_thenReturnsCreated() {
        when(apiTaskCreateProxyService.createTaskIfNecessary(eq(Channel.ALIPAY), any(), eq(API_SOURCE)))
                .thenReturn(true);

        ResponseEntity<Void> response = billTaskController.createApiBillTasksByChannel(Channel.ALIPAY);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenCreateTaskFailed_whenCreateApiBillTasksByChannel_thenReturnsNotModified() {
        when(apiTaskCreateProxyService.createTaskIfNecessary(eq(Channel.ALIPAY), any(), eq(API_SOURCE)))
                .thenReturn(false);

        ResponseEntity<Void> response = billTaskController.createApiBillTasksByChannel(Channel.ALIPAY);

        assertEquals(HttpStatus.NOT_MODIFIED, response.getStatusCode());
    }

    @Test
    void givenValidParams_whenCreateApiBillTasksByChannelAndMerchant_thenReturnsCreated() {
        when(apiTaskCreateProxyService.createTaskIfNecessary(
                eq(Channel.ALIPAY), eq(TEST_MERCHANT_ID), eq(BillType.TRADE_FLOW), any(), eq(API_SOURCE)))
                .thenReturn(true);

        ResponseEntity<Void> response = billTaskController.createApiBillTasksByChannelAndMerchant(
                Channel.ALIPAY, TEST_MERCHANT_ID, BillType.TRADE_FLOW, TEST_BILL_DATE);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenValidJdChannel_whenCreateSftpBillTasksByChannel_thenReturnsCreated() {
        when(sftpTaskCreateProxyService.createTaskIfNecessary(eq(TEST_SFTP_PATH), any(), eq(API_SOURCE)))
                .thenReturn(true);

        ResponseEntity<Void> response = billTaskController.createBillTasksByChannel(TEST_SFTP_PATH);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenAcquireSuccess_whenAcquireTaskById_thenReturnsCreated() {
        when(billAcquireService.acquireIfNecessary(TEST_TASK_ID)).thenReturn(true);

        ResponseEntity<Void> response = billTaskController.acquireTaskById(TEST_TASK_ID);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenProcessSuccess_whenProcessTaskById_thenReturnsCreated() {
        when(billProcessService.processIfNecessary(TEST_TASK_ID)).thenReturn(true);

        ResponseEntity<Void> response = billTaskController.processTaskById(TEST_TASK_ID);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenPushToSapSuccess_whenPushTaskBillDataToSap_thenReturnsCreated() {
        when(sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID)).thenReturn(true);

        ResponseEntity<Void> response = billTaskController.pushTaskBillDataToSap(TEST_TASK_ID);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenPushToBlacklineSuccess_whenPushTaskBillDataToBlackline_thenReturnsCreated() {
        when(blacklineBillArchiveService.archiveIfNecessary(Channel.JD)).thenReturn(true);

        ResponseEntity<Void> response = billTaskController.pushTaskBillDataToBlackline(Channel.JD);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }

    @Test
    void givenValidQueryDTO_whenQueryBillTasks_thenReturnsOk() {
        BillTaskQueryDTO queryDTO = new BillTaskQueryDTO();
        Page<BillTaskVO> mockPage = new Page<>();

        when(billTaskService.getBillTasksWithPagination(queryDTO)).thenReturn(mockPage);

        ResponseEntity<CommonBusinessVO<Page<BillTaskVO>>> response = billTaskController.queryBillTasks(queryDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void givenValidOssUrl_whenDownloadBillFile_thenReturnsOk() {
        String testOssUrl = "test_oss_url";
        InputStreamResource mockInputStreamResource = new InputStreamResource(new java.io.ByteArrayInputStream(new byte[0]));

        when(billTaskService.getBillFileStream(testOssUrl)).thenReturn(mockInputStreamResource);

        ResponseEntity<InputStreamResource> response = billTaskController.downloadBillFile(testOssUrl);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
