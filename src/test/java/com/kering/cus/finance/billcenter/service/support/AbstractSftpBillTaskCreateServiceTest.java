package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AbstractSftpBillTaskCreateServiceTest {

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantConfigDAO merchantConfigDAO;

    @InjectMocks
    private TestSftpBillTaskCreateService testService;

    @BeforeEach
    void setUp() {
        testService = new TestSftpBillTaskCreateService(Channel.JD, billTaskDAO, transitLogDAO, merchantConfigDAO);
    }

    @Test
    void givenValidParameters_whenCreateTask_thenSuccess() {
        // Arrange
        String brand = "brand1";
        String merchantId = "merchant1";
        BillType billType = BillType.FUND_FLOW;
        String sftpPath = "/path/to/file";
        ZonedDateTime when = ZonedDateTime.now();
        String source = "test";

        MerchantConfigEntity config = new MerchantConfigEntity();
        config.setBrand(brand);
        config.setIsTmall(true);
        config.setIsSyncToSap(true);
        config.setIsSyncToBlackline(true);

        when(billTaskDAO.create(any(BillTaskEntity.class))).thenAnswer(a -> {
            a.<BillTaskEntity>getArgument(0).setId(0L);
            return a.<BillTaskEntity>getArgument(0);
        });
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), anyString()))
                .thenReturn(config);
        // Act
        Long taskId = testService.doCreateTaskIfNecessary(
                brand, merchantId, billType, sftpPath, when, source
        );

        // Assert
        assertNotNull(taskId);
        verify(billTaskDAO).create(any(BillTaskEntity.class));
        verify(transitLogDAO).create(any(TransitLogEntity.class));
    }

    @Test
    void givenDuplicateKeyExists_whenCreateTask_thenReturnNull() {
        // Arrange
        String sftpPath = "/path/to/file";
        ZonedDateTime when = ZonedDateTime.now();
        String source = "test";

        when(billTaskDAO.findExistsTaskUniqueKeys(anyList()))
                .thenReturn(Collections.singletonList("duplicate_key"));
        // Act
        Long taskId = testService.doCreateTaskIfNecessary(
                "brand1", "merchant1", BillType.FUND_FLOW, sftpPath, when, source
        );

        // Assert
        assertNull(taskId);
    }

    // 内部测试类实现抽象方法
    static class TestSftpBillTaskCreateService extends AbstractSftpBillTaskCreateService {
        public TestSftpBillTaskCreateService(Channel channel, BillTaskDAO billTaskDAO, TransitLogDAO transitLogDAO, MerchantConfigDAO merchantConfigDAO) {
            super(channel, billTaskDAO, transitLogDAO, merchantConfigDAO);
        }


        @Override
        public boolean matches(String sftpPath) {
            return StringUtils.hasText(sftpPath);
        }

        @Override
        public Long createTaskIfNecessary(String sftpPath, ZonedDateTime when, String source) {
            return null;
        }
    }
}