package com.kering.cus.finance.billcenter.service.wechat;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class WechatBillTaskCreateServiceTest {

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantConfigDAO merchantConfigDAO;

    @Mock
    private MerchantGrantConfigDAO merchantGrantConfigDAO;

    @InjectMocks
    private WechatBillTaskCreateService wechatBillTaskCreateService;

    @BeforeEach
    void setUp() {
        wechatBillTaskCreateService = new WechatBillTaskCreateService(
                billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO
        );
    }

    @Test
    void givenWechatChannel_whenMatches_thenReturnTrue() {
        // Act & Assert
        assertTrue(wechatBillTaskCreateService.matches(Channel.WECHAT));
        assertFalse(wechatBillTaskCreateService.matches(Channel.ALIPAY));
    }

    @Test
    void givenSupportedTypes_whenGetSupportedTypes_thenContainsTradeAndFundFlow() {
        // Act
        List<BillType> supportedTypes = wechatBillTaskCreateService.getSupportedBillTypes();

        // Assert
        assertNotNull(supportedTypes);
        assertEquals(2, supportedTypes.size());
        assertTrue(supportedTypes.contains(BillType.TRADE_FLOW));
        assertTrue(supportedTypes.contains(BillType.FUND_FLOW));
    }
}