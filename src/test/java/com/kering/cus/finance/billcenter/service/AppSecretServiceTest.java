package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.lib.secret.access.SecretAccessService;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AppSecretServiceTest {

    @Mock
    private BillCenterConfig config;

    @Mock
    private SecretAccessService secretAccessService;

    private AppSecretService appSecretService;

    @BeforeEach
    void setUp() {
        appSecretService = new AppSecretService(config, secretAccessService);
    }

    @Test
    void givenValidChannel_whenGetIsvAppSecret_thenReturnExpectedConfig() {
        // Arrange
        String channel = "testChannel";
        String expectedPath = "kering/testchannel/isv-app-config";
        String secretRootPath = "https://secret-service.com";
        TestConfig expectedConfig = new TestConfig("value");

        when(config.getSecretAccessRootPath()).thenReturn(secretRootPath);
        when(secretAccessService.getSecretValue(secretRootPath + "/" + expectedPath, TestConfig.class))
                .thenReturn(expectedConfig);

        // Act
        TestConfig result = appSecretService.getIsvAppSecret(channel, TestConfig.class);

        // Assert
        assertEquals(expectedConfig, result);
    }

    @Test
    void givenValidMerchantInfo_whenGetMerchantAppSecret_thenReturnExpectedConfig() {
        // Arrange
        String brand = "brand1";
        String channel = "channel1";
        String merchantId = "merchant1";
        String expectedPath = "brand1/channel1/merchant1/mch-app-config";
        String secretRootPath = "https://secret-service.com";
        TestConfig expectedConfig = new TestConfig("value");

        when(config.getSecretAccessRootPath()).thenReturn(secretRootPath);
        when(secretAccessService.getSecretValue(secretRootPath + "/" + expectedPath, TestConfig.class))
                .thenReturn(expectedConfig);

        // Act
        TestConfig result = appSecretService.getMerchantAppSecret(brand, channel, merchantId, TestConfig.class);

        // Assert
        assertEquals(expectedConfig, result);
        assertEquals("value", result.getValue());
    }

    // Test configuration class
    @Getter
    @Setter
    static class TestConfig {
        private String value;

        public TestConfig(String value) {
            this.value = value;
        }
    }
}
