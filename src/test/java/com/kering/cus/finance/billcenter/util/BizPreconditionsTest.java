package com.kering.cus.finance.billcenter.util;

import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.exception.BillCenterErrorCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class BizPreconditionsTest {

    private static final BillCenterErrorCode TEST_ERROR_CODE = BillCenterErrorCode.CHANNEL_NOT_FOUND;

    @Test
    void checkTrue_ShouldNotThrow_WhenConditionIsTrue() {
        assertDoesNotThrow(() -> BizPreconditions.checkTrue(true, TEST_ERROR_CODE));
    }

    @Test
    void checkTrue_ShouldThrow_WhenConditionIsFalse() {
        assertThrows(BillCenterBusinessException.class, 
            () -> BizPreconditions.checkTrue(false, TEST_ERROR_CODE));
    }

    @Test
    void checkHasText_ShouldReturnText_WhenHasText() {
        assertEquals("test", BizPreconditions.checkHasText("test", TEST_ERROR_CODE));
    }

    @Test
    void checkHasText_ShouldThrow_WhenTextIsEmpty() {
        assertThrows(BillCenterBusinessException.class,
                () -> BizPreconditions.checkHasText("", TEST_ERROR_CODE));
    }

    @Test
    void checkNotNull_ShouldReturnObject_WhenNotNull() {
        Object obj = new Object();
        assertSame(obj, BizPreconditions.checkNotNull(obj, TEST_ERROR_CODE));
    }

    @Test
    void checkNotNull_ShouldThrow_WhenObjectIsNull() {
        assertThrows(BillCenterBusinessException.class,
                () -> BizPreconditions.checkNotNull(null, TEST_ERROR_CODE));
    }
}