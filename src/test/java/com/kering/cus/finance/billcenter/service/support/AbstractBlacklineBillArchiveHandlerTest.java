package com.kering.cus.finance.billcenter.service.support;

import com.jcraft.jsch.ChannelSftp;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AbstractBlacklineBillArchiveHandlerTest {

    @Mock
    private SftpConfig sftpConfig;

    @InjectMocks
    private TestBlacklineBillArchiveHandler testHandler;

    private Map<String, File> testFiles;

    @BeforeEach
    void setUp() {
        testFiles = new HashMap<>();
    }

    @Test
    void givenValidFiles_whenSendToBlackline_thenVerifySftpOperations() throws Exception {
        // Arrange
        File testFile = FileUtils2.createTempFile("testfile", ".csv").toFile();
        testFiles.put("test.csv", testFile);

        when(sftpConfig.getBlacklineSftpHostname()).thenReturn("test-host");
        when(sftpConfig.getBlacklineSftpPort()).thenReturn(22);
        when(sftpConfig.getBlacklineSftpUsername()).thenReturn("test-user");
        when(sftpConfig.getBlacklineSftpPassword()).thenReturn("test-pass");
        when(sftpConfig.getBlacklineSftpWechatArchiveDirectory()).thenReturn("/archive");
        try (MockedStatic<SftpUtils> mockedSftpUtils = mockStatic(SftpUtils.class)) {
            ChannelSftp mockChannel = mock(ChannelSftp.class);
            mockedSftpUtils.when(() -> SftpUtils.connect(
                    eq("test-host"),
                    eq(22),
                    eq("test-user"),
                    eq("test-pass"),
                    isNull(),
                    isNull(),
                    eq(10000)
            )).thenReturn(mockChannel);
            mockedSftpUtils.when(() -> SftpUtils.mkdirs(any(), any())).thenReturn(true);

            when(sftpConfig.getSftpConnectTimeout()).thenReturn(10000);

            // Act
            testHandler.sendToBlackline(testFiles);

            // Assert
            mockedSftpUtils.verify(() -> SftpUtils.connect(
                    "test-host", 22, "test-user", "test-pass", null, null, 10000
            ));
            mockedSftpUtils.verify(() -> SftpUtils.mkdirs(mockChannel, "/archive"));
            verify(mockChannel, times(1)).put(any(InputStream.class), eq("/archive/test.csv"));
            mockedSftpUtils.verify(() -> SftpUtils.disconnect(mockChannel));
        } finally {
            FileUtils.deleteQuietly(testFile);
        }
    }

    @Test
    void givenInvalidDirectory_whenSendToBlackline_thenThrowIOException() throws Exception {
        // Arrange
        File testFile = FileUtils2.createTempFile("testfile", ".csv").toFile();
        testFiles.put("test.csv", testFile);

        when(sftpConfig.getBlacklineSftpHostname()).thenReturn("test-host");
        when(sftpConfig.getBlacklineSftpUsername()).thenReturn("test-user");
        when(sftpConfig.getBlacklineSftpPassword()).thenReturn("test-pass");
        try (MockedStatic<SftpUtils> mockedSftpUtils = mockStatic(SftpUtils.class)) {
            ChannelSftp mockChannel = mock(ChannelSftp.class);
            mockedSftpUtils.when(() -> SftpUtils.connect(any(), anyInt(), anyString(), anyString(), isNull(), isNull(), anyInt()))
                    .thenReturn(mockChannel);
            when(sftpConfig.getSftpConnectTimeout()).thenReturn(10000);

            // 模拟目录创建失败
            mockedSftpUtils.when(() -> SftpUtils.mkdirs(eq(mockChannel), anyString())).thenReturn(false);

            // Act & Assert
            IOException exception = assertThrows(IOException.class, () -> {
                testHandler.sendToBlackline(testFiles);
            });

            // Verify
            assertTrue(exception.getMessage().contains("Failed to mkdirs"));
            mockedSftpUtils.verify(() -> SftpUtils.disconnect(mockChannel));
        } finally {
            FileUtils.deleteQuietly(testFile);
        }
    }

    @Test
    void givenNullFilesMap_whenSendToBlackline_thenThrowIllegalArgumentException() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            testHandler.sendToBlackline(null);
        });

        // Verify
        assertEquals("filenameToFile cannot be null", exception.getMessage());
    }

    @Test
    void givenEmptyFilesMap_whenSendToBlackline_thenThrowIllegalArgumentException() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            testHandler.sendToBlackline(testFiles);
        });

        // Verify
        assertEquals("filenameToFile cannot be null", exception.getMessage());
    }

    @Test
    void givenDifferentChannel_whenMatches_thenReturnCorrectResult() {
        // Arrange
        Channel testChannel = Channel.ALIPAY;
        TestBlacklineBillArchiveHandler handler = new TestBlacklineBillArchiveHandler(testChannel, sftpConfig);

        // Act & Assert
        assertTrue(handler.matches(testChannel));
        assertFalse(handler.matches(Channel.WECHAT));
    }

    // 内部测试类实现抽象方法
    static class TestBlacklineBillArchiveHandler extends AbstractBlacklineBillArchiveHandler {
        public TestBlacklineBillArchiveHandler(Channel channel, SftpConfig sftpConfig) {
            super(channel, sftpConfig);
        }

        @Override
        public boolean isGroupingByTmall() {
            return false;
        }

        @Override
        public Map<String,File> archive(Group group, long seqInGroup, List<Long> taskIds) throws IOException {
            return null;
        }

        @Override
        protected String getBlacklineSftpTargetDirectory() {
            return sftpConfig.getBlacklineSftpWechatArchiveDirectory();
        }
    }
}