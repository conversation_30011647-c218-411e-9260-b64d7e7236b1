package com.kering.cus.finance.billcenter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.DictionaryDAO;
import com.kering.cus.finance.billcenter.dto.BillTaskQueryDTO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.DictionaryEntity;
import com.kering.cus.finance.billcenter.vo.BillTaskVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.InputStreamResource;

import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BillTaskServiceTest {

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private DictionaryDAO dictionaryDAO;

    @Mock
    private AppStorageService appStorageService;

    @InjectMocks
    private BillTaskService billTaskService;

    @Test
    void givenValidQueryDTO_whenGetBillTasksWithPagination_thenReturnsPage() {
        // Arrange
        BillTaskQueryDTO queryDTO = new BillTaskQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setLimit(10);

        Page<BillTaskEntity> mockEntityPage = new Page<>();
        mockEntityPage.setRecords(Collections.emptyList());
        mockEntityPage.setCurrent(1);
        mockEntityPage.setSize(10);
        mockEntityPage.setTotal(0);

        when(billTaskDAO.queryTasks(any(Page.class), eq(queryDTO))).thenReturn(mockEntityPage);

        // Act
        Page<BillTaskVO> result = billTaskService.getBillTasksWithPagination(queryDTO);

        // Assert
        assertNotNull(result);
        assertEquals(mockEntityPage.getCurrent(), result.getCurrent());
        assertEquals(mockEntityPage.getSize(), result.getSize());
        assertEquals(mockEntityPage.getTotal(), result.getTotal());
        verify(billTaskDAO, times(1)).queryTasks(any(Page.class), eq(queryDTO));
    }

    @Test
    void givenEntityPage_whenConvertToVOPage_thenReturnsVOPage() {
        // Arrange
        BillTaskEntity entity = new BillTaskEntity();
        entity.setId(1L);
        List<BillTaskEntity> entityList = Collections.singletonList(entity);

        Page<BillTaskEntity> entityPage = new Page<>();
        entityPage.setRecords(entityList);
        entityPage.setCurrent(1);
        entityPage.setSize(1);
        entityPage.setTotal(1);

        // Act
        Page<BillTaskVO> voPage = billTaskService.convertToVOPage(entityPage);

        // Assert
        assertNotNull(voPage);
        assertEquals(entityPage.getCurrent(), voPage.getCurrent());
        assertEquals(entityPage.getSize(), voPage.getSize());
        assertEquals(entityPage.getTotal(), voPage.getTotal());
        assertEquals(1, voPage.getRecords().size());
    }

    @Test
    void givenEntity_whenConvertToVO_thenReturnsVO() {
        // Arrange
        BillTaskEntity entity = new BillTaskEntity();
        entity.setId(1L);
        entity.setBrand("TestBrand");
        entity.setChannel(com.kering.cus.finance.billcenter.constant.Channel.ALIPAY);
        entity.setMerchantId("TestMerchant");
        entity.setState(TaskState.WAIT_ACQUIRE);

        DictionaryEntity dictionaryEntity = new DictionaryEntity();
        dictionaryEntity.setDicValue("Initial State");
        when(dictionaryDAO.findNameByGroupAndKey(anyString(), anyString())).thenReturn(dictionaryEntity);

        // Act
        BillTaskVO vo = billTaskService.convertToVO(entity);

        // Assert
        assertNotNull(vo);
        assertEquals(entity.getId(), vo.getId());
        assertEquals(entity.getBrand(), vo.getBrand());
        assertEquals(entity.getChannel().name(), vo.getChannel());
        assertEquals(entity.getMerchantId(), vo.getMerchantId());
        assertEquals(entity.getState().name(), vo.getState());
        assertEquals("Initial State", vo.getStatusName());
        verify(dictionaryDAO, times(1)).findNameByGroupAndKey(anyString(), anyString());
    }

    @Test
    void givenValidOssUrl_whenGetBillFileStream_thenReturnsInputStreamResource() {
        // Arrange
        String ossUrl = "test-oss-url";
        ByteArrayInputStream inputStream = new ByteArrayInputStream(new byte[0]);
        when(appStorageService.readStream(ossUrl)).thenReturn(inputStream);

        // Act
        InputStreamResource result = billTaskService.getBillFileStream(ossUrl);

        // Assert
        assertNotNull(result);
        verify(appStorageService, times(1)).readStream(ossUrl);
    }

    @Test
    void givenException_whenGetBillFileStream_thenReturnsNull() {
        // Arrange
        String ossUrl = "test-oss-url";
        when(appStorageService.readStream(ossUrl)).thenThrow(new RuntimeException("Test Exception"));

        // Act
        InputStreamResource result = billTaskService.getBillFileStream(ossUrl);

        // Assert
        assertNull(result);
        verify(appStorageService, times(1)).readStream(ossUrl);
    }
}
