package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.TransitLogEntity;
import com.kering.cus.finance.billcenter.vo.TaskOperationLogVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskOperationLogServiceTest {

    @Mock
    private TransitLogDAO transitLogDAO;

    @InjectMocks
    private TaskOperationLogService taskOperationLogService;

    @Test
    void givenValidTraceId_whenGetTaskOperationLogs_thenReturnsList() {
        // Arrange
        String traceId = "123";
        TransitLogEntity entity = new TransitLogEntity();
        List<TransitLogEntity> entities = Arrays.asList(entity);

        when(transitLogDAO.getTaskOperationLogs(traceId)).thenReturn(entities);

        // Act
        List<TaskOperationLogVO> result = taskOperationLogService.getTaskOperationLogs(traceId);

        // Assert
        assertNotNull(result);
        assertEquals(entities.size(), result.size());
        verify(transitLogDAO, times(1)).getTaskOperationLogs(traceId);
    }
}
