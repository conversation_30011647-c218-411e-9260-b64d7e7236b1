package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.Constants;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.WechatFundFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WechatFundFlowEntity;
import com.kering.cus.finance.billcenter.util.CsvParserUtil;
import com.kering.cus.finance.billcenter.util.StringUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

@ExtendWith(MockitoExtension.class)
class WechatFundProcessHandlerTest {

  @InjectMocks
  private WechatFundProcessHandler handler;

  @Mock
  private WechatFundFlowDAO wechatFundFlowDAO;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void givenWechatChannelAndMerchantGrantTypeAndFundBillType_whenMatchesInvoked_thenReturnsTrue() {
    assertTrue(handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.FUND_FLOW));
    assertFalse(handler.matches(Channel.ALIPAY, GrantType.MERCHANT, BillType.FUND_FLOW));
    assertFalse(handler.matches(Channel.WECHAT, GrantType.ISV, BillType.FUND_FLOW));
    assertFalse(handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.TRADE_FLOW));
  }

  @Test
  void givenInvalidParameters_whenProcessInvoked_thenReturnsEmptyList() {
    File tempFile = null;
    BillTaskEntity billTaskEntity = null;
    List<String> result = handler.process(tempFile, billTaskEntity);
    assertTrue(CollectionUtils.isEmpty(result));
  }

  @Test
  void givenDuplicatedData_whenBuildEntityInvoked_thenThrowsBillCenterBusinessException() {
    List<String> data = Lists.newArrayList("2025-01-01 12:00:00", "bizOrderId1", "transactionNo1");
    List<String> headerMap = Lists.newArrayList("记账时间", "微信支付业务单号", "资金流水单号");
    BillTaskEntity task = new BillTaskEntity();
    task.setMerchantId("test");

    try (MockedStatic<CsvParserUtil> csvParserUtilMockedStatic = mockStatic(CsvParserUtil.class)) {
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "记账时间")).thenReturn("2025-01-01 12:00:00");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "微信支付业务单号")).thenReturn("bizOrderId1");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "资金流水单号")).thenReturn("transactionNo1");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(data, headerMap, "收支金额(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("100.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(data, headerMap, "账户结余(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("200.00"));

      assertNotNull(data);
    }
  }

  @Test
  void givenValidData_whenBuildEntityInvoked_thenReturnsEntity() {
    List<String> data = Lists.newArrayList("2025-01-01 12:00:00", "bizOrderId1", "transactionNo1");
    List<String> headerMap = Lists.newArrayList("记账时间", "微信支付业务单号", "资金流水单号");
    BillTaskEntity task = new BillTaskEntity();
    task.setMerchantId("test");

    try (MockedStatic<CsvParserUtil> csvParserUtilMockedStatic = mockStatic(CsvParserUtil.class)) {
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "记账时间")).thenReturn("2025-01-01 12:00:00");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "微信支付业务单号")).thenReturn("bizOrderId1");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeGetString(data, headerMap, "资金流水单号")).thenReturn("transactionNo1");
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(data, headerMap, "收支金额(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("100.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(data, headerMap, "账户结余(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("200.00"));

      WechatFundFlowEntity entity = handler.buildEntity(data, task, headerMap);
      assertNotNull(entity);
      assertEquals("2025-01-01 12:00:00", entity.getBillingDate());
      assertEquals("bizOrderId1", entity.getBizOrderId());
      assertEquals("transactionNo1", entity.getTransactionNo());
    }
  }

  @Test
  void givenIOException_whenDealCsvFileToDbInvoked_thenThrowsException() throws IOException {
    BillTaskEntity billTaskEntity = new BillTaskEntity();
    List<WechatFundFlowEntity> batchList = new ArrayList<>(Constants.BATCH_SIZE);
    BufferedReader reader = mock(BufferedReader.class);

    when(reader.readLine()).thenThrow(new IOException("Read error"));

    assertThrows(IOException.class, () -> handler.dealCsvFileToDb(billTaskEntity, batchList, reader));
  }

  @Test
  void givenValidCsvFile_whenDealCsvFileToDbInvoked_thenInsertDataSuccessfully() throws IOException {
    BillTaskEntity billTaskEntity = new BillTaskEntity();
    List<WechatFundFlowEntity> batchList = new ArrayList<>(Constants.BATCH_SIZE);
    BufferedReader reader = mock(BufferedReader.class);

    List<String> headerMap = Lists.newArrayList("记账时间", "微信支付业务单号", "资金流水单号");
    List<String> rowData1 = Lists.newArrayList("2025-01-01 10:00:00", "bizOrderId1", "transactionNo1");
    List<String> rowData2 = Lists.newArrayList("2025-01-02 11:00:00", "bizOrderId2", "transactionNo2");
    List<String> summaryRow = Lists.newArrayList("资金流水总笔数", "100", "200");

    try (MockedStatic<CsvParserUtil> csvParserUtilMockedStatic = mockStatic(CsvParserUtil.class);
        MockedStatic<StringUtils> stringUtilsMockedStatic = mockStatic(StringUtils.class)) {
      when(reader.readLine())
          .thenReturn("记账时间,微信支付业务单号,资金流水单号,收支金额(元),账户结余(元)")
          .thenReturn("2025-01-01 10:00:00,bizOrderId1,transactionNo1,100.00,200.00")
          .thenReturn("2025-01-02 11:00:00,bizOrderId2,transactionNo2,200.00,300.00")
          .thenReturn("资金流水总笔数,100,200")
          .thenReturn(null);

      csvParserUtilMockedStatic.when(() -> CsvParserUtil.parseCsvLine("记账时间,微信支付业务单号,资金流水单号,收支金额(元),账户结余(元)"))
          .thenReturn(headerMap);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.parseCsvLine("2025-01-01 10:00:00,bizOrderId1,transactionNo1,100.00,200.00"))
          .thenReturn(rowData1);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.parseCsvLine("2025-01-02 11:00:00,bizOrderId2,transactionNo2,200.00,300.00"))
          .thenReturn(rowData2);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.parseCsvLine("资金流水总笔数,100,200")).thenReturn(summaryRow);

      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(rowData1, headerMap, "记账时间", "资金流水总笔数")).thenReturn(false);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(rowData2, headerMap, "记账时间", "资金流水总笔数")).thenReturn(false);
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.isSummaryRow(summaryRow, headerMap, "记账时间", "资金流水总笔数")).thenReturn(true);

      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "收支金额(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("100.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData1, headerMap, "账户结余(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("200.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "收支金额(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("200.00"));
      csvParserUtilMockedStatic.when(() -> CsvParserUtil.safeParseBigDecimal(rowData2, headerMap, "账户结余(元)", new BigDecimal("0.00")))
          .thenReturn(new BigDecimal("300.00"));

      handler.dealCsvFileToDb(billTaskEntity, batchList, reader);
      assertNotNull(reader);
    }
  }
}
