package com.kering.cus.finance.billcenter.service.support;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AbstractApiBillTaskCreateServiceTest {

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantConfigDAO merchantConfigDAO;

    @Mock
    private MerchantGrantConfigDAO merchantGrantConfigDAO;

    @InjectMocks
    private TestApiBillTaskCreateService testService;

    @BeforeEach
    void setUp() {
        testService = new TestApiBillTaskCreateService(Channel.ALIPAY, billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO);
    }

    @Test
    void givenValidInput_whenDoCreateTasksIfNecessary_thenReturnsNonEmptyList() {
        // Act
        List<BillTaskEntity> result = testService.doCreateTasksIfNecessary(
                Channel.ALIPAY, "M123", GrantType.ISV, "KMS_BRAND_ID", Collections.singletonList(BillType.TRADE_FLOW), ZonedDateTime.now(), "test"
        );

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void givenMerchantConfigIsWosaipayFundFlow_whenDoCreateTasksIfNecessary_thenReturnsNotEmptyList() {
        // Arrange
        final MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsWosaipayMerchant(true);
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(config);

        // Act
        List<BillTaskEntity> result = testService.doCreateTasksIfNecessary(
                Channel.ALIPAY, "M123", GrantType.ISV, "KMS_BRAND_ID", Collections.singletonList(BillType.TRADE_FLOW), ZonedDateTime.now(), "test"
        );

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void givenValidMerchantData_whenCreateTaskIfNecessary_thenReturnsList() {
        // Arrange
        final MerchantConfigEntity config = new MerchantConfigEntity();
        config.setIsWosaipayMerchant(false);
        final MerchantGrantConfigEntity grant = new MerchantGrantConfigEntity();
        grant.setMerchantId("M123");
        when(merchantGrantConfigDAO.findEnabledMerchantGrantConfig(any(), any(), eq("M123"))).thenReturn(grant);
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(config);

        // Act
        List<Long> ids = testService.createTaskIfNecessary("M123", Collections.singletonList(BillType.TRADE_FLOW), ZonedDateTime.now(), "test");

        // Assert
        assertNotNull(ids);
        assertTrue(!ids.isEmpty());
    }

    // 内部测试类实现抽象方法
    static class TestApiBillTaskCreateService extends AbstractApiBillTaskCreateService {
        public TestApiBillTaskCreateService(Channel channel, BillTaskDAO billTaskDAO, TransitLogDAO transitLogDAO, MerchantConfigDAO merchantConfigDAO, MerchantGrantConfigDAO merchantGrantConfigDAO) {
            super(channel, billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO);
        }

        @Override
        protected List<BillType> getSupportedBillTypes() {
            return Collections.singletonList(BillType.TRADE_FLOW);
        }
    }
}