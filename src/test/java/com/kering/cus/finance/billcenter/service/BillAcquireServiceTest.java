package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.BillAcquireHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.FileInputStream;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@Slf4j
class BillAcquireServiceTest {
    @Mock
    private BillCenterConfig billCenterConfig;

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantGrantConfigDAO merchantGrantConfigDAO;

    @Mock
    private AppStorageService appStorageService;
    @Mock
    private BillTaskEventPublisher publisher;

    @Mock
    private List<BillAcquireHandler> billAcquireHandlers;

    @InjectMocks
    private BillAcquireService billAcquireService;

    private static final String TEST_TRACE_ID = "test-trace-id";
    private static final String TEST_MERCHANT_ID = "test-merchant";
    private static final String TEST_BRAND = "test-brand";
    private static final Long TEST_TASK_ID = 1L;

    // Helper method
    private BillTaskEntity createTestBillEntityWithState(TaskState state) {
        BillTaskEntity task = new BillTaskEntity();
        task.setId(TEST_TASK_ID);
        task.setTraceId(TEST_TRACE_ID);
        task.setChannel(Channel.ALIPAY);
        task.setGrantType(GrantType.ISV);
        task.setBillType(BillType.TRADE_FLOW);
        task.setMerchantId(TEST_MERCHANT_ID);
        task.setBrand(TEST_BRAND);
        task.setState(state);
        task.setVersion(1L);
        task.setNextRunTime(ZonedDateTime.now().minusMinutes(1));
        EntityUtils.fill(task, ZonedDateTime.now());
        return task;
    }

    @Test
    void shouldReturnFalse_whenTaskNotFound() {
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.empty());

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
    }

    @Test
    void shouldReturnFalse_whenInvalidTaskState() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_PROCESS);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
    }

    @Test
    void shouldReturnFalse_whenNextRunTimeInFuture() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        task.setNextRunTime(ZonedDateTime.now().plusHours(1));
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
    }

    @Test
    void shouldReturnFalse_whenUpdateNextRunTimeFails() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong())).thenReturn(0);

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong());
    }

    @Test
    void shouldProcessSuccessfully_whenValidTask() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong())).thenReturn(1);

        MerchantGrantConfigEntity merchant = new MerchantGrantConfigEntity();
        merchant.setApiVersion("v1");
        when(merchantGrantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

        BillAcquireHandler mockHandler = mock(BillAcquireHandler.class);
        when(mockHandler.matches(any(), any(), any(), any())).thenReturn(true);
        when(billAcquireHandlers.stream()).thenReturn(Stream.of(mockHandler));

        when(mockHandler.acquireTo(any(), any())).thenReturn("csv");

        when(billTaskDAO.updateToAcquireSuccessByIdAndVersion(anyString(), any(), eq(TEST_TASK_ID), anyLong())).thenReturn(1);

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertTrue(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong());
        verify(merchantGrantConfigDAO).findByChannelAndMerchantId(any(), any());
        verify(mockHandler).acquireTo(any(), any());
        verify(appStorageService).writeStream(anyString(), any(FileInputStream.class));
        verify(billTaskDAO).updateToAcquireSuccessByIdAndVersion(anyString(), any(), eq(TEST_TASK_ID), anyLong());
        verify(publisher).publishWaitProcessEvent(anyLong());
    }

    @Test
    void shouldProcessFailed_whenAcquireNoSuffix() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong())).thenReturn(1);

        MerchantGrantConfigEntity merchant = new MerchantGrantConfigEntity();
        merchant.setApiVersion("v1");
        when(merchantGrantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

        BillAcquireHandler mockHandler = mock(BillAcquireHandler.class);
        when(mockHandler.matches(any(), any(), any(), any())).thenReturn(true);
        when(billAcquireHandlers.stream()).thenReturn(Stream.of(mockHandler));

        when(mockHandler.acquireTo(any(), any())).thenReturn(null);

        assertThrows(BillCenterBusinessException.class, () -> billAcquireService.acquireIfNecessary(TEST_TASK_ID));

        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong());
        verify(merchantGrantConfigDAO).findByChannelAndMerchantId(any(), any());
        verify(mockHandler).acquireTo(any(), any());
        verify(billTaskDAO).updateToFailedByIdAndVersion(
                eq(TaskState.ACQUIRE_FAILED), anyInt(), anyString(), any(), eq(TEST_TASK_ID), anyLong()
        );
        verify(transitLogDAO, times(2)).create(any());
    }

    @Test
    void shouldProcessFailed_whenNoHandlerFound() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong())).thenReturn(1);

        MerchantGrantConfigEntity merchant = new MerchantGrantConfigEntity();
        merchant.setApiVersion("v1");
        when(merchantGrantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

        when(billAcquireHandlers.stream()).thenReturn(Stream.empty());

        boolean result = billAcquireService.acquireIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong());
        verify(merchantGrantConfigDAO).findByChannelAndMerchantId(any(), any());
        verify(billTaskDAO).updateToFailedByIdAndVersion(
                eq(TaskState.ACQUIRE_FAILED), anyInt(), anyString(), any(), eq(TEST_TASK_ID), anyLong()
        );
        verify(billCenterConfig).getMaxRetryTimes();
    }

    @Test
    void shouldProcessFailed_whenHappenError() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong())).thenReturn(1);

        MerchantGrantConfigEntity merchant = new MerchantGrantConfigEntity();
        merchant.setApiVersion("v1");
        when(merchantGrantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

        BillAcquireHandler mockHandler = mock(BillAcquireHandler.class);
        when(mockHandler.matches(any(), any(), any(), any())).thenReturn(true);
        when(billAcquireHandlers.stream()).thenReturn(Stream.of(mockHandler));

        when(mockHandler.acquireTo(any(), any())).thenThrow(new RuntimeException());

        assertThrows(BillCenterBusinessException.class, () -> billAcquireService.acquireIfNecessary(TEST_TASK_ID));

        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(TEST_TASK_ID), anyLong());
        verify(merchantGrantConfigDAO).findByChannelAndMerchantId(any(), any());
        verify(mockHandler).acquireTo(any(), any());
        verify(billTaskDAO).updateToFailedByIdAndVersion(
                eq(TaskState.ACQUIRE_FAILED), anyInt(), any(), any(), eq(TEST_TASK_ID), anyLong()
        );
    }

    @Test
    void shouldGenerateCorrectOssPath_withDefaultValues() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        task.setBrand(null);
        task.setMerchantId(null);
        task.setBillType(BillType.TRADE_FLOW);

        String result = billAcquireService.generateOssPath(task, "csv");

        assertTrue(result.contains("unknown/original"));
        assertTrue(result.contains("unknown/" + BillType.TRADE_FLOW));
    }

    @Test
    void shouldGenerateCorrectOssPath_withAllValues() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_ACQUIRE);
        String result = billAcquireService.generateOssPath(task, "csv");

        assertTrue(result.contains(task.getBrand()));
        assertTrue(result.contains("/original/"));
        assertTrue(result.contains("/" + task.getMerchantId()));
        assertTrue(result.contains("/" + task.getBillType()));
    }

}