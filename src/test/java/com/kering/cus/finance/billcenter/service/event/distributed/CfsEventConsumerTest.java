package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.service.SftpBillTaskCreateProxyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CfsEventConsumerTest {

    @Mock
    private SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService;

    @InjectMocks
    private CfsEventConsumer cfsEventConsumer;

    @BeforeEach
    void setUp() {
        cfsEventConsumer = new CfsEventConsumer(sftpBillTaskCreateProxyService);
    }


    @Test
    void givenValidEvent_whenConsume_thenDelegatesToAbstractConsumer() {
        // Arrange
        CfsEvent event = new CfsEvent();
        event.setVirtualPath("/jd/path");
        event.setAction("upload");

        // Act
        cfsEventConsumer.consume(event);

        // Assert
        verify(sftpBillTaskCreateProxyService).createTaskIfNecessary(
                eq("/jd/path"),
                any(ZonedDateTime.class),
                eq("CFS_MQ")
        );
    }

    @Test
    void givenEventWithVirtualPath_whenConsume_thenCallsProxyServiceWithCorrectParameters() {
        // Arrange
        String virtualPath = "/path/to/file";
        CfsEvent event = new CfsEvent();
        event.setVirtualPath(virtualPath);
        event.setAction("upload");

        // Act
        cfsEventConsumer.consume(event);

        // Assert
        verify(sftpBillTaskCreateProxyService).createTaskIfNecessary(
                eq(virtualPath),
                any(ZonedDateTime.class),
                eq("CFS_MQ")
        );
    }

    @Test
    void givenNullEvent_whenConsume_thenThrowsException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            cfsEventConsumer.consume(null);
        });
    }

    @Test
    void givenEmptyPath_whenConsume_thenDoesNotCallProxyService() {
        // Arrange
        CfsEvent event = new CfsEvent();
        event.setVirtualPath(null);

        // Act
        cfsEventConsumer.consume(event);

        // Assert
        verify(sftpBillTaskCreateProxyService, never()).createTaskIfNecessary(
                anyString(),
                any(ZonedDateTime.class),
                anyString()
        );
    }
}