package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.client.oauth2.AuthorizationInfo;
import com.kering.cus.finance.billcenter.client.oauth2.Oauth2Client;
import com.kering.cus.finance.billcenter.config.AlipayIsvAppProperties;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.OauthAccessTokenDAO;
import com.kering.cus.finance.billcenter.entity.MerchantGrantConfigEntity;
import com.kering.cus.finance.billcenter.entity.OauthAccessTokenEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.kering.cus.finance.billcenter.constant.Channel.ALIPAY;
import static com.kering.cus.finance.billcenter.constant.Channel.WECHAT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OauthGrantServiceTest {

    @Mock
    private BillCenterConfig config;
    @Mock
    private AppSecretService appSecretService;
    @Mock
    private OauthAccessTokenDAO oauthAccessTokenDAO;
    @Mock
    private Oauth2Client mockOauthClient;
    @Mock
    private MerchantGrantConfigDAO merchantGrantConfigDAO;

    @InjectMocks
    private OauthGrantService oauthGrantService;

    private AlipayIsvAppProperties alipayProps;
    private OauthAccessTokenEntity existingToken;
    private AuthorizationInfo authorizationInfo;

    @BeforeEach
    void setUp() {
        alipayProps = new AlipayIsvAppProperties();
        alipayProps.setAppId("test_app_id");
        alipayProps.setAppPrivateKey("test_private_key");
        alipayProps.setAlipayPublicKey("test_public_key");

        existingToken = new OauthAccessTokenEntity();
        existingToken.setId(1L);
        existingToken.setPlatform(ALIPAY);
        existingToken.setMerchantId("test_merchant");
        existingToken.setRefreshToken("test_refresh_token");
        existingToken.setNextRefreshTime(ZonedDateTime.now().minusHours(1));

        oauthGrantService.mockClient = mockOauthClient;

        authorizationInfo = new AuthorizationInfo();
        authorizationInfo.setAccessToken("access_token");
        authorizationInfo.setRefreshToken("refresh_token");
        authorizationInfo.setUserId("UID");
        authorizationInfo.setMerchantId("UID0156");
        authorizationInfo.setExpiresIn(3600L);
        authorizationInfo.setReExpiresIn(3600L);
    }

    @Test
    void givenValidChannel_whenGetAuthorizeUrl_thenReturnAlipayUrl() {
        when(appSecretService.getIsvAppSecret(ALIPAY.name(), AlipayIsvAppProperties.class))
                .thenReturn(alipayProps);

        when(mockOauthClient.getAuthorizeUrl(anyString(), any())).thenReturn("mock_url");

        String result = oauthGrantService.getAuthorizeUrl(ALIPAY, "http://callback");

        assertNotNull(result);
    }

    @Test
    void givenUnsupportedChannel_whenGetAuthorizeUrl_thenReturnsNull() {
        String result = oauthGrantService.getAuthorizeUrl(WECHAT, "http://callback");

        assertNull(result);
    }

    @Test
    void shouldCreateNewToken_whenDoAuthorizeCallbackAndTokenNotExist() {
        when(appSecretService.getIsvAppSecret(ALIPAY.name(), AlipayIsvAppProperties.class))
                .thenReturn(alipayProps);

        when(oauthAccessTokenDAO.findByPlatformAndMerchantId(eq(ALIPAY), anyString()))
                .thenReturn(Optional.empty());
        when(mockOauthClient.getAuthorizationInfo(anyMap()))
                .thenReturn(authorizationInfo);

        AuthorizationInfo result = oauthGrantService.doAuthorizeCallback(
                ALIPAY,
                Map.of("app_auth_code", "test_code")
        );

        assertNotNull(result);
        verify(oauthAccessTokenDAO).create(any(OauthAccessTokenEntity.class));
        verify(merchantGrantConfigDAO).create(any(MerchantGrantConfigEntity.class));
    }

    @Test
    void shouldUpdateToken_whenDoAuthorizeCallbackAndTokenExists() {
        when(appSecretService.getIsvAppSecret(ALIPAY.name(), AlipayIsvAppProperties.class))
                .thenReturn(alipayProps);

        when(oauthAccessTokenDAO.findByPlatformAndMerchantId(eq(ALIPAY), anyString()))
                .thenReturn(Optional.of(existingToken));
        when(mockOauthClient.getAuthorizationInfo(anyMap()))
                .thenReturn(authorizationInfo);

        AuthorizationInfo result = oauthGrantService.doAuthorizeCallback(
                ALIPAY,
                Map.of("app_auth_code", "test_code")
        );

        assertNotNull(result);
        verify(oauthAccessTokenDAO).update(any(OauthAccessTokenEntity.class));
    }

    @Test
    void shouldReturnFalse_whenDoAuthorizeCallbackWithInvalidChannel() {
        AuthorizationInfo result = oauthGrantService.doAuthorizeCallback(WECHAT, Map.of("app_auth_code", "test_code"));

        assertNull(result);
    }

    @Test
    void shouldRefreshToken_whenRefreshAccessTokenIfNecessary() {
        when(appSecretService.getIsvAppSecret(ALIPAY.name(), AlipayIsvAppProperties.class))
                .thenReturn(alipayProps);

        when(oauthAccessTokenDAO.findById(1L)).thenReturn(Optional.of(existingToken));

        oauthGrantService.refreshAccessTokenIfNecessary(1L);

        verify(oauthAccessTokenDAO).update(any(OauthAccessTokenEntity.class));
    }

    @Test
    void shouldNotRefresh_whenTokenNotNeedRefresh() {

        existingToken.setNextRefreshTime(ZonedDateTime.now().plusHours(1));
        when(oauthAccessTokenDAO.findById(1L)).thenReturn(Optional.of(existingToken));

        oauthGrantService.refreshAccessTokenIfNecessary(1L);

        verify(oauthAccessTokenDAO, never()).update(any());
    }

    @Test
    void shouldThrowException_whenRefreshAccessTokenFails() {
        when(appSecretService.getIsvAppSecret(ALIPAY.name(), AlipayIsvAppProperties.class))
                .thenReturn(alipayProps);

        when(oauthAccessTokenDAO.findById(1L)).thenReturn(Optional.of(existingToken));
        when(mockOauthClient.refreshAccessToken(anyString(), anyString()))
                .thenThrow(new RuntimeException("Refresh failed"));

        oauthGrantService.refreshAccessTokenIfNecessary(1L);
        verify(oauthAccessTokenDAO).update(any(OauthAccessTokenEntity.class));
    }

    @Test
    void shouldReturnTokenIds_whenGetRefreshAccessTokenIds() {
        when(config.getMaxRetryTimes()).thenReturn(3);
        when(oauthAccessTokenDAO.findRefreshAccessTokenIds(any(ZonedDateTime.class), anyInt(), anyInt()))
                .thenReturn(List.of(1L, 2L));

        List<Long> result = oauthGrantService.getRefreshAccessTokenIds(10);

        assertEquals(2, result.size());
        verify(oauthAccessTokenDAO).findRefreshAccessTokenIds(any(ZonedDateTime.class), eq(3), eq(10));
    }

    @Test
    void shouldReturnTokenIds_whenGetRefreshAccessToken() {
        when(config.getMaxRetryTimes()).thenReturn(3);
        when(oauthAccessTokenDAO.findRefreshAccessTokenIds(any(ZonedDateTime.class), anyInt(), anyInt()))
                .thenReturn(List.of(1L, 2L));
        oauthGrantService.refreshAccessTokenIfNecessary();
        verify(oauthAccessTokenDAO).findRefreshAccessTokenIds(any(ZonedDateTime.class), anyInt(), anyInt());
    }
}
