package com.kering.cus.finance.billcenter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class BillAlertServiceTest {

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private BillAlertService billAlertService;





    @Test
     void givenTaskWithRecords_whenBillAlert_thenSendsEmail() {
        // Given
        String type = "账单获取失败";
        TaskState taskState = TaskState.ACQUIRE_FAILED;
        SyncState syncState = null;

        BillTaskEntity task = new BillTaskEntity();
        task.setBrand("TestBrand");
        task.setChannel(Channel.WECHAT);
        task.setMerchantId("123456");
        task.setErrorCount(3);
        task.setErrorMsg("Connection timeout");

        Page<BillTaskEntity> page = new Page<>();
        page.setRecords(List.of(task));
        page.setTotal(1L);

        when(billTaskDAO.findTasksByStatus(any(), any(), any(), anyInt(), anyInt())).thenReturn(page);

        // When
        billAlertService.billAlert(type, taskState, syncState);

        // Then
        verify(emailService).send(anyString(), anyString());
    }

    @Test
     void givenTaskWithNoRecords_whenBillAlert_thenDoesNotSendEmail() {
        // Given
        String type = "账单获取失败";
        TaskState taskState = TaskState.ACQUIRE_FAILED;
        SyncState syncState = null;

        Page<BillTaskEntity> page = new Page<>();
        page.setRecords(new ArrayList<>());
        page.setTotal(0L);

        when(billTaskDAO.findTasksByStatus(any(), any(), any(), anyInt(), anyInt())).thenReturn(page);

        // When
        billAlertService.billAlert(type, taskState, syncState);

        // Then
        verify(emailService, never()).send(anyString(), anyString());
    }

    @Test
     void givenExceptionInDao_whenBillAlert_thenHandlesExceptionGracefully() {
        // Given
        String type = "账单获取失败";
        TaskState taskState = TaskState.ACQUIRE_FAILED;
        SyncState syncState = null;

        when(billTaskDAO.findTasksByStatus(any(), any(), any(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> billAlertService.billAlert(type, taskState, syncState));
    }


    @Test
     void givenBlacklineTasksWithRecords_whenBillBlacklinePushFailAlert_thenSendsEmail() {
        // Given
        List<BillTaskEntity> tasks = new ArrayList<>();
        BillTaskEntity task = new BillTaskEntity();
        task.setChannel(Channel.ALIPAY);
        task.setIsTmall(true);
        task.setErrorCount(2);
        task.setErrorMsg("Push failed");
        tasks.add(task);

        when(billTaskDAO.findTasksByBlacklineStatus(any(), any())).thenReturn(tasks);

        // When
        billAlertService.billBlacklinePushFailAlert();

        // Then
        verify(emailService).send(anyString(), anyString());
    }

    @Test
     void givenBlacklineTasksWithNoRecords_whenBillBlacklinePushFailAlert_thenDoesNotSendEmail() {
        // Given
        when(billTaskDAO.findTasksByBlacklineStatus(any(), any())).thenReturn(new ArrayList<>());

        // When
        billAlertService.billBlacklinePushFailAlert();

        // Then
        verify(emailService, never()).send(anyString(), anyString());
    }

    @Test
     void givenExceptionInBlacklineDao_whenBillBlacklinePushFailAlert_thenHandlesExceptionGracefully() {
        // Given
        when(billTaskDAO.findTasksByBlacklineStatus(any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> billAlertService.billBlacklinePushFailAlert());
    }


    @Test
     void givenDateAndType_whenGenerateBillAlertHeader_thenGeneratesCorrectHeader() {
        // Given
        LocalDate date = LocalDate.of(2023, 1, 1);
        String type = "账单获取失败";

        // When
        StringBuilder header = billAlertService.generateBillAlertHeader(date, type);

        // Then
        String content = header.toString();
        assertTrue(content.contains("<th>品牌</th>"));
        assertTrue(content.contains("<th>渠道</th>"));
        assertTrue(content.contains("<th>商户号</th>"));
        assertTrue(content.contains("<th>账单类型</th>"));
        assertTrue(content.contains("<th>重试次数</th>"));
        assertTrue(content.contains("<th>失败原因</th>"));
    }

    @Test
     void givenPushFailedType_whenGenerateBillAlertHeader_thenExcludesBillTypeHeader() {
        // Given
        LocalDate date = LocalDate.of(2023, 1, 1);
        String type = "账单推送SAP失败";

        // When
        StringBuilder header = billAlertService.generateBillAlertHeader(date, type);

        // Then
        String content = header.toString();
        assertTrue(content.contains("<th>品牌</th>"));
        assertTrue(content.contains("<th>渠道</th>"));
        assertTrue(content.contains("<th>商户号</th>"));
        assertFalse(content.contains("<th>账单类型</th>"));
        assertTrue(content.contains("<th>重试次数</th>"));
        assertTrue(content.contains("<th>失败原因</th>"));
    }

    @Test
     void givenDate_whenGeneratePushBlacklineAlertHeader_thenGeneratesCorrectHeader() {
        // Given
        LocalDate date = LocalDate.of(2023, 1, 1);

        // When
        StringBuilder header = billAlertService.generatePushBlacklineAlertHeader(date);

        // Then
        String content = header.toString();
        assertTrue(content.contains("<th>渠道</th>"));
        assertTrue(content.contains("<th>类型</th>"));
        assertTrue(content.contains("<th>重试次数</th>"));
        assertTrue(content.contains("<th>失败原因</th>"));
    }

}