package com.kering.cus.finance.billcenter.service.wosaipay;

import com.jcraft.jsch.ChannelSftp;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.config.WosaipayBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class WosaipayTradeFlowAcquireHandlerTest {

    @Mock
    private SftpConfig sftpConfig;
    @Mock
    private WosaipayBillConfig wosaipayBillConfig;
    @Mock
    private ChannelSftp mockChannelSftp;

    private WosaipayTradeFlowAcquireHandler handler;

    @BeforeEach
    void setUp() {
        handler = new WosaipayTradeFlowAcquireHandler(sftpConfig, wosaipayBillConfig);
    }

    @Test
    void givenWosaipayChannelAndTradeFlowType_whenMatches_thenReturnTrue() {
        assertTrue(handler.matches(Channel.WOSAIPAY, GrantType.SFTP, BillType.TRADE_FLOW, "1.0"));
    }

    @Test
    void givenAlipayChannel_whenMatches_thenReturnFalse() {
        assertFalse(handler.matches(Channel.JD, GrantType.SFTP, BillType.TRADE_FLOW, "1.0"));
    }

    @Test
    void givenFundFlowBillType_whenMatches_thenReturnFalse() {
        assertFalse(handler.matches(Channel.WOSAIPAY, GrantType.SFTP, BillType.FUND_FLOW, "1.0"));
    }

    @Test
    void givenIsvGrantType_whenMatches_thenReturnFalse() {
        assertFalse(handler.matches(Channel.WOSAIPAY, GrantType.ISV, BillType.TRADE_FLOW, "1.0"));
    }

    @Test
    void givenValidTaskWithUploadCsv_whenAcquireTo_thenReturnCsvSuffix() throws IOException {
        try (final MockedStatic<SftpUtils> ms = mockStatic(SftpUtils.class)) {
            ms.when(() -> SftpUtils.connect(any(), anyInt(), any(), any(), any(), any(), anyInt())).thenReturn(mockChannelSftp);
            ms.when(() -> SftpUtils.mkdirs(any(), any())).thenReturn(true);
            
            final BillTaskEntity task = BillTaskEntity.builder()
                    .extraParams("/upload.csv")
                    .build();
            
            final File tempFile = File.createTempFile(BillType.FUND_FLOW.name(), ".tmp");
            tempFile.deleteOnExit();
            
            try {
                final String extension = handler.acquireTo(task, tempFile);
                assertFalse(handler.matches(Channel.JD, GrantType.ISV, BillType.FUND_FLOW, "1.0"));
                assertEquals("upload.csv", extension);
            } finally {
                tempFile.delete();
            }
        }
    }
}