package com.kering.cus.finance.billcenter.service.alipay;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler.Group;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.CollectionUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AlipayBlacklineBillArchiveHandlerTest {


  @InjectMocks
  private AlipayBlacklineBillArchiveHandler handler;

  @Mock
  private AlipayBillFlowDAO alipayBillFlowDAO;


  private final List<Long> taskIds = List.of(1L, 2L, 3L);

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

  }

  @Test
  void givenAlipayChannel_whenMatches_thenReturnsTrue() {
    boolean result = handler.matches(Channel.ALIPAY);
    assertTrue(result);
  }

  @Test
  void givenNonAlipayChannel_whenMatches_thenReturnsFalse() {
    boolean result = handler.matches(Channel.WECHAT);
    assertFalse(result);
  }

  @Test
  void givenAnyInput_whenIsGroupingByTmall_thenReturnsTrue() {
    boolean result = handler.isGroupingByTmall();
    assertTrue(result);
  }

  @Test
  void givenEmptyBillList_whenArchive_thenReturnsNullAndLogsWarning() throws IOException {
    Page<AlipayBillFlowEntity> page = new Page<>();
    when(alipayBillFlowDAO.findAlipayBillByParams(anyList(), anyBoolean(), anyInt(), anyInt())).thenReturn(page);

    Map<String, File> result = handler.archive(Group.TMALL, 1L, taskIds);

    assertTrue(CollectionUtils.isEmpty(result));

  }

  @Test
  void givenValidBillList_whenArchive_thenCreatesCsvFileWithHeadersAndData() throws IOException {
    Page<AlipayBillFlowEntity> page = new Page<>();
    page.setRecords(createMockAlipayBillFlowEntities(3));
    when(alipayBillFlowDAO.findAlipayBillByParams(anyList(), anyBoolean(), anyInt(), anyInt())).thenReturn(page);

    Map<String, File> result = handler.archive(AlipayBlacklineBillArchiveHandler.Group.TMALL, 1L, taskIds);

    assertNotNull(result);
  }


  @Test
  void givenNonTmallGroup_whenArchive_thenPassesFalseToDao() throws IOException {
    Page<AlipayBillFlowEntity> page = new Page<>();
    page.setRecords(createMockAlipayBillFlowEntities(1));
    when(alipayBillFlowDAO.findAlipayBillByParams(anyList(), anyBoolean(), anyInt(), anyInt())).thenReturn(page);

    Map<String, File> archive = handler.archive(AlipayBlacklineBillArchiveHandler.Group.NON_TMALL, 1L, taskIds);
    assertNotNull(archive);
  }

  private List<AlipayBillFlowEntity> createMockAlipayBillFlowEntities(int count) {
    List<AlipayBillFlowEntity> entities = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      AlipayBillFlowEntity entity = new AlipayBillFlowEntity();
      entity.setBrand("品牌" + i);
      entity.setChannel("渠道" + i);
      entity.setMerchantId("商户号" + i);
      entity.setAccountTransactionNo("账务流水号" + i);
      entity.setBizTransactionNo("业务流水号" + i);
      entity.setMerchantOrderId("商户订单号" + i);
      entity.setSkuName("商品名称" + i);
      entity.setTransactionTime("2023-01-01 00:00:00");
      entity.setReciprocalAccount("对方账号" + i);
      entity.setIncome(new BigDecimal("100.00"));
      entity.setExpense(new BigDecimal("50.00"));
      entity.setBalance(new BigDecimal("50.00"));
      entity.setTransactionChannel("交易渠道" + i);
      entity.setTransactionType("业务类型" + i);
      entity.setNote("备注" + i);
      entity.setBizDesc("业务描述" + i);
      entity.setBizOrderId("业务订单号" + i);
      entity.setBizBaseOrderId("业务基础订单号" + i);
      entity.setBizBillSource("业务账单来源" + i);
      entities.add(entity);
    }
    return entities;
  }
}
