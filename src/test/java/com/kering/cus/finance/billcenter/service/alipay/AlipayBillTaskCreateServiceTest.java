package com.kering.cus.finance.billcenter.service.alipay;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.MerchantGrantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AlipayBillTaskCreateServiceTest {
    
    @Mock
    private BillTaskDAO billTaskDAO;
    
    @Mock
    private TransitLogDAO transitLogDAO;
    
    @Mock
    private MerchantConfigDAO merchantConfigDAO;
    
    @Mock
    private MerchantGrantConfigDAO merchantGrantConfigDAO;
    
    @InjectMocks
    private AlipayBillTaskCreateService alipayBillTaskCreateService;

    @BeforeEach
    void setUp() {
        alipayBillTaskCreateService = new AlipayBillTaskCreateService(
            billTaskDAO, transitLogDAO, merchantConfigDAO, merchantGrantConfigDAO
        );
    }

    @Test
    void givenAlipayChannel_whenMatches_thenReturnTrue() {
        // Act & Assert
        assertTrue(alipayBillTaskCreateService.matches(Channel.ALIPAY));
        assertFalse(alipayBillTaskCreateService.matches(Channel.JD));
    }

    @Test
    void givenTradeFlowType_whenGetSupportedTypes_thenContainsTradeFlow() {
        // Act
        List<BillType> supportedTypes = alipayBillTaskCreateService.getSupportedBillTypes();
        
        // Assert
        assertNotNull(supportedTypes);
        assertEquals(1, supportedTypes.size());
        assertTrue(supportedTypes.contains(BillType.TRADE_FLOW));
    }
}