package com.kering.cus.finance.billcenter.util;

import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class CsvWriterUtilsTest {

    private Path tempDir;
    private File testFile;

    @SneakyThrows
    private static void accept(Path path) {
        Files.delete(path);
    }

    @BeforeEach
    void setUp() throws IOException {
        tempDir = Files.createTempDirectory("csvtest");
        testFile = new File(tempDir.toFile(), "test.csv");
    }

    @AfterEach
    void tearDown() throws IOException {
        if (tempDir != null) {
            try (Stream<Path> stream = Files.walk(tempDir)
                    .sorted(Comparator.reverseOrder())) {
                stream.forEach(CsvWriterUtilsTest::accept);
            }
        }
    }


    @Test
    void givenValidHeader_whenWriteHeader_thenFileContainsExpectedContent() throws IOException {
        try (CsvWriterUtils writer = new CsvWriterUtils(testFile)) {
            writer.writeHeader(new String[]{"Name", "Age", "City"});
        }

        List<String> lines = Files.readAllLines(testFile.toPath());
        assertEquals(1, lines.size());

    }

    @Test
    void givenValidLines_whenWriteLine_thenFileContainsExpectedLines() throws IOException {
        try (CsvWriterUtils writer = new CsvWriterUtils(testFile)) {
            writer.writeLine(new String[]{"Alice", "30", "New York"});
            writer.writeLine(new String[]{"Bob", "25", "San Francisco"});
        }
        List<String> lines = Files.readAllLines(testFile.toPath());
        assertEquals(2, lines.size());

    }


    @Test
    void givenBatchData_whenWriteBatch_thenAllLinesAreWrittenCorrectly() throws IOException {
        List<String[]> data = Arrays.asList(
                new String[]{"Name", "Age", "City"},
                new String[]{"Alice", "30", "New York"},
                new String[]{"Bob", "25", "San Francisco"}
        );

        try (CsvWriterUtils writer = new CsvWriterUtils(testFile)) {
            writer.writeBatch(data);
        }

        List<String> lines = Files.readAllLines(testFile.toPath());
        assertEquals(3, lines.size());
    }

    @Test
    void givenLargeBatchData_whenWriteBatch_thenAllLinesAreWrittenCorrectly() throws IOException {
        StringBuilder largeValue = new StringBuilder();
        largeValue.append("X".repeat(2000));

        List<String[]> data = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            data.add(new String[]{"User" + i, largeValue.toString()});
        }

        try (CsvWriterUtils writer = new CsvWriterUtils(testFile)) {
            writer.writeBatch(data);
        }

        List<String> lines = Files.readAllLines(testFile.toPath());
        assertEquals(100, lines.size());
        assertTrue(lines.get(0).startsWith("\"User0\","));
    }

}
