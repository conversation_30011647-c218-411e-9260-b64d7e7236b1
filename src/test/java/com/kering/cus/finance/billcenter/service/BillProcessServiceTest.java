package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.BillProcessHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import org.apache.commons.io.input.NullInputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BillProcessServiceTest {

    @Mock
    private BillCenterConfig config;
    @Mock
    private BillTaskDAO billTaskDAO;
    @Mock
    private TransitLogDAO transitLogDAO;
    @Mock
    private AppStorageService appStorageService;
    @Mock
    private BillProcessHandler mockHandler;
    @Mock
    private BillTaskEventPublisher billTaskEventPublisher;

    @InjectMocks
    private BillProcessService billProcessService;

    private BillTaskEntity validTask;
    private BillTaskEntity runningTask;
    private BillTaskEntity futureTask;

    @BeforeEach
    void setUp() {
        billProcessService = new BillProcessService(
                config, billTaskDAO, transitLogDAO, appStorageService,
                Collections.singletonList(mockHandler), billTaskEventPublisher
        );

        validTask = new BillTaskEntity();
        validTask.setId(1L);
        validTask.setState(TaskState.WAIT_PROCESS);
        validTask.setVersion(1L);
        validTask.setTraceId("test_trace");
        validTask.setNextRunTime(ZonedDateTime.now().minusMinutes(1));
        validTask.setChannel(Channel.ALIPAY);
        validTask.setGrantType(GrantType.ISV);
        validTask.setBillType(BillType.TRADE_FLOW);
        validTask.setOriginBillUrl("test_url");

        runningTask = new BillTaskEntity();
        runningTask.setId(2L);
        runningTask.setState(TaskState.WAIT_PROCESS);
        runningTask.setVersion(1L);
        runningTask.setNextRunTime(ZonedDateTime.now().plusMinutes(1));

        futureTask = new BillTaskEntity();
        futureTask.setId(3L);
        futureTask.setState(TaskState.WAIT_PROCESS);
        futureTask.setVersion(1L);
        futureTask.setNextRunTime(ZonedDateTime.now().plusMinutes(1));
    }

    @Test
    void givenExistingTaskWithIdNotFound_whenProcessIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(anyLong())).thenReturn(Optional.empty());

        boolean result = billProcessService.processIfNecessary(1L);

        assertFalse(result);
    }

    @Test
    void givenRunningTaskInWaitProcessState_whenProcessIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(2L)).thenReturn(Optional.of(runningTask));

        boolean result = billProcessService.processIfNecessary(2L);

        assertFalse(result);
    }

    @Test
    void givenFutureTaskNotReadyToRun_whenProcessIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(3L)).thenReturn(Optional.of(futureTask));

        boolean result = billProcessService.processIfNecessary(3L);

        assertFalse(result);
    }

    @Test
    void givenLockAcquireFailed_whenProcessIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(1L)).thenReturn(Optional.of(validTask));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L))).thenReturn(0);

        boolean result = billProcessService.processIfNecessary(1L);

        assertFalse(result);
    }

    @Test
    void givenNoHandlerFound_whenProcessIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(1L)).thenReturn(Optional.of(validTask));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L))).thenReturn(1);
        when(config.getMaxRetryTimes()).thenReturn(3);

        boolean result = billProcessService.processIfNecessary(1L);

        assertFalse(result);
        verify(billTaskDAO).findById(1L);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L));
        verify(billTaskDAO).updateToFailedByIdAndVersion(
                eq(TaskState.PROCESS_FAILED), anyInt(), anyString(), any(), eq(1L), eq(2L)
        );
    }

    @Test
    void givenValidTaskWithSuccessfulProcessing_whenProcessIfNecessary_thenReturnsTrue() {
        when(billTaskDAO.findById(1L)).thenReturn(Optional.of(validTask));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L))).thenReturn(1);
        when(billTaskDAO.updateMerchantIdTaskStateAndSyncStateByIdAndVersion(
                any(), any(), any(), any(), any(), any()
        )).thenReturn(1);
        when(mockHandler.matches(Channel.ALIPAY, GrantType.ISV, BillType.TRADE_FLOW)).thenReturn(true);
        when(appStorageService.readStream(anyString())).thenReturn(new NullInputStream());

        boolean result = billProcessService.processIfNecessary(1L);

        assertTrue(result);
        verify(billTaskDAO).findById(1L);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L));
        verify(billTaskDAO).updateMerchantIdTaskStateAndSyncStateByIdAndVersion(
                any(), any(), any(), any(), any(),  any()
        );
    }

    @Test
    void givenNullInputStream_whenProcessIfNecessary_thenThrowsException() {
        when(billTaskDAO.findById(1L)).thenReturn(Optional.of(validTask));
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L))).thenReturn(1);
        when(mockHandler.matches(Channel.ALIPAY, GrantType.ISV, BillType.TRADE_FLOW)).thenReturn(true);
        when(appStorageService.readStream(anyString())).thenReturn(null);

        assertThrows(BillCenterBusinessException.class, () -> billProcessService.processIfNecessary(1L));
        verify(billTaskDAO).findById(1L);
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(1L), eq(1L));
        verify(billTaskDAO).updateToFailedByIdAndVersion(
                eq(TaskState.PROCESS_FAILED), anyInt(), anyString(), any(), eq(1L), eq(2L)
        );
    }

    @Test
    void givenNoTasks_whenProcessRetryIfNecessary_thenDoNothing() {
        when(config.getMaxRetryTimes()).thenReturn(3);
        when(billTaskDAO.findWaitProcessTasks(any(), any(), anyInt(), anyInt())).thenReturn(List.of());

        billProcessService.processRetryIfNecessary();

        verify(billTaskDAO).findWaitProcessTasks(any(), any(), eq(3), eq(BillProcessService.BATCH_SIZE));
        verifyNoMoreInteractions(billTaskDAO, billTaskEventPublisher);
    }

    @Test
    void givenSingleBatchTasks_whenProcessRetryIfNecessary_thenDispatchAll() {
        BillTaskEntity task1 = createTestBillEntityWithState(TaskState.PROCESS_FAILED);
        BillTaskEntity task2 = createTestBillEntityWithState(TaskState.PROCESS_FAILED);
        when(config.getMaxRetryTimes()).thenReturn(3);
        when(billTaskDAO.findWaitProcessTasks(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(task1, task2))
                .thenReturn(List.of());
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), anyLong(), anyLong()))
                .thenReturn(1);

        billProcessService.processRetryIfNecessary();

        verify(billTaskDAO, times(1)).findWaitProcessTasks(any(), any(), eq(3), eq(BillProcessService.BATCH_SIZE));
        verify(billTaskDAO, times(2)).updateNextRunTimeByIdAndVersion(any(), anyLong(), anyLong());
        verify(billTaskEventPublisher, times(2)).publishWaitProcessEvent(anyLong());
    }

    @Test
    void givenMultiBatchTasks_whenProcessRetryIfNecessary_thenProcessAllBatches() {
        List<BillTaskEntity> batch1 = IntStream.range(0, BillProcessService.BATCH_SIZE)
                .mapToObj(i -> createTestBillEntityWithState(TaskState.PROCESS_FAILED))
                .toList();
        List<BillTaskEntity> batch2 = List.of(createTestBillEntityWithState(TaskState.PROCESS_FAILED));

        when(config.getMaxRetryTimes()).thenReturn(3);
        when(billTaskDAO.findWaitProcessTasks(any(), any(), anyInt(), anyInt()))
                .thenReturn(batch1)
                .thenReturn(batch2);
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), anyLong(), anyLong()))
                .thenReturn(1);

        billProcessService.processRetryIfNecessary();

        verify(billTaskDAO, times(2)).findWaitProcessTasks(any(), any(), eq(3), eq(BillProcessService.BATCH_SIZE));
        verify(billTaskDAO, times(BillProcessService.BATCH_SIZE + 1)).updateNextRunTimeByIdAndVersion(any(), anyLong(), anyLong());
        verify(billTaskEventPublisher, times(BillProcessService.BATCH_SIZE + 1)).publishWaitProcessEvent(anyLong());
    }

    @Test
    void givenUpdateFailed_whenProcessRetryIfNecessary_thenSkipTask() {
        BillTaskEntity task = createTestBillEntityWithState(TaskState.WAIT_PROCESS);
        when(config.getMaxRetryTimes()).thenReturn(3);
        when(billTaskDAO.findWaitProcessTasks(any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(task))
                .thenReturn(List.of());
        when(billTaskDAO.updateNextRunTimeByIdAndVersion(any(), anyLong(), anyLong()))
                .thenReturn(0);

        billProcessService.processRetryIfNecessary();

        verify(billTaskDAO).findWaitProcessTasks(any(), any(), eq(3), eq(BillProcessService.BATCH_SIZE));
        verify(billTaskDAO).updateNextRunTimeByIdAndVersion(any(), eq(task.getId()), eq(task.getVersion()));
        verify(billTaskEventPublisher, never()).publishWaitProcessEvent(anyLong());
    }

    private BillTaskEntity createTestBillEntityWithState(TaskState state) {
        BillTaskEntity task = new BillTaskEntity();
        task.setId(0L);
        task.setTraceId("TEST_TRACE_ID");
        task.setChannel(Channel.ALIPAY);
        task.setGrantType(GrantType.ISV);
        task.setBillType(BillType.TRADE_FLOW);
        task.setMerchantId("TEST_MERCHANT_ID");
        task.setBrand("TEST_BRAND");
        task.setState(state);
        task.setVersion(1L);
        task.setNextRunTime(ZonedDateTime.now().minusMinutes(1));
        EntityUtils.fill(task, ZonedDateTime.now());
        return task;
    }

}