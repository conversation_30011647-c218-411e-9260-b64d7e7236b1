package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static com.kering.cus.finance.billcenter.util.Nulls.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class NullsTest {

    @Test
    void givenNullInput_whenNvl_thenReturnsDefaultValue() {
        assertEquals(ZERO_INT, nvl((Integer) null));
        assertEquals(ZERO_LONG, nvl((Long) null));
        assertEquals(ZERO_DOUBLE, nvl((Double) null));
        assertEquals(ZERO_BIG_DECIMAL, nvl((BigDecimal) null));
        assertEquals(ZERO_BOOLEAN, nvl((Boolean) null));
        assertEquals(Collections.emptyList(), nvl((List<?>) null));
        assertEquals(Collections.emptyMap(), nvl((Map<?, ?>) null));
    }

    @Test
    void givenNonNullInput_whenNvl_thenReturnsOriginalValue() {
        assertEquals(Integer.valueOf(5), nvl(5));
        assertEquals(Long.valueOf(10L), nvl(10L));
        assertEquals(Double.valueOf(3.14), nvl(3.14));
        assertEquals(BigDecimal.TEN, nvl(BigDecimal.TEN));
        assertEquals(true, nvl(true));
        assertEquals(List.of(1, 2, 3), nvl(List.of(1, 2, 3)));
        assertEquals(Map.of("key", "value"), nvl(Map.of("key", "value")));
    }

    @Test
    void givenNullInput_whenNvlWithDefault_thenReturnsCustomDefault() {
        assertEquals(42, nvl(null, 42));
        assertEquals("default", nvl(null, "default"));
        assertEquals(BigDecimal.ONE, nvl(null, BigDecimal.ONE));
    }

    @Test
    void givenNullAndSupplier_whenNvlWithSupplier_thenReturnsSupplierValue() {
        Supplier<String> supplier = () -> "supplied value";
        assertEquals("supplied value", nvl(null, supplier));
        assertEquals("original", nvl("original", supplier));
    }

    @Test
    void givenMultipleNullCandidates_whenNvlWithMultipleArgs_thenReturnsFirstNonNull() {
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime later = now.plusHours(1);
        assertNull(nvl((ZonedDateTime) null));
        assertEquals(now, nvl(null, now, later));
        assertEquals(later, nvl(null, null, later));
    }

    @Test
    void givenLongInput_whenNvlAsInt_thenConvertsToInteger() {
        assertEquals(0, nvlAsInt((Long) null));
        assertEquals(5, nvlAsInt(5L));
        assertEquals(10, nvlAsInt(10L, 20));
    }

    @Test
    void givenStringInput_whenNvlAsInt_thenParsesToInteger() {
        assertEquals(0, nvlAsInt((Long) null));
        assertEquals(0, nvlAsInt(""));
        assertEquals(0, nvlAsInt("abc"));
        assertEquals(123, nvlAsInt("123"));
    }

    @Test
    void givenIntegerInput_whenNvlAsLong_thenConvertsToLong() {
        assertEquals(0L, nvlAsLong((Integer) null));
        assertEquals(5L, nvlAsLong(5));
        assertEquals(10L, nvlAsLong(null, 10L));
        assertEquals(20L, nvlAsLong(20L, 30L));
    }
}