package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.kering.cus.finance.billcenter.util.CsvParserUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CsvParserUtilTest {

  @Test
  void givenBlankLine_whenParseCsvLineInvoked_thenReturnsEmptyList() {
    String line = "";
    List<String> result = CsvParserUtil.parseCsvLine(line);
    assertTrue(result.isEmpty());
  }

  @Test
  void givenValidLine_whenParseCsvLineInvoked_thenReturnsParsedList() {
    String line = "value1,value2,value3";
    List<String> expected = new ArrayList<>();
    expected.add("value1");
    expected.add("value2");
    expected.add("value3");
    List<String> result = CsvParserUtil.parseCsvLine(line);
    assertEquals(expected, result);
  }

  @Test
  void givenLineWithQuotedFields_whenParseCsvLineInvoked_thenReturnsCorrectlyParsedList() {
    String line = "value1,\"value2, with comma\",value3";
    List<String> expected = Arrays.asList("value1", "value2, with comma", "value3");
    List<String> result = CsvParserUtil.parseCsvLine(line);
    assertIterableEquals(expected, result);
  }

  @Test
  void givenValidColumn_whenSafeGetStringInvoked_thenReturnsColumnValue() {
    List<String> data = new ArrayList<>();
    data.add("value1");
    data.add("value2");
    data.add("value3");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("column1");
    headerMap.add("column2");
    headerMap.add("column3");
    String result = CsvParserUtil.safeGetString(data, headerMap, "column2");
    assertEquals("value2", result);
  }

  @Test
  void givenInvalidColumn_whenSafeGetStringInvoked_thenReturnsNull() {
    List<String> data = new ArrayList<>();
    data.add("value1");
    data.add("value2");
    data.add("value3");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("column1");
    headerMap.add("column2");
    headerMap.add("column3");
    String result = CsvParserUtil.safeGetString(data, headerMap, "column4");
    assertNull(result);
  }

  @Test
  void givenValidValue_whenSafeParseBigDecimalInvoked_thenReturnsParsedBigDecimal() {
    List<String> data = new ArrayList<>();
    data.add("100.00");
    data.add("200.00");
    data.add("300.00");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("column1");
    headerMap.add("column2");
    headerMap.add("column3");
    BigDecimal defaultValue = BigDecimal.ZERO;
    BigDecimal result = CsvParserUtil.safeParseBigDecimal(data, headerMap, "column2", defaultValue);
    assertEquals(new BigDecimal("200.00"), result);
  }

  @Test
  void givenInvalidValue_whenSafeParseBigDecimalInvoked_thenReturnsDefaultValue() {
    List<String> data = new ArrayList<>();
    data.add("invalid");
    data.add("200.00");
    data.add("300.00");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("column1");
    headerMap.add("column2");
    headerMap.add("column3");
    BigDecimal defaultValue = BigDecimal.ZERO;
    BigDecimal result = CsvParserUtil.safeParseBigDecimal(data, headerMap, "column1", defaultValue);
    assertEquals(defaultValue, result);
  }

  @Test
  void givenSummaryRow_whenIsSummaryRowInvoked_thenReturnsTrue() {
    List<String> rowData = new ArrayList<>();
    rowData.add("总交易单数");
    rowData.add("100");
    rowData.add("200");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("交易时间");
    headerMap.add("公众账号ID");
    headerMap.add("商户号");
    String columnName = "交易时间";
    String totalLineFlag = "总交易单数";
    boolean result = CsvParserUtil.isSummaryRow(rowData, headerMap, columnName, totalLineFlag);
    assertTrue(result);
  }

  @Test
  void givenNonSummaryRow_whenIsSummaryRowInvoked_thenReturnsFalse() {
    List<String> rowData = new ArrayList<>();
    rowData.add("2025-01-01 10:00:00");
    rowData.add("appid1");
    rowData.add("merchant1");
    List<String> headerMap = new ArrayList<>();
    headerMap.add("交易时间");
    headerMap.add("公众账号ID");
    headerMap.add("商户号");
    String columnName = "交易时间";
    String totalLineFlag = "总交易单数";
    boolean result = CsvParserUtil.isSummaryRow(rowData, headerMap, columnName, totalLineFlag);
    assertFalse(result);
  }

  @Test
  void givenNoSpecialCharacters_whenAppendCsvField_thenAppendedCorrectly() {
    StringBuilder line = new StringBuilder();
    CsvParserUtil.appendCsvField(line, "test");
    assertEquals("test", line.toString());
  }

  @Test
  void givenSpecialCharacters_whenAppendCsvField_thenEscapedCorrectly() {
    try (MockedStatic<StringUtils> stringUtilsMockedStatic = Mockito.mockStatic(StringUtils.class)) {
      stringUtilsMockedStatic.when(() -> StringUtils.isBlank("test,")).thenReturn(false);

      StringBuilder line = new StringBuilder();
      CsvParserUtil.appendCsvField(line, "test,");
      assertEquals("\"test,\"", line.toString());
    }
  }

  @Test
  void givenNullValue_whenAppendCsvField_thenSkipped() {
    StringBuilder line = new StringBuilder();
    CsvParserUtil.appendCsvField(line, null);
    assertEquals("", line.toString());
  }
}
