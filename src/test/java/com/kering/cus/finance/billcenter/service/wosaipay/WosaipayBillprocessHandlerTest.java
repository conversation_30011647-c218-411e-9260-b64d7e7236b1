package com.kering.cus.finance.billcenter.service.wosaipay;


import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.WosaipayBillConverter;
import com.kering.cus.finance.billcenter.dao.WosaipayTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.vo.WosaipayTradeFlowVO;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class WosaipayBillprocessHandlerTest {
    @TempDir
    Path tempDir;

    @Mock
    private WosaipayTradeFlowDAO wosaipayTradeFlowDAO;

    @Mock
    private WosaipayBillConverter wosaipayBillConverter;


    @InjectMocks
    private WosaipayBillprocessHandler wosaipayBillprocessHandler;

    private File tempFile;
    private BillTaskEntity task;

    @BeforeEach
    void setUp() throws IOException {
        tempFile = tempDir.resolve("test.xlsx").toFile();
        createTestExcelFile(tempFile);

        task = new BillTaskEntity();
        task.setBrand("testBrand");
    }


    @Test
    void givenValidParams_whenMatches_thenReturnTrue() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.WOSAIPAY, GrantType.SFTP, BillType.TRADE_FLOW);
        assertTrue(result);
    }

    @Test
    void givenMismatchedBillType_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.WOSAIPAY, GrantType.SFTP, BillType.FUND_FLOW);
        assertFalse(result);
    }

    @Test
    void givenMismatchedGrantType_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.WOSAIPAY, GrantType.MERCHANT, BillType.TRADE_FLOW);
        assertFalse(result);
    }

    @Test
    void givenMismatchedChannel_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.ALIPAY, GrantType.SFTP, BillType.TRADE_FLOW);
        assertFalse(result);
    }

    @Test
    void givenNullChannel_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(null, GrantType.SFTP, BillType.TRADE_FLOW);
        assertFalse(result);
    }

    @Test
    void givenNullGrantType_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.WOSAIPAY, null, BillType.TRADE_FLOW);
        assertFalse(result);
    }

    @Test
    void givenNullBillType_whenMatches_thenReturnFalse() {
        boolean result = wosaipayBillprocessHandler.matches(Channel.WOSAIPAY, GrantType.SFTP, null);
        assertFalse(result);
    }

    @Test
    void givenNullFile_whenProcess_thenShouldReturnEmptyList() {
        List<String> result = wosaipayBillprocessHandler.process(null, task);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenNonExcelFile_whenProcess_thenShouldReturnEmptyList() {
        File nonExcelFile = tempDir.resolve("test.txt").toFile();
        List<String> result = wosaipayBillprocessHandler.process(nonExcelFile, task);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenValidExcel_whenProcess_thenShouldProcessData() {
        WosaipayTradeFlowEntity mockEntity = new WosaipayTradeFlowEntity();
        mockEntity.setMerchantId("merchant123");
        when(wosaipayBillConverter.toWosaipayBillEntity(any(), any())).thenReturn(mockEntity);

        List<String> result = wosaipayBillprocessHandler.process(tempFile, task);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("merchant123", result.get(0));

        verify(wosaipayTradeFlowDAO, atLeastOnce()).createBatch(anyList());
    }

    @Test
    void givenEmptyExcel_whenProcess_thenShouldReturnEmptyList() throws IOException {
        File emptyFile = tempDir.resolve("empty.xlsx").toFile();
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(emptyFile)) {
            workbook.createSheet("明细");
            workbook.write(fos);
        }

        List<String> result = wosaipayBillprocessHandler.process(emptyFile, task);
        assertTrue(result.isEmpty());
    }

    @Test
    void givenIOExceptionOccurs_whenProcess_thenShouldThrowBusinessException() throws IOException {
        File corruptFile = tempDir.resolve("large.xlsx").toFile();
        try (FileOutputStream fos = new FileOutputStream(corruptFile)) {
            fos.write("corrupt data".getBytes());
        }

        assertThrows(BillCenterBusinessException.class, () -> wosaipayBillprocessHandler.process(corruptFile, task));
    }

    @Test
    void givenLargeExcel_whenProcess_thenShouldHandleBatchProcessingCorrectly() throws IOException {
        File largeFile = tempDir.resolve("large.xlsx").toFile();
        createLargeTestExcelFile(largeFile);

        WosaipayTradeFlowEntity mockEntity = new WosaipayTradeFlowEntity();
        mockEntity.setMerchantId("merchant_%d");
        when(wosaipayBillConverter.toWosaipayBillEntity(any(), any()))
                .thenAnswer(invocation -> {
                    WosaipayTradeFlowVO vo = invocation.getArgument(1);
                    mockEntity.setMerchantId(String.format("merchant_%s", vo.getMerchantId()));
                    return mockEntity;
                });

        List<String> result = wosaipayBillprocessHandler.process(largeFile, task);

        verify(wosaipayTradeFlowDAO, atLeast(2)).createBatch(anyList());
        assertFalse(result.isEmpty());
    }

    private void createTestExcelFile(File file) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(file)) {

            Sheet sheet = workbook.createSheet("明细");

            for (int i = 0; i < 7; i++) {
                sheet.createRow(i);
            }

            Row header = sheet.createRow(7);
            header.createCell(0).setCellValue("商户号");
            header.createCell(1).setCellValue("交易金额");

            Row dataRow = sheet.createRow(8);
            dataRow.createCell(0).setCellValue("merchant123");
            dataRow.createCell(1).setCellValue("100.00");

            workbook.write(fos);
        }
    }

    private void createLargeTestExcelFile(File file) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(file)) {
            Sheet sheet = workbook.createSheet("明细");

            for (int i = 0; i < 7; i++) {
                sheet.createRow(i);
            }

            Row header = sheet.createRow(7);
            header.createCell(0).setCellValue("商户号");
            header.createCell(1).setCellValue("交易金额");

            for (int i = 8; i < 8 + 1500; i++) {
                Row dataRow = sheet.createRow(i);
                dataRow.createCell(0).setCellValue("merchant_" + (i - 7));
                dataRow.createCell(1).setCellValue(i * 10);
            }

            workbook.write(fos);
        }
    }
}
