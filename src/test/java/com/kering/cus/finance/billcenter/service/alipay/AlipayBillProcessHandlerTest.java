package com.kering.cus.finance.billcenter.service.alipay;

import com.kering.cus.finance.billcenter.config.AlipayBillConfig;
import com.kering.cus.finance.billcenter.converter.AlipayBillConverter;
import com.kering.cus.finance.billcenter.dao.AlipayBillFlowDAO;
import com.kering.cus.finance.billcenter.entity.AlipayBillFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import com.kering.cus.finance.billcenter.util.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;


import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@Slf4j
@ExtendWith(MockitoExtension.class)
class AlipayBillProcessHandlerTest {


    @InjectMocks
    private AlipayBillProcessHandler handler;

    @Mock
    private AlipayBillFlowDAO alipayBillFlowDAO;

    @Mock
    private AlipayBillConverter alipayBillConverter;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private TransactionTemplate transactionTemplate;

    @Mock
    private AlipayBillConfig alipayBillConfig;


    @Test
    void givenCorrectParams_whenMatches_thenReturnTrue() {
        boolean result = handler.matches(Channel.ALIPAY, GrantType.ISV, BillType.TRADE_FLOW);
        assertTrue(result);
    }

    @Test
    void givenWrongChannel_whenMatches_thenReturnFalse() {
        boolean result = handler.matches(Channel.WECHAT, GrantType.ISV, BillType.TRADE_FLOW);
        assertFalse(result);
    }




    @Test
    void givenValidBillFile_whenProcess_thenReturnSuccess() throws IOException {
        BillTaskEntity task = createBillTask();

        File mockZipFile = mock(File.class);

        when(alipayBillConverter.toAlipayBillFlowEntity(  any(),any()) ).thenReturn(createMockEntity());
        when(alipayBillFlowDAO.findByAccountTransactionNos(any(), anyString())).thenReturn(Collections.emptyList());

        doAnswer(invocation -> {
            TransactionCallback<?> callback = invocation.getArgument(0);
            return callback.doInTransaction(mock(TransactionStatus.class));
        }).when(transactionTemplate).execute(any());

        when(alipayBillConfig.getTransactionTypes()).thenReturn("交易付款,交易退款");
        when(alipayBillConfig.getBillsSuffix()).thenReturn("_details");

        CsvReaderUtils mockCsvReader = mock(CsvReaderUtils.class);
        doNothing().when(mockCsvReader).open(any());
        doAnswer(invocation -> {
            Consumer<Map<String, String>> consumer = invocation.getArgument(0);
            consumer.accept(createMockBillMap());
            consumer.accept(createMockBillMap());
            return null;
        }).when(mockCsvReader).forEach(any());

        try (MockedStatic<ZipUtils> zipUtilMockedStatic = mockStatic(ZipUtils.class);
             MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
             MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class);
             MockedStatic<StringUtils> stringUtilMockedStatic = mockStatic(StringUtils.class);
             MockedStatic<CsvReaderUtils> mockedStatic = mockStatic(CsvReaderUtils.class)) {

            Path mockPath1 = mock(Path.class);
            when(mockPath1.getFileName()).thenReturn(Paths.get("test_details.csv"));

            Path mockPath2 = mock(Path.class);
            when(mockPath2.getFileName()).thenReturn(Paths.get("file2.csv"));

            mockedFiles.when(() -> Files.walk(any(Path.class))).thenReturn(Stream.of(mockPath1, mockPath2));
            mockedFiles.when(() -> Files.isRegularFile(any(Path.class))).thenReturn(true);


            zipUtilMockedStatic.when(() -> ZipUtils.isZipFile(mockZipFile)).thenReturn(true);
            zipUtilMockedStatic.when(() -> ZipUtils.unzip(any(), any(), any())).thenAnswer(invocation -> null);

            fileUtilsMockedStatic.when(() -> FileUtils.deleteQuietly(any())).thenReturn(true);

            stringUtilMockedStatic.when(() -> StringUtils.trimAndRemoveSpecialChars(anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            CsvReaderUtils.Builder mockBuilder = mock(CsvReaderUtils.Builder.class);
            when(mockBuilder.headerLine(anyInt())).thenReturn(mockBuilder);
            when(mockBuilder.charset(any(Charset.class))).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockCsvReader);
            mockedStatic.when(CsvReaderUtils::builder).thenReturn(mockBuilder);

            List<String> process = handler.process(mockZipFile, task);
            assertTrue(CollectionUtils.isEmpty(process));
        }


    }


    @Test
    void givenInvalidCsvFile_whenProcess_thenThrowException() throws IOException {
        BillTaskEntity task = createBillTask();

        File mockZipFile = mock(File.class);
        when(alipayBillConfig.getBillsSuffix()).thenReturn("_details");

        CsvReaderUtils mockCsvReader = mock(CsvReaderUtils.class);
        doThrow(new IOException("Read error")).when(mockCsvReader).open(any());

        try (MockedStatic<ZipUtils> zipUtilMockedStatic = mockStatic(ZipUtils.class);
             MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
             MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class);
             MockedStatic<StringUtils> stringUtilMockedStatic = mockStatic(StringUtils.class);
             MockedStatic<CsvReaderUtils> mockedStatic = mockStatic(CsvReaderUtils.class)) {

            Path mockPath1 = mock(Path.class);
            when(mockPath1.getFileName()).thenReturn(Paths.get("test_details.csv"));


            mockedFiles.when(() -> Files.walk(any(Path.class))).thenReturn(Stream.of(mockPath1));
            mockedFiles.when(() -> Files.isRegularFile(any(Path.class))).thenReturn(true);


            zipUtilMockedStatic.when(() -> ZipUtils.isZipFile(mockZipFile)).thenReturn(true);
            zipUtilMockedStatic.when(() -> ZipUtils.unzip(any(), any(), any())).thenAnswer(invocation -> null);

            fileUtilsMockedStatic.when(() -> FileUtils.deleteQuietly(any())).thenReturn(true);

            stringUtilMockedStatic.when(() -> StringUtils.trimAndRemoveSpecialChars(anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            CsvReaderUtils.Builder mockBuilder = mock(CsvReaderUtils.Builder.class);
            when(mockBuilder.headerLine(anyInt())).thenReturn(mockBuilder);
            when(mockBuilder.charset(any(Charset.class))).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockCsvReader);
            mockedStatic.when(CsvReaderUtils::builder).thenReturn(mockBuilder);

            assertThrows(BillCenterBusinessException.class, () ->
                    handler.process(mockZipFile, task));

        }


    }


    private BillTaskEntity createBillTask() {
        BillTaskEntity task = new BillTaskEntity();
        task.setId(1L);
        task.setChannel(Channel.ALIPAY);
        task.setBrand("Kering");
        task.setMerchantId("test_merchant");
        task.setIsSyncToBlackline(false);
        task.setIsSyncToSap(true);
        task.setIsTmall(true);
        return task;
    }


    @Test
    void testProcess_ShouldReturnFalse_IfNotZipFile() {
        File tempFile = mock(File.class);
        when(tempFile.getName()).thenReturn("test.txt");

        BillTaskEntity task = createBillTask();

        try (MockedStatic<ZipUtils> zipUtilMockedStatic = mockStatic(ZipUtils.class)) {
            zipUtilMockedStatic.when(() -> ZipUtils.isZipFile(tempFile)).thenReturn(false);

            List<String> process = handler.process(tempFile, task);
            assertTrue(CollectionUtils.isEmpty(process));
        }
    }


    private Map<String, String> createMockBillMap() {
        Map<String, String> map = new HashMap<>();
        map.put("账务流水号", "123456");
        map.put("业务类型", "交易付款");
        map.put("业务基础订单号", "T200P789012");
        map.put("收入金额", "100.00");
        map.put("对方账户", "buyer_account");
        return map;
    }



    private AlipayBillFlowEntity createMockEntity() {
       return AlipayBillFlowEntity.builder().accountTransactionNo("123456").merchantId("test").build();
    }


}
