package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.*;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilsTest {



    @Test
    void givenEmptyInput_whenExtractDateEnhanced_thenReturnsNull() {
        LocalDate result = DateUtils.extractDateEnhanced("", new String[]{"yyyy-MM-dd"});
        assertNull(result);
    }

    @Test
    void givenValidPatternMatched_whenExtractDateEnhanced_thenParsesCorrectly() {
        LocalDate result = DateUtils.extractDateEnhanced("2024-01-01", new String[]{"yyyy-MM-dd"});
        assertEquals(LocalDate.of(2024, 1, 1), result);
    }

    @Test
    void givenFirstPatternMatches_whenExtractDateEnhanced_thenUsesFirstPattern() {
        LocalDate result = DateUtils.extractDateEnhanced("2024-01-01", new String[]{"yyyy-MM-dd", "yyyy/MM/dd"});
        assertEquals(LocalDate.of(2024, 1, 1), result);
    }

    @Test
    void givenSecondPatternMatches_whenExtractDateEnhanced_thenUsesSecondPattern() {
        LocalDate result = DateUtils.extractDateEnhanced("2024/01/01", new String[]{"yyyy-MM-dd", "yyyy/MM/dd"});
        assertEquals(LocalDate.of(2024, 1, 1), result);
    }

    @Test
    void givenNoPatternMatches_whenExtractDateEnhanced_thenReturnsNull() {
        LocalDate result = DateUtils.extractDateEnhanced("2024-01-01", new String[]{"yyyy/MM/dd", "yyyy.MM.dd"});
        assertNull(result);
    }

    @Test
    void givenInvalidDate_whenExtractDateEnhanced_thenReturnsNull() {
        LocalDate result = DateUtils.extractDateEnhanced("invalid", new String[]{"yyyy-MM-dd"});
        assertNull(result);
    }

    @Test
    void givenInvalidDateFormat_whenExtractDateEnhanced_thenReturnsNull() {
        LocalDate result = DateUtils.extractDateEnhanced("2024-02-30", new String[]{"yyyy/MM/dd"});
        assertNull(result);
    }


    @Test
    void givenEmptyPatterns_whenExtractDateEnhanced_thenReturnsNull() {
        LocalDate result = DateUtils.extractDateEnhanced("2024-01-01", new String[]{});
        assertNull(result);
    }



    @Test
    void givenValidInputStrWithShortDateFormat_whenConvertDateStr_thenReturnsFormattedDate() {
        // Given
        String inputStr = "2023-10-05";
        String pattern = "yyyyMMdd";

        // When
        String result = DateUtils.convertDateStr(inputStr, pattern);

        // Then
        assertEquals("20231005", result);
    }

    @Test
    void givenInvalidInputStr_whenConvertDateStr_thenThrowsIllegalArgumentException() {
        // Given
        String inputStr = "invalid-date";
        String pattern = "yyyyMMdd";

        // When & Then
        Exception exception = assertThrows(IllegalArgumentException.class, () -> DateUtils.convertDateStr(inputStr, pattern));

        assertEquals("date is null", exception.getMessage());
    }

    @Test
    void givenValidInputStrAndDifferentPattern_whenConvertDateStr_thenReturnsCorrectFormat() {
        // Given
        String inputStr = "2023-10-05";
        String pattern = "MM/dd/yyyy";

        // When
        String result = DateUtils.convertDateStr(inputStr, pattern);

        // Then
        assertEquals("10/05/2023", result);
    }

    @Test
    void givenNullOrEmptyInput_whenMinusDay_thenReturnsEmptyString() {
        // Given & When
        String result1 = DateUtils.minusDay(null, "yyyyMMdd", 1);
        String result2 = DateUtils.minusDay("", "yyyyMMdd", 1);

        // Then
        assertEquals("", result1);
        assertEquals("", result2);
    }

    @Test
    void givenValidDateAndPattern_whenMinusDay_thenReturnsCorrectFormattedDate() {
        // Given
        String dateStr = "2024-01-05";
        String pattern = "yyyyMMdd";
        int days = 5;

        // When
        String result = DateUtils.minusDay(dateStr, pattern, days);

        // Then
        assertEquals("20231231", result);
    }

    @Test
    void givenDateTimeFormat_whenMinusDay_thenParsesSuccessfully() {
        // Given
        String dateStr = "2024-01-05 12:00:00";
        String pattern = "yyyy-MM-dd";
        int days = 1;

        // When
        String result = DateUtils.minusDay(dateStr, pattern, days);

        // Then
        assertEquals("2024-01-04", result);
    }

    @Test
    void givenInvalidDateFormat_whenMinusDay_thenReturnsEmptyString() {
        // Given
        String dateStr = "invalid-date";
        String pattern = "yyyyMMdd";
        int days = 1;

        // When
        String result = DateUtils.minusDay(dateStr, pattern, days);

        // Then
        assertEquals("", result);
    }

    @Test
    void givenNegativeDays_whenMinusDay_thenAddsDays() {
        // Given
        String dateStr = "2024-01-05";
        String pattern = "yyyy-MM-dd";
        int days = -2;

        // When
        String result = DateUtils.minusDay(dateStr, pattern, days);

        // Then
        assertEquals("2024-01-07", result);
    }

    @Test
    void givenInvalidPattern_whenMinusDay_thenHandlesExceptionGracefully() {
        // Given
        String dateStr = "2024-01-05";
        String pattern = "invalidPattern";
        int days = 1;

        // When
        String result = DateUtils.minusDay(dateStr, pattern, days);

        // Then
        assertEquals("", result);
    }
}
