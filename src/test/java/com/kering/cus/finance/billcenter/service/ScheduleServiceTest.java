package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScheduleServiceTest {
    @Mock
    private BillCenterConfig billCenterConfig;

    @Mock
    private OauthGrantService oauthGrantService;

    @Mock
    private ApiBillTaskCreateProxyService apiBillTaskCreateProxyService;

    @Mock
    private SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService;

    @Mock
    private BillAcquireService billAcquireService;

    @Mock
    private BillProcessService billProcessService;

    @Mock
    private SapBillArchiveService sapBillArchiveService;
    @Mock
    private BlacklineBillArchiveService blacklineBillArchiveService;

    @InjectMocks
    private ScheduleService scheduleService;

    @Test
    void givenValidCall_whenRefreshOauthAccessTokenTask_thenCallsService() {
        // Act
        scheduleService.refreshOauth2AccessToken();

        // Assert
        verify(oauthGrantService).refreshAccessTokenIfNecessary();
    }

    @Test
    void givenValidCall_whenAcquireRetryTask_thenCallsService() {
        // Act
        scheduleService.acquireBillRetryIfNecessary();

        // Assert
        verify(billAcquireService).acquireRetryIfNecessary();
    }

    @Test
    void givenValidCall_whenProcessRetryTask_thenCallsService() {
        // Act
        scheduleService.processBillRetryIfNecessary();

        // Assert
        verify(billProcessService).processRetryIfNecessary();
    }

    @Test
    void givenValidChannel_whenInitializeApiTask_thenCallsService() {
        // Arrange
        String channel = Channel.JD.name();

        // Act
        scheduleService.initializeApiTaskIfNecessary(channel);

        // Assert
        verify(apiBillTaskCreateProxyService).createTaskIfNecessary(channel);
    }

    @Test
    void givenValidParams_whenInitializeSftpTask_thenCallsService() {
        // Arrange
        String sftpPath = "/test/path";

        // Act
        scheduleService.initializeSftpTaskIfNecessary(sftpPath);

        // Assert
        verify(sftpBillTaskCreateProxyService).createTaskIfNecessary(
                eq(sftpPath),
                any(ZonedDateTime.class),
                eq("SCHEDULER")
        );
    }

    @Test
    void givenTaskId_whenAcquireBill_thenCallsService() {
        // Arrange
        Long taskId = 123L;

        // Act
        scheduleService.acquireBillIfNecessary(taskId);

        // Assert
        verify(billAcquireService).acquireIfNecessary(taskId);
    }

    @Test
    void givenTaskId_whenProcessBill_thenCallsService() {
        // Arrange
        Long taskId = 123L;

        // Act
        scheduleService.processBillIfNecessary(taskId);

        // Assert
        verify(billProcessService).processIfNecessary(taskId);
    }

    @Test
    void givenChannel_whenBillPushToSap_thenCallsService() {
        // Arrange
        String channel = Channel.JD.name();

        // Act
        scheduleService.billPushToSapIfNecessary(channel);

        // Assert
        verify(sapBillArchiveService).archiveAllIfNecessary(Channel.JD);
    }

    @Test
    void givenChannel_whenBillPushToBlackline_thenCallsService() {
        // Arrange
        String channel = Channel.JD.name();

        // Act
        scheduleService.billPushToBlacklineIfNecessary(channel);

        // Assert
        verify(blacklineBillArchiveService).archiveIfNecessary(Channel.JD);
    }
}