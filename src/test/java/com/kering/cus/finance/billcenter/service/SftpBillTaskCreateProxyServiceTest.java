package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.AbstractSftpBillTaskCreateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SftpBillTaskCreateProxyServiceTest {

    @Mock
    private BillCenterConfig config;
    @Mock
    private BillTaskEventPublisher billTaskEventPublisher;
    @Mock
    private List<AbstractSftpBillTaskCreateService> sftpBillTaskCreateServices;

    @InjectMocks
    private SftpBillTaskCreateProxyService sftpBillTaskCreateProxyService;

    @BeforeEach
    void setUp() {
        // 初始化测试服务
    }

    @Test
    void shouldCreateTaskWhenValidTopic() {
        // Arrange
        AbstractSftpBillTaskCreateService mockService = mock(AbstractSftpBillTaskCreateService.class);
        when(mockService.matches("/path/to/file")).thenReturn(true);
        when(mockService.createTaskIfNecessary(anyString(), any(ZonedDateTime.class), anyString()))
                .thenReturn(1L);

        when(sftpBillTaskCreateServices.stream().filter(service -> service.matches("/path/to/file")))
                .thenReturn(Stream.of(mockService));

        // Act
        boolean result = sftpBillTaskCreateProxyService.createTaskIfNecessary(
                "/path/to/file", ZonedDateTime.now(), "test"
        );

        // Assert
        assertTrue(result);
        verify(mockService).createTaskIfNecessary(anyString(), any(ZonedDateTime.class), anyString());
        verify(billTaskEventPublisher).publishWaitAcquireEvent(anyLong());
    }

}