package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class ErrorsTest {

    @Test
    void givenErrorCodeAndMessage_whenFormat_thenReturnsFormattedString() {
        // Arrange
        String errorCode = "ERR001";
        String errorMsg = "Invalid parameter";

        // Act
        String result = Errors.format(errorCode, errorMsg);

        // Assert
        assertEquals("Invalid parameter(ERR001)", result);
    }

    @Test
    void givenPrefixAndError_whenFormat_thenReturnsPrefixedFormattedString() {
        // Arrange
        String prefix = "Validation";
        String errorCode = "ERR002";
        String errorMsg = "Field required";

        // Act
        String result = Errors.format(prefix, errorCode, errorMsg);

        // Assert
        assertEquals("Validation: Field required(ERR002)", result);
    }

    @Test
    void givenLongMessage_whenTruncateWithMaxLength_thenReturnsTruncatedString() {
        // Arrange
        String errorMsg = "This is a very long error message that needs to be truncated";
        int maxLength = 20;

        // Act
        String result = Errors.format(errorMsg, maxLength);

        // Assert
        assertEquals(20, result.length());  // 验证长度
        assertEquals("This is a very long ", result);  // 验证完整截断内容
    }

    @Test
    void givenLongMessageWithErrorCode_whenTruncateWithMaxLength_thenReturnsTruncatedString() {
        // Arrange
        String errorCode = "ERR002";
        String errorMsg = "This is a very long error message that needs to be truncated";
        int maxLength = 20;

        // Act
        String result = Errors.format(errorCode, errorMsg, maxLength);

        // Assert
        assertEquals(20, result.length());  // 验证长度
        assertEquals("This is a very long ", result);  // 验证完整截断内容
    }

    @Test
    void givenNullMessage_whenFormat_thenHandlesNullGracefully() {
        // Arrange
        String errorMsg = null;
        int maxLength = 10;

        // Act
        String result = Errors.format(errorMsg, maxLength);

        // Assert
        assertEquals("(null)", result);
    }
}