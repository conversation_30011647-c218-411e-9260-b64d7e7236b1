package com.kering.cus.finance.billcenter.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;


@Slf4j
@ExtendWith(MockitoExtension.class)
class CsvReaderUtilsTest {

    private Path tempFile;
    private File testFile;

    @BeforeEach
    void setUp() throws IOException {
        tempFile = Files.createTempFile("csv-test", ".csv");
        testFile = tempFile.toFile();

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(testFile))) {
            writer.write("Name,Age,Location\n");
            writer.write("\"<PERSON>, <PERSON>\",35,\"New York\"\n");
            writer.write("<PERSON><PERSON>, <PERSON>, \"Los Angeles\"\n");
            writer.write("\n");
            writer.write("Brown,45,\"San Francisco\"\n");
        }
    }

    @AfterEach
    void tearDown() {
        if (testFile != null && testFile.exists() && !testFile.delete()) {
            log.error("Failed to delete temporary file: {}", testFile.getAbsolutePath());
        }


    }


    @Test
    void givenValidCsvFile_whenReadNext_thenReturnsFirstRowSuccessfully() throws Exception {
        try (CsvReaderUtils csvReader = CsvReaderUtils.builder()
                .charset(StandardCharsets.UTF_8)
                .headerLine(0)
                .trimValues(true)
                .ignoreEmptyLines(true)
                .build()) {

            csvReader.open(tempFile);

            Map<String, String> row = csvReader.readNext();
            assertNotNull(row);
            assertEquals(3, row.size());
            assertEquals("Name", row.keySet().toArray()[0]);
            assertEquals("\"Smith, John", row.get("Name"));
            assertEquals("35", row.get("Age"));
            assertEquals("\"New York", row.get("Location"));

        }
    }


    @Test
    void givenCustomSeparator_whenReadCsvWithSemicolon_thenReturnsCorrectData() throws Exception {
        Path path = tempFile.resolveSibling("semi-colon.csv");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(path.toFile()))) {
            writer.write("Name;Age;Location\n");
            writer.write("Smith;35;New York\n");
        }

        try (CsvReaderUtils csvReader = CsvReaderUtils.builder()
                .separator(';')
                .charset(StandardCharsets.UTF_8)
                .headerLine(0)
                .build()) {

            csvReader.open(path);

            Map<String, String> row = csvReader.readNext();
            assertNotNull(row);
            assertEquals(3, row.size());
            assertEquals("Smith", row.get("Name"));
            assertEquals("35", row.get("Age"));
            assertEquals("New York", row.get("Location"));
        }
    }







    @Test
    void givenCsvFileWithEmptyLines_whenForEachProcessed_thenProcessesThreeRows() throws Exception {
        try (CsvReaderUtils csvReader = CsvReaderUtils.builder()
                .charset(StandardCharsets.UTF_8)
                .headerLine(0)
                .trimValues(true)
                .ignoreEmptyLines(true)
                .build()) {

            csvReader.open(tempFile);

            final int[] count = {0};
            csvReader.forEach(row -> {
                assertNotNull(row);
                assertEquals(3, row.size());
                count[0]++;
            });

            assertEquals(3, count[0]);
        }
    }



    @Test
     void givenHeaderAtThirdLine_whenLocateHeaderLine_thenReadsHeaderCorrectly() throws Exception {
        Path path = tempFile.resolveSibling("multi-line.csv");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(path.toFile()))) {
            writer.write("First Line\n");
            writer.write("Second Line\n");
            writer.write("Name,Age,Location\n");
            writer.write("Smith,35,New York\n");
        }

        try (CsvReaderUtils csvReader = CsvReaderUtils.builder()
                .headerLine(2)
                .build()) {

            csvReader.open(path);

            Map<String, String> row = csvReader.readNext();
            assertNotNull(row);
            assertEquals("Smith", row.get("Name"));
            assertEquals("35", row.get("Age"));
            assertEquals("Location", row.keySet().toArray()[2]);
        }
    }


    @Test
     void givenFileWithOnlyHeader_whenReadNextAtEndOfFile_thenReturnsEmptyMap() throws Exception {
        Path path = tempFile.resolveSibling("single-line.csv");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(path.toFile()))) {
            writer.write("Name,Age,Location\n");
        }

        try (CsvReaderUtils csvReader = CsvReaderUtils.builder().build()) {
            csvReader.open(path);

            Map<String, String> row = csvReader.readNext();
            assertTrue(CollectionUtils.isEmpty(row));
        }
    }
}
