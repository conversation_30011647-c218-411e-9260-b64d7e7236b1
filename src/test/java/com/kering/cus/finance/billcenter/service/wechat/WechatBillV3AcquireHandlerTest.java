package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.kering.cus.finance.billcenter.client.wechat.WechatPayUtils;
import com.kering.cus.finance.billcenter.client.wechat.WechatQueryBillRequest;
import com.kering.cus.finance.billcenter.client.wechat.WechatQueryBillResponse;
import com.kering.cus.finance.billcenter.config.WechatBillConfig;
import com.kering.cus.finance.billcenter.config.WechatV3MerchantAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WechatBillV3AcquireHandlerTest {

  @InjectMocks
  private WechatBillV3AcquireHandler handler;

  @Mock
  private AppSecretService appSecretService;

  @Mock
  private WechatBillConfig wechatBillConfig;

  @Mock
  private WechatV3MerchantAppProperties properties;

  private BillTaskEntity task;

  @Mock
  private WechatQueryBillResponse wechatQueryBillResponse;

  @Mock
  private WechatQueryBillRequest wechatQueryBillRequest;


  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    task = new BillTaskEntity();
    task.setChannel(Channel.WECHAT);
    task.setGrantType(GrantType.MERCHANT);
    task.setBillType(BillType.TRADE_FLOW);
    task.setMerchantId("test_merchant");
    task.setBrand("test_brand");
    task.setExtraParams("test_brand");
  }


  @Test
  void givenValidRequestAndHttpClient_whenApplyBillDownloadUrl_thenReturnResponse() throws Exception {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class)) {
      when(wechatBillConfig.getWechatBillHost()).thenReturn("https://api.mch.weixin.qq.com");
      when(wechatBillConfig.getWechatTradeBillUrlV3()).thenReturn("/v3/bill/tradebill");

      CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
      mockedUtils.when(() -> WechatPayUtils.createHttpClient(null, null, null)).thenReturn(httpClient);

      CloseableHttpResponse response = mock(CloseableHttpResponse.class);
      HttpEntity entity = mock(HttpEntity.class);

      String responseBody = "{\"hash_type\": \"SHA1\", \"hash_value\": \"test_hash_value\", \"download_url\": \"http://test-download-url\"}";
      InputStream inputStream = new ByteArrayInputStream(responseBody.getBytes());
      when(entity.getContent()).thenReturn(inputStream);
      when(entity.getContentLength()).thenReturn((long) responseBody.length());

      Header contentTypeHeader = mock(Header.class);
      when(contentTypeHeader.getElements()).thenReturn((new HeaderElement[0]));
      when(entity.getContentType()).thenReturn(contentTypeHeader);

      when(response.getEntity()).thenReturn(entity);
      when(response.getStatusLine()).thenReturn(new org.apache.http.message.BasicStatusLine(HttpVersion.HTTP_1_1, HttpStatus.SC_OK, "OK"));
      mockedUtils.when(() -> WechatPayUtils.executeWithRetry(any(), any(HttpGet.class), eq(httpClient))).thenReturn(response);

      when(wechatQueryBillRequest.getMchId()).thenReturn("test_mch_id");
      when(wechatQueryBillRequest.getWechatCertificateSerialNo()).thenReturn("test_certificate_serial_no");
      when(wechatQueryBillRequest.getWechatPrivateKey()).thenReturn("test_private_key");
      when(wechatQueryBillRequest.getBillDate()).thenReturn("20270901");

      when(WechatPayUtils.fromJson(anyString(), eq(WechatQueryBillResponse.class))).thenReturn(wechatQueryBillResponse);
      WechatQueryBillResponse wechatQueryBillResponse1 = handler.applyBillDownloadUrl(wechatQueryBillRequest, httpClient, BillType.TRADE_FLOW);

      assertNotNull(wechatQueryBillResponse1);
    }
  }

  @Test
  void givenDownloadUrlAndFile_whenDownloadBillFile_thenFileIsWritten() throws Exception {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class)) {
      when(wechatQueryBillRequest.getMchId()).thenReturn("test_mch_id");
      when(wechatQueryBillRequest.getWechatCertificateSerialNo()).thenReturn("test_certificate_serial_no");
      when(wechatQueryBillRequest.getWechatPrivateKey()).thenReturn("test_private_key");

      String downloadUrl = "http://test-download-url?token=xxx";
      File billFile = File.createTempFile("test-bill", ".gz");
      billFile.deleteOnExit();

      CloseableHttpClient httpClient = mock(CloseableHttpClient.class);

      CloseableHttpResponse response = mock(CloseableHttpResponse.class);
      HttpEntity entity = mock(HttpEntity.class);
      InputStream inputStream = new InputStream() {
        private final byte[] msg = "Hello World".getBytes();
        private int index = 0;

        @Override
        public int read() {
          if (index >= msg.length) {
            return -1;
          }
          return msg[index++];
        }
      };
      when(entity.getContent()).thenReturn(inputStream);
      when(response.getEntity()).thenReturn(entity);
      when(response.getStatusLine()).thenReturn(new org.apache.http.message.BasicStatusLine(HttpVersion.HTTP_1_1, HttpStatus.SC_OK, "OK"));
      mockedUtils.when(() -> WechatPayUtils.executeWithRetry(any(), any(HttpGet.class), eq(httpClient))).thenReturn(response);

      handler.downloadBillFile(wechatQueryBillRequest, downloadUrl, billFile, httpClient);
      assertNotNull(wechatQueryBillRequest);
    }
  }

  @Test
  void givenValidParams_whenAcquireTo_thenShouldReturnExtension() {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class)) {
      when(properties.getApiPrivateKey()).thenReturn("test_api_private_key");
      when(properties.getApiCertificateSerialNo()).thenReturn("test_api_certificate_serial_no");

      when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV3MerchantAppProperties.class)))
          .thenReturn(properties);

      File targetFile = mock(File.class);

      // mock CloseableHttpClient
      CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
      mockedUtils.when(() -> WechatPayUtils.createHttpClient(null, null, null)).thenReturn(httpClient);

      // mock applyBillDownloadUrl
      wechatQueryBillResponse.setDownloadUrl("test_download_url");
      try {
        mockedUtils.when(() -> handler.applyBillDownloadUrl(wechatQueryBillRequest, eq(httpClient), BillType.TRADE_FLOW))
            .thenReturn(wechatQueryBillResponse);
      } catch (Exception e) {
        e.printStackTrace();
      }

      // mock downloadBillFile
      try {
        mockedUtils.when(
                () -> handler.downloadBillFile(wechatQueryBillRequest, wechatQueryBillResponse.getDownloadUrl(), eq(targetFile), eq(httpClient)))
            .thenReturn(null);
      } catch (Exception e) {
        e.printStackTrace();
      }
      String result = null;
      try {
        result = handler.acquireTo(task, targetFile);
      } catch (Exception e) {
        e.printStackTrace();
      }
      assertNull(result);
    }
  }

  @Test
  void givenCorrectParams_whenMatches_thenShouldReturnTrue() {
    boolean result = handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.TRADE_FLOW, "v3");
    assertTrue(result);
  }

  @Test
  void givenWrongChannel_whenMatches_thenShouldReturnFalse() {
    boolean result = handler.matches(Channel.ALIPAY, GrantType.MERCHANT, BillType.TRADE_FLOW, "v3");
    assertFalse(result);
  }


  @Test
  void givenNullAppSecretConfig_whenAcquireTo_thenShouldThrowException() {
    when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV3MerchantAppProperties.class)))
        .thenReturn(null);
    File targetFile = mock(File.class);
    assertThrows(
        BillCenterBusinessException.class,
        () -> handler.acquireTo(task, targetFile),
        "Expected acquireTo() to throw BillCenterBusinessException when appSecret is null"
    );
  }

  @Test
  void givenExceptionInAcquireWechatV3TradeBill_whenAcquireTo_thenShouldThrowException() {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class)) {
      when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV3MerchantAppProperties.class)))
          .thenReturn(new WechatV3MerchantAppProperties());

      File targetFile = mock(File.class);
      mockedUtils.when(() -> WechatPayUtils.createHttpClient(null, null, null)).thenThrow(new RuntimeException());

      assertThrows(
          BillCenterBusinessException.class,
          () -> handler.acquireTo(task, targetFile),
          "Expected acquireTo() to throw BillCenterBusinessException when an exception occurs in acquireWechatV3TradeBill"
      );
    }
  }
}
