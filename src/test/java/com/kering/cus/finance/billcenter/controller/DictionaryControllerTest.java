package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.DictionaryService;
import com.kering.cus.finance.billcenter.vo.CommonBusinessVO;
import com.kering.cus.finance.billcenter.vo.DictionaryVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DictionaryControllerTest {

    @Mock
    private DictionaryService dictionaryService;

    @InjectMocks
    private DictionaryController dictionaryController;

    @Test
    void givenValidDicGroup_whenQueryDictionaryData_thenReturnsResponseEntity() {
        // Arrange
        String dicGroup = "bill.brands";
        DictionaryVO vo = new DictionaryVO();
        List<DictionaryVO> voList = Arrays.asList(vo);
        CommonBusinessVO<List<DictionaryVO>> businessVO = CommonBusinessVO.success(voList);

        when(dictionaryService.getDictionaryData(dicGroup)).thenReturn(voList);

        // Act
        ResponseEntity<CommonBusinessVO<List<DictionaryVO>>> result = dictionaryController.queryDictionaryData(dicGroup);

        // Assert
        assertNotNull(result);
        assertEquals(businessVO.getData(), result.getBody().getData());
        verify(dictionaryService, times(1)).getDictionaryData(dicGroup);
    }
}
