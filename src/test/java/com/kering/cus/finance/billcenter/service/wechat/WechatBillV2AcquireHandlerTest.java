package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.kering.cus.finance.billcenter.client.wechat.WechatPayUtils;
import com.kering.cus.finance.billcenter.config.WechatBillConfig;
import com.kering.cus.finance.billcenter.config.WechatV2MerchantAppProperties;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.AppSecretService;
import java.io.File;
import lombok.SneakyThrows;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WechatBillV2AcquireHandlerTest {

  @InjectMocks
  private WechatBillV2AcquireHandler handler;

  @Mock
  private AppSecretService appSecretService;

  @Mock
  private WechatBillConfig wechatBillConfig;

  @Mock
  private WechatV2MerchantAppProperties properties;

  private BillTaskEntity task;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    task = new BillTaskEntity();
    task.setChannel(Channel.WECHAT);
    task.setGrantType(GrantType.MERCHANT);
    task.setBillType(BillType.TRADE_FLOW);
    task.setMerchantId("test_merchant");
    task.setBrand("test_brand");
    task.setExtraParams("test_brand");
  }

  @Test
  void givenCorrectParams_whenMatches_thenShouldReturnTrue() {
    boolean result = handler.matches(Channel.WECHAT, GrantType.MERCHANT, BillType.TRADE_FLOW, "v2");
    assertTrue(result);
  }

  @Test
  void givenWrongChannel_whenMatches_thenShouldReturnFalse() {
    boolean result = handler.matches(Channel.ALIPAY, GrantType.MERCHANT, BillType.TRADE_FLOW, "v2");
    assertFalse(result);
  }

  @SneakyThrows
  @Test
  void givenValidParams_whenAcquireTo_thenShouldReturnExtension() {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class); MockedStatic<EntityUtils> entityUtilsMock = mockStatic(
        EntityUtils.class)) {
      when(properties.getAppId()).thenReturn("test_app_id");
      when(properties.getAppSecret()).thenReturn("test_app_secret");
      when(properties.getApiCertificate()).thenReturn("test_api_certificate");
      when(properties.getApiPrivateKey()).thenReturn("test_api_private_key");

      when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV2MerchantAppProperties.class)))
          .thenReturn(properties);

      File targetFile = File.createTempFile("test-bill", ".gz");
      targetFile.deleteOnExit();

      // mock CloseableHttpClient
      CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
      mockedUtils.when(() -> WechatPayUtils.createHttpClient(anyString(), anyString(), anyString())).thenReturn(httpClient);

      // mock CloseableHttpResponse
      CloseableHttpResponse response = mock(CloseableHttpResponse.class);
      HttpEntity entity = mock(HttpEntity.class);

      when(response.getEntity()).thenReturn(entity);
      when(response.getStatusLine()).thenReturn(new org.apache.http.message.BasicStatusLine(HttpVersion.HTTP_1_1, HttpStatus.SC_OK, "OK"));
      mockedUtils.when(() -> WechatPayUtils.executeWithRetry(any(HttpPost.class), any(), eq(httpClient))).thenReturn(response);

      task.setBrand("test_brand");
      task.setMerchantId("test_merchant_id");
      task.setBillStartTime(java.time.ZonedDateTime.now());
      task.setBillType(com.kering.cus.finance.billcenter.constant.BillType.TRADE_FLOW);

      entityUtilsMock.when(() -> EntityUtils.toString(entity, "UTF-8"))
          .thenReturn("mock response content");
      String extension = handler.acquireTo(task, targetFile);
      assertNotNull(extension);
    }
  }

  @Test
  void givenNullAppSecretConfig_whenAcquireTo_thenShouldThrowException() {
    when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV2MerchantAppProperties.class)))
        .thenReturn(null);
    File targetFile = mock(File.class);
    assertThrows(
        BillCenterBusinessException.class,
        () -> handler.acquireTo(task, targetFile),
        "Expected acquireTo() to throw BillCenterBusinessException when appSecret is null"
    );
  }

  @Test
  void givenExceptionInAcquireWechatV2TradeBill_whenAcquireTo_thenShouldThrowException() {
    try (MockedStatic<WechatPayUtils> mockedUtils = mockStatic(WechatPayUtils.class)) {
      when(appSecretService.getMerchantAppSecret(anyString(), anyString(), anyString(), eq(WechatV2MerchantAppProperties.class)))
          .thenReturn(new WechatV2MerchantAppProperties());

      File targetFile = mock(File.class);
      mockedUtils.when(() -> WechatPayUtils.createHttpClient(anyString(), anyString(), anyString())).thenThrow(new RuntimeException());

      assertThrows(
          BillCenterBusinessException.class,
          () -> handler.acquireTo(task, targetFile),
          "Expected acquireTo() to throw BillCenterBusinessException when an exception occurs in acquireWechatV2TradeBill"
      );
    }
  }
}
