package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.lib.storage.StorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AppStorageServiceTest {

    @Mock
    private BillCenterConfig config;
    @Mock
    private StorageService storageService;

    @InjectMocks
    private AppStorageService appStorageService;

    private static final String TEST_BUCKET = "test-bucket";
    private static final String TEST_PATH = "test/path";
    private static final String TEST_RESULT = "test-result";

    @BeforeEach
    void setUp() {
        when(config.getStorageBucket()).thenReturn(TEST_BUCKET);
    }

    @Test
    void shouldReturnResult_whenWriteStreamSuccess() {
        InputStream mockInput = mock(InputStream.class);
        when(storageService.writeStream(mockInput, TEST_BUCKET, TEST_PATH))
                .thenReturn(TEST_RESULT);

        String result = appStorageService.writeStream(TEST_PATH, mockInput);

        assertEquals(TEST_RESULT, result);
        verify(storageService).writeStream(mockInput, TEST_BUCKET, TEST_PATH);
    }

    @Test
    void shouldReturnInputStream_whenReadStreamSuccess() {
        InputStream mockInput = mock(InputStream.class);
        when(storageService.readStream(TEST_BUCKET, TEST_PATH))
                .thenReturn(mockInput);

        InputStream result = appStorageService.readStream(TEST_PATH);

        assertSame(mockInput, result);
        verify(storageService).readStream(TEST_BUCKET, TEST_PATH);
    }

    @Test
    void shouldReturnNull_whenReadStreamFailed() {
        when(storageService.readStream(TEST_BUCKET, TEST_PATH))
                .thenReturn(null);

        InputStream result = appStorageService.readStream(TEST_PATH);

        assertNull(result);
    }
}