package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.BillAcquireService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JvmBillTaskAcquireEventConsumerTest {

    @Mock
    private BillAcquireService billAcquireService;

    @InjectMocks
    private JvmBillTaskAcquireEventConsumer consumer;

    @BeforeEach
    void setUp() {
        consumer = new JvmBillTaskAcquireEventConsumer(billAcquireService);
    }

    @Test
    void shouldAcquireWhenWaitAcquireState() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, TaskState.WAIT_ACQUIRE);

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billAcquireService).acquireIfNecessary(taskId);
    }

    @Test
    void shouldAcquireWhenAcquireFailedState() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, TaskState.ACQUIRE_FAILED);

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billAcquireService).acquireIfNecessary(taskId);
    }

    @Test
    void shouldNotAcquireWhenOtherState() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, TaskState.WAIT_PROCESS);

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billAcquireService, never()).acquireIfNecessary(taskId);
    }

    @Test
    void shouldNotAcquireWhenNullState() {
        // Arrange
        JvmBillTaskEvent event = new JvmBillTaskEvent(1L, null);

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billAcquireService, never()).acquireIfNecessary(anyLong());
    }

    @Test
    void shouldNotAcquireWhenNullEvent() {
        // Arrange
        JvmBillTaskEvent event = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            consumer.onApplicationEvent(event);
        });
    }
}