package com.kering.cus.finance.billcenter.service.event.local;

import com.kering.cus.finance.billcenter.service.BillProcessService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JvmBillTaskProcessEventConsumerTest {

    @Mock
    private BillProcessService billProcessService;

    @InjectMocks
    private JvmBillTaskProcessEventConsumer consumer;

    @BeforeEach
    void setUp() {
        consumer = new JvmBillTaskProcessEventConsumer(billProcessService);
    }

    @Test
    void givenProcess_whenConsumeEvent_thenProcessesTask() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, "PROCESS");

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billProcessService).processIfNecessary(taskId);
    }

    @Test
    void givenAcquire_whenConsumeEvent_thenDoesNotProcessTask() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, "ACQUIRE");

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billProcessService, never()).processIfNecessary(taskId);
    }

    @Test
    void givenPuthToSap_whenConsumeEvent_thenDoesNotProcessTask() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, "PUSH_TO_SAP");

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billProcessService, never()).processIfNecessary(taskId);
    }

    @Test
    void givenNullState_whenConsumeEvent_thenDoesNotProcessTask() {
        // Arrange
        Long taskId = 1L;
        JvmBillTaskEvent event = new JvmBillTaskEvent(taskId, null);

        // Act
        consumer.onApplicationEvent(event);

        // Assert
        verify(billProcessService, never()).processIfNecessary(anyLong());
    }

    @Test
    void givenNullEvent_whenConsumeEvent_thenThrowsException() {
        // Arrange
        JvmBillTaskEvent event = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            consumer.onApplicationEvent(event);
        });
    }
}