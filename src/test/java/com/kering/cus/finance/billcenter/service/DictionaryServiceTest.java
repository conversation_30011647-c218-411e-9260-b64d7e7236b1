package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.dao.DictionaryDAO;
import com.kering.cus.finance.billcenter.entity.DictionaryEntity;
import com.kering.cus.finance.billcenter.vo.DictionaryVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DictionaryServiceTest {

    @Mock
    private DictionaryDAO dictionaryDAO;

    @InjectMocks
    private DictionaryService dictionaryService;

    @Test
    void givenValidDicGroup_whenGetDictionaryData_thenReturnsList() {
        // Arrange
        String dicGroup = "bill.brands";
        DictionaryEntity entity = new DictionaryEntity();
        entity.setDicKey("key");
        entity.setDicValue("value");
        entity.setIsEnabled(true);
        List<DictionaryEntity> entities = Arrays.asList(entity);

        when(dictionaryDAO.findByDicGroup(dicGroup)).thenReturn(entities);

        // Act
        List<DictionaryVO> result = dictionaryService.getDictionaryData(dicGroup);

        // Assert
        assertNotNull(result);
        assertEquals(entities.size(), result.size());
        verify(dictionaryDAO, times(1)).findByDicGroup(dicGroup);
    }

    @Test
    void givenEmptyDicGroup_whenGetDictionaryData_thenThrowsIllegalArgumentException() {
        // Arrange
        String dicGroup = "";

        // Act and Assert
        assertThrows(IllegalArgumentException.class, () -> dictionaryService.getDictionaryData(dicGroup));
        verify(dictionaryDAO, never()).findByDicGroup(anyString());
    }

    @Test
    void givenInvalidDicGroup_whenGetDictionaryData_thenThrowsIllegalArgumentException() {
        // Arrange
        String dicGroup = "invalid.group";

        // Act and Assert
        assertThrows(IllegalArgumentException.class, () -> dictionaryService.getDictionaryData(dicGroup));
        verify(dictionaryDAO, never()).findByDicGroup(anyString());
    }
}
