package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.service.BillProcessService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DistributedBillTaskProcessEventConsumerTest {

    @Mock
    private BillProcessService billProcessService;

    @InjectMocks
    private DistributedBillTaskProcessEventConsumer distributedBillTaskProcessEventConsumer;

    @BeforeEach
    void setUp() {
        distributedBillTaskProcessEventConsumer = new DistributedBillTaskProcessEventConsumer(billProcessService);
    }

    @Test
    void givenProcess_whenConsumeEvent_thenCallsProcess() {
        // Arrange
        Long taskId = 1L;
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskType()).thenReturn("PROCESS");
        when(event.getTaskId()).thenReturn(taskId);

        // Act
        distributedBillTaskProcessEventConsumer.consume(event);

        // Assert
        verify(billProcessService).processIfNecessary(taskId);
    }

    @Test
    void givenNullEvent_whenConsumeEvent_thenThrowsException() {
        // Arrange
        DistributedBillTaskEvent event = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            distributedBillTaskProcessEventConsumer.consume(event);
        });
    }

    @Test
    void givenNullState_whenConsumeEvent_thenDoesNotCallProcess() {
        // Arrange
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskType()).thenReturn(null);

        // Act
        distributedBillTaskProcessEventConsumer.consume(event);

        // Assert
        verify(billProcessService, never()).processIfNecessary(anyLong());
    }
}