package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.AbstractApiBillTaskCreateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ApiBillTaskCreateProxyServiceTest {
    @Mock
    private BillTaskEventPublisher billTaskEventPublisher;

    @Mock
    private List<AbstractApiBillTaskCreateService> billTaskCreateServices;

    @InjectMocks
    private ApiBillTaskCreateProxyService apiBillTaskCreateProxyService;

    @BeforeEach
    void setUp() {
        // 初始化测试服务
    }

    @Test
    void givenValidChannel_whenCreateTask_thenCreatesSuccessfully() {
        // Arrange
        AbstractApiBillTaskCreateService mockService = mock(AbstractApiBillTaskCreateService.class);
        when(mockService.matches(Channel.JD)).thenReturn(true);
        when(mockService.createTaskIfNecessary(any(ZonedDateTime.class), anyString()))
                .thenReturn(Collections.singletonList(1L));

        when(billTaskCreateServices.stream().filter(service -> service.matches(Channel.JD)))
                .thenReturn(Stream.of(mockService));

        // Act
        boolean result = apiBillTaskCreateProxyService.createTaskIfNecessary(Channel.JD, ZonedDateTime.now(), "test");

        // Assert
        assertTrue(result);
        verify(mockService).createTaskIfNecessary(any(ZonedDateTime.class), anyString());
        verify(billTaskEventPublisher).publishWaitAcquireEvent(anyList());
    }

    @Test
    void givenValidRequest_whenCreateTask_thenCreatesSuccessfully() {
        // Arrange
        AbstractApiBillTaskCreateService mockService = mock(AbstractApiBillTaskCreateService.class);
        when(mockService.matches(Channel.JD)).thenReturn(true);
        when(mockService.createTaskIfNecessary(anyString(), any(), any(ZonedDateTime.class), anyString()))
                .thenReturn(Collections.singletonList(1L));

        when(billTaskCreateServices.stream().filter(service -> service.matches(Channel.JD)))
                .thenReturn(Stream.of(mockService));

        // Act
        boolean result = apiBillTaskCreateProxyService.createTaskIfNecessary(Channel.JD, "merchant", BillType.TRADE_FLOW, ZonedDateTime.now(), "test");

        // Assert
        assertTrue(result);
        verify(mockService).createTaskIfNecessary(anyString(), any(), any(ZonedDateTime.class), anyString());
        verify(billTaskEventPublisher).publishWaitAcquireEvent(anyList());
    }

    @Test
    void givenJobRequest_whenCreateTask_thenTriggersProcessing() {
        // Arrange
        AbstractApiBillTaskCreateService mockService = mock(AbstractApiBillTaskCreateService.class);
        when(mockService.matches(Channel.JD)).thenReturn(true);
        when(mockService.createTaskIfNecessary(any(ZonedDateTime.class), anyString()))
                .thenReturn(Collections.singletonList(1L));

        when(billTaskCreateServices.stream().filter(service -> service.matches(Channel.JD)))
                .thenReturn(Stream.of(mockService));

        // Act
        apiBillTaskCreateProxyService.createTaskIfNecessary(Channel.JD.name());

        // Assert
        verify(mockService).createTaskIfNecessary(any(ZonedDateTime.class), anyString());
        verify(billTaskEventPublisher).publishWaitAcquireEvent(anyList());
    }

    @Test
    void givenNoServiceFound_whenCreateTask_thenThrowsException() {
        // Arrange
        when(billTaskCreateServices.stream().filter(service -> service.matches(Channel.WOSAIPAY)))
                .thenReturn(Stream.empty());

        // Act & Assert
        try {
            apiBillTaskCreateProxyService.createTaskIfNecessary(Channel.WOSAIPAY, ZonedDateTime.now(), "test");
        } catch (UnsupportedOperationException e) {
            assertTrue(e.getMessage().contains("No service found"));
        }
    }

}