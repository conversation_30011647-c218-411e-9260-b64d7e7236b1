package com.kering.cus.finance.billcenter.service;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.support.BlacklineBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import java.io.File;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Path;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BlacklineBillArchiveServiceTest {
    @Mock
    private BillCenterConfig billCenterConfig;
    @Mock
    private BillTaskDAO billTaskDAO;
    @Mock
    private TransitLogDAO transitLogDAO;
    @Mock
    private BillTaskService billTaskService;
    @Mock
    private AppStorageService appStorageService;
    @Mock
    private List<BlacklineBillArchiveHandler> blacklineBillArchiveHandlers;

    @InjectMocks
    private BlacklineBillArchiveService blacklineBillArchiveService;

    private static final String TEST_TRACE_ID = "test-trace-id";
    private static final String TEST_MERCHANT_ID = "test-merchant";
    private static final String TEST_BRAND = "test-brand";
    private static final Long TEST_TASK_ID = 1L;

    private BillTaskEntity createTestBillTaskEntity(SyncState syncState) {
        BillTaskEntity task = new BillTaskEntity();
        task.setId(TEST_TASK_ID);
        task.setTraceId(TEST_TRACE_ID);
        task.setChannel(Channel.ALIPAY);
        task.setMerchantId(TEST_MERCHANT_ID);
        task.setBrand(TEST_BRAND);
        task.setBlacklineSyncState(syncState);
        task.setVersion(1L);
        EntityUtils.fill(task, ZonedDateTime.now());
        return task;
    }

    @Test
    void givenNoPendingTasks_whenArchiveIfNecessary_returnsFalse() {
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(), any(), any()))
                .thenReturn(Collections.emptyList());

        boolean result = blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY);

        assertFalse(result);
        verify(billTaskDAO).findBlacklineTasksByChannelAndCreateTime(any(), any(), any());
    }

    @Test
    void givenRunningTasks_whenArchiveIfNecessary_returnsFalse() {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.RUNNING);
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(Channel.class), any(), any()))
                .thenReturn(Collections.singletonList(task));

        boolean result = blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY);

        assertFalse(result);
        verify(billTaskDAO).findBlacklineTasksByChannelAndCreateTime(any(Channel.class), any(), any());
    }

    @Test
    void givenNoArchiveHandlerFound_whenArchiveIfNecessary_throwsConfigurationException() {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(Channel.class), any(), any()))
                .thenReturn(Collections.singletonList(task));
        when(blacklineBillArchiveHandlers.stream()).thenReturn(Stream.of());

        boolean result = blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY);

        assertFalse(result);
        verify(billTaskDAO).findBlacklineTasksByChannelAndCreateTime(any(Channel.class), any(), any());
    }

    @Test
    void givenArchiveHandlerThrowsException_whenArchiveIfNecessary_handlesError() throws IOException {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(), any(), any()))
                .thenReturn(Collections.singletonList(task));

        BlacklineBillArchiveHandler mockHandler = mock(BlacklineBillArchiveHandler.class);
        when(mockHandler.matches(any())).thenReturn(true);
        when(blacklineBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));
        when(mockHandler.archive(any(), anyLong(), any())).thenThrow(new RuntimeException("archive failed"));

        assertThrows(BillCenterBusinessException.class, () -> 
            blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY)
        );

        verify(billTaskDAO).findBlacklineTasksByChannelAndCreateTime(any(), any(), any());
        verify(mockHandler).archive(any(), anyLong(), any());
    }

    @Test
    void givenAllConditionsMet_whenArchiveIfNecessary_processesSuccessfully() throws IOException {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(), any(), any()))
                .thenReturn(Collections.singletonList(task));

        BlacklineBillArchiveHandler mockHandler = mock(BlacklineBillArchiveHandler.class);
        when(mockHandler.matches(any())).thenReturn(true);
        when(blacklineBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));

        Path tmp = FileUtils2.createTempFile("tmp", ".tmp");
        try {
            Map<String, File> filenameToFile = Map.of(".csv", tmp.toFile());
            when(mockHandler.archive(any(), anyLong(), any())).thenReturn(filenameToFile);

            boolean result = blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY);

            assertTrue(result);
            verify(billTaskDAO).findBlacklineTasksByChannelAndCreateTime(any(), any(), any());
            verify(mockHandler).archive(any(), anyLong(), any());
            verify(mockHandler).sendToBlackline(any());
        } finally {
            FileUtils.deleteQuietly(tmp.toFile());
        }
    }

    @Test
    void givenMultipleGroups_whenArchiveIfNecessary_handlesGrouping() throws IOException {
        BillTaskEntity tmallTask = createTestBillTaskEntity(SyncState.WAITING);
        tmallTask.setIsTmall(true);
        BillTaskEntity nonTmallTask = createTestBillTaskEntity(SyncState.WAITING);
        nonTmallTask.setIsTmall(false);
        when(billTaskDAO.findBlacklineTasksByChannelAndCreateTime(any(), any(), any()))
                .thenReturn(Arrays.asList(tmallTask, nonTmallTask));

        BlacklineBillArchiveHandler mockHandler = mock(BlacklineBillArchiveHandler.class);
        when(mockHandler.matches(any())).thenReturn(true);
        when(mockHandler.isGroupingByTmall()).thenReturn(true);
        when(blacklineBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));

        Path tmallFile = FileUtils2.createTempFile("tmall", ".tmp");
        Path nonTmallFile = FileUtils2.createTempFile("nonTmall", ".tmp");
        try {
            Map<String, File> filenameToFile = Map.of(".csv", tmallFile.toFile());
            Map<String, File> notmal = Map.of(".csv", nonTmallFile.toFile());
            when(mockHandler.archive(eq(BlacklineBillArchiveHandler.Group.TMALL), anyLong(), any())).thenReturn(filenameToFile);
            when(mockHandler.archive(eq(BlacklineBillArchiveHandler.Group.NON_TMALL), anyLong(), any())).thenReturn(notmal);

            blacklineBillArchiveService.archiveIfNecessary(Channel.ALIPAY);

            verify(mockHandler).archive(eq(BlacklineBillArchiveHandler.Group.TMALL), anyLong(), any());
            verify(mockHandler).archive(eq(BlacklineBillArchiveHandler.Group.NON_TMALL), anyLong(), any());
            verify(mockHandler, times(2)).sendToBlackline(any());
        } finally {
            FileUtils.deleteQuietly(tmallFile.toFile());
            FileUtils.deleteQuietly(nonTmallFile.toFile());
        }
    }
}