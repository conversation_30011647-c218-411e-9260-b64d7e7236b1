package com.kering.cus.finance.billcenter.service.support;

import com.jcraft.jsch.ChannelSftp;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class AbstractSftpAcquireHandlerTest {

    @Mock
    private SftpConfig sftpConfig;
    @Mock
    private ChannelSftp mockChannelSftp;

    private TestSftpAcquireHandler handler;

    @BeforeEach
    void setUp() {
        handler = new TestSftpAcquireHandler(sftpConfig, Channel.JD, BillType.FUND_FLOW);
    }

    @Test
    void shouldReturnTrueWhenMatchesValidParameters() {
        // Act & Assert
        assertTrue(handler.matches(Channel.JD, GrantType.SFTP, BillType.FUND_FLOW, "1.0"));
    }

    @Test
    void shouldReturnFalseWhenChannelNotMatch() {
        // Act & Assert
        assertFalse(handler.matches(Channel.WOSAIPAY, GrantType.SFTP, BillType.FUND_FLOW, "1.0"));
    }

    @Test
    void shouldReturnFalseWhenBillTypeNotMatch() {
        // Act & Assert
        assertFalse(handler.matches(Channel.JD, GrantType.SFTP, BillType.TRADE_FLOW, "1.0"));
    }

    @Test
    void shouldReturnFalseWhenGrantTypeNotMatch() {
        // Act & Assert
        assertFalse(handler.matches(Channel.JD, GrantType.ISV, BillType.FUND_FLOW, "1.0"));
    }

    @Test
    void shouldReturnSuffixWhenMatch() throws IOException {
        try (final MockedStatic<SftpUtils> ms = mockStatic(SftpUtils.class)) {
            ms.when(() -> SftpUtils.connect(any(), anyInt(), any(), any(), any(), any(), anyInt())).thenReturn(mockChannelSftp);
            ms.when(() -> SftpUtils.mkdirs(any(), any())).thenReturn(true);
            // Act & Assert
            final BillTaskEntity task = BillTaskEntity.builder()
                    .extraParams("/upload.csv")
                    .build();
            final File tempFile = File.createTempFile(BillType.FUND_FLOW.name(), ".tmp");
            tempFile.deleteOnExit();
            try {
                final String extension = handler.acquireTo(task, tempFile);
                assertFalse(handler.matches(Channel.JD, GrantType.ISV, BillType.FUND_FLOW, "1.0"));
                assertEquals("csv", extension);
            } finally {
                tempFile.delete();
            }
        }
    }

    static class TestSftpAcquireHandler extends AbstractSftpAcquireHandler {
        TestSftpAcquireHandler(SftpConfig sftpConfig, Channel channel, BillType billType) {
            super(sftpConfig, channel, billType);
        }

        @Override
        protected String determineAcquiredSftpPath(String sftpPath) {
            return "/upload/bak/" + System.currentTimeMillis();
        }
    }

}