package com.kering.cus.finance.billcenter.service.wosaipay;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.WosaipayTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.WosaipayTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import java.nio.file.Files;
import java.util.Map;
import java.util.Map.Entry;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.util.CollectionUtils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WosaipayBlacklineBillArchiveHandlerTest {


    @InjectMocks
    private WosaipayBlacklineBillArchiveHandler handler;

    @Mock
    private WosaipayTradeFlowDAO wosaipayTradeFlowDAO;


    private List<Long> taskIds;

    @BeforeEach
    void setUp() {
        taskIds = Arrays.asList(1L, 2L, 3L);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenWosaipayChannel_whenMatches_thenReturnsTrue() {
        boolean result = handler.matches(Channel.WOSAIPAY);
        assertTrue(result);
    }

    @Test
    void givenNonWosaipayChannel_whenMatches_thenReturnsFalse() {
        boolean result = handler.matches(Channel.ALIPAY);
        assertFalse(result);
    }

    @Test
    void givenAnyInput_whenIsGroupingByTmall_thenReturnsFalse() {
        boolean result = handler.isGroupingByTmall();
        assertFalse(result);
    }


    @Test
    void givenTaskIdsWithNoData_whenArchive_thenReturnsNullAndDeletesFile() throws IOException {
        when(wosaipayTradeFlowDAO.findWosaipayBillByTaskIds(anyList(), anyInt(), anyInt()))
                .thenReturn(new Page<>());


        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(org.apache.commons.io.FileUtils.class)) {
            Map<String, File> result = handler.archive(WosaipayBlacklineBillArchiveHandler.Group.NON_TMALL, 1L, taskIds);

            assertTrue(CollectionUtils.isEmpty(result));
        }
    }

    @Test
    void givenTaskIdsWithData_whenArchive_thenCreatesCsvFile() throws IOException {
        Page<WosaipayTradeFlowEntity> mockData = new Page<>();
        mockData.setRecords(createMockWosaipayTradeFlowEntities(2));
        when(wosaipayTradeFlowDAO.findWosaipayBillByTaskIds(anyList(), anyInt(), eq(1))).thenReturn(mockData);

        Map<String, File> result = handler.archive(WosaipayBlacklineBillArchiveHandler.Group.NON_TMALL, 1L, taskIds);

        assertNotNull(result);

        for (Entry<String, File> entry : result.entrySet()) {
            File value = entry.getValue();
            Files.deleteIfExists(value.toPath());
        }
    }

    @Test
    void givenTaskIdsWithMultiplePages_whenArchive_thenProcessesAllPages() throws IOException {
        Page<WosaipayTradeFlowEntity> mockData = new Page<>();
        mockData.setRecords(createMockWosaipayTradeFlowEntities(1000));
        when(wosaipayTradeFlowDAO.findWosaipayBillByTaskIds(anyList(), anyInt(), eq(1))).thenReturn(mockData);


        Map<String, File> result = handler.archive(WosaipayBlacklineBillArchiveHandler.Group.NON_TMALL, 1L, taskIds);
        assertNotNull(result);

        for (Entry<String, File> entry : result.entrySet()) {
            File value = entry.getValue();
            Files.deleteIfExists(value.toPath());
        }
    }

    @Test
    void givenExceptionInDao_whenArchive_thenThrowsIOException() {
        when(wosaipayTradeFlowDAO.findWosaipayBillByTaskIds(anyList(), anyInt(), anyInt()))
                .thenThrow(new IllegalArgumentException("Database error"));

        try (MockedStatic<org.apache.commons.io.FileUtils> mockedFileUtils = mockStatic(org.apache.commons.io.FileUtils.class)) {
            assertThrows(BillCenterBusinessException.class, () -> handler.archive(WosaipayBlacklineBillArchiveHandler.Group.NON_TMALL, 1L, taskIds));

        }
    }

    private List<WosaipayTradeFlowEntity> createMockWosaipayTradeFlowEntities(int count) {
        List<WosaipayTradeFlowEntity> entities = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            WosaipayTradeFlowEntity entity = new WosaipayTradeFlowEntity();
            entity.setBrand("品牌" + i);
            entity.setChannel("WOSAIPAY");
            entity.setMerchantId("M123456");
            entity.setMerchantName("商户名" + i);
            entity.setStoreCode("门店号" + i);
            entity.setStoreName("门店名" + i);
            entity.setTransactionDate("2025-01-01");
            entity.setTransactionTime("10:30:00");
            entity.setMerchantOrderId("订单号" + i);
            entity.setTransactionAmount(new BigDecimal("100.00"));
            entity.setServiceCharge(new BigDecimal("2.00"));
            entities.add(entity);
        }
        return entities;
    }



}
