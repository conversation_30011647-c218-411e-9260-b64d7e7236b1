package com.kering.cus.finance.billcenter.service.support;

import com.jcraft.jsch.ChannelSftp;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.util.DateUtil;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import com.kering.cus.finance.billcenter.util.SftpUtils;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AbstractSapBillArchiveHandlerTest {

    @Mock
    private SftpConfig sftpConfig;

    @InjectMocks
    private TestSapBillArchiveHandler testHandler;

    private MerchantConfigEntity merchantConfig;

    @BeforeEach
    void setUp() {
        merchantConfig = new MerchantConfigEntity();
        merchantConfig.setSapMerchantCode("SAP123");
        merchantConfig.setChannel(Channel.ALIPAY);
    }

    @Test
    void givenValidConfigAndFile_whenSendToSap_thenVerifySftpOperations() throws Exception {
        // Arrange
        File testFile = FileUtils2.createTempFile("testfile", ".csv").toFile();
        try (MockedStatic<SftpUtils> mockedSftpUtils = mockStatic(SftpUtils.class)) {
            ChannelSftp mockChannel = mock(ChannelSftp.class);
            mockedSftpUtils.when(() -> SftpUtils.connect(
                    eq("test-host"),
                    eq(22),
                    eq("test-user"),
                    eq("test-pass"),
                    isNull(),
                    isNull(),
                    eq(10000)
            )).thenReturn(mockChannel);
            mockedSftpUtils.when(() -> SftpUtils.mkdirs(any(), any())).thenReturn(true);

            when(sftpConfig.getSftpConnectTimeout()).thenReturn(10000);

            // Act
            final String filename = "CNCASHSAP123ALIPAY01_" + ZonedDateTime.now().minusDays(1).format(DateUtil.DATE_FORMATTER_FIRST) + ".csv";
            testHandler.sendToSap(filename, testFile, merchantConfig);

            // Assert
            mockedSftpUtils.verify(() -> SftpUtils.connect(
                    "test-host", 22, "test-user", "test-pass", null, null, 10000
            ));
            mockedSftpUtils.verify(() -> SftpUtils.mkdirs(mockChannel, "/archive"));
            verify(mockChannel, times(1)).put(any(InputStream.class), eq("/archive/" + filename));
            mockedSftpUtils.verify(() -> SftpUtils.disconnect(mockChannel));
        } finally {
            FileUtils.deleteQuietly(testFile);
        }
    }

    @Test
    void givenInvalidDirectory_whenSendToSap_thenThrowIOException() throws Exception {
        // Arrange
        File testFile = FileUtils2.createTempFile("testfile", ".csv").toFile();
        try (MockedStatic<SftpUtils> mockedSftpUtils = mockStatic(SftpUtils.class)) {
            ChannelSftp mockChannel = mock(ChannelSftp.class);
            mockedSftpUtils.when(() -> SftpUtils.connect(any(), anyInt(), anyString(), anyString(), isNull(), isNull(), anyInt()))
                    .thenReturn(mockChannel);
            when(sftpConfig.getSftpConnectTimeout()).thenReturn(10000);

            // 模拟目录创建失败
            mockedSftpUtils.when(() -> SftpUtils.mkdirs(eq(mockChannel), anyString())).thenReturn(false);

            // Act & Assert
            IOException exception = assertThrows(IOException.class, () -> {
                testHandler.sendToSap("filename", testFile, merchantConfig);
            });

            // Verify
            assertTrue(exception.getMessage().contains("Failed to mkdirs"));
            mockedSftpUtils.verify(() -> SftpUtils.disconnect(mockChannel));
        } finally {
            FileUtils.deleteQuietly(testFile);
        }
    }

    @Test
    void givenNullFile_whenSendToSap_thenThrowIllegalArgumentException() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            testHandler.sendToSap("filename", null, merchantConfig);
        });

        // Verify
        assertEquals("targetFile cannot be null", exception.getMessage());
    }

    @Test
    void givenDifferentChannelAndBillType_whenMatches_thenReturnCorrectResult() {
        // Arrange
        Channel testChannel = Channel.ALIPAY;
        BillType testBillType = BillType.TRADE_FLOW;
        TestSapBillArchiveHandler handler = new TestSapBillArchiveHandler(testChannel, testBillType, sftpConfig);

        // Act & Assert
        assertTrue(handler.matches(testChannel, testBillType));
        assertFalse(handler.matches(Channel.WECHAT, testBillType));
        assertFalse(handler.matches(testChannel, BillType.FUND_FLOW));
    }

    // 内部测试类实现抽象方法
    static class TestSapBillArchiveHandler extends AbstractSapBillArchiveHandler {
        public TestSapBillArchiveHandler(Channel channel, BillType billType, SftpConfig sftpConfig) {
            super(channel, billType, sftpConfig);
        }

        @Override
        protected String getSapSftpHostname() {
            return "test-host";
        }

        @Override
        protected int getSapSftpPort() {
            return 22;
        }

        @Override
        protected String getSapSftpUsername() {
            return "test-user";
        }

        @Override
        protected String getSapSftpPassword() {
            return "test-pass";
        }

        @Override
        protected String getSapSftpTargetDirectory() {
            return "/archive";
        }


        @Override
        public Map<String, File> archive(Long taskId, MerchantConfigEntity config) throws IOException {
            return Map.of();
        }
    }
}