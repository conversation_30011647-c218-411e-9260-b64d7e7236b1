
package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DateUtilTest {

    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    private static final ZoneId UTC_ZONE_ID = ZoneId.of("UTC");

    private static final String VALID_DATE_TIME_STR = "2025-07-08 15:03:00";
    private static final ZonedDateTime VALID_ZONED_DATE_TIME = ZonedDateTime.of(
            2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);

    @Test
    void givenValidZonedDateTimeAndFormatter_whenFormat_thenReturnsFormattedString() {
        String result = DateUtil.format(VALID_ZONED_DATE_TIME, DateUtil.DEFAULT_DATETIME_FORMATTER);
        assertEquals(VALID_DATE_TIME_STR, result);
    }


    @Test
    void givenValidZonedDateTime_whenFormatDefault_thenReturnsDefaultFormattedString() {
        String result = DateUtil.formatDefault(VALID_ZONED_DATE_TIME);
        assertEquals(VALID_DATE_TIME_STR, result);
    }


    @Test
    void givenValidDateTimeStringFormatterAndZoneId_whenParse_thenReturnsParsedZonedDateTime() {
        ZonedDateTime result = DateUtil.parse(VALID_DATE_TIME_STR, DateUtil.DEFAULT_DATETIME_FORMATTER, DEFAULT_ZONE_ID);
        assertEquals(VALID_ZONED_DATE_TIME, result);
    }

    @Test
    void givenEmptyTimeString_whenParse_thenReturnsNull() {
        ZonedDateTime result = DateUtil.parse("", DateUtil.DEFAULT_DATETIME_FORMATTER, DEFAULT_ZONE_ID);
        assertNull(result);
    }

    @Test
    void givenNullFormatter_whenParse_thenReturnsNull() {
        ZonedDateTime result = DateUtil.parse(VALID_DATE_TIME_STR, null, DEFAULT_ZONE_ID);
        assertNull(result);
    }

    @Test
    void givenNullZoneId_whenParse_thenReturnsNull() {
        ZonedDateTime result = DateUtil.parse(VALID_DATE_TIME_STR, DateUtil.DEFAULT_DATETIME_FORMATTER, null);
        assertNull(result);
    }

    @Test
    void givenInvalidDateTimeString_whenParse_thenThrowsIllegalArgumentException() {
        assertThrows(IllegalArgumentException.class, () -> {
            DateUtil.parse("invalid-date", DateUtil.DEFAULT_DATETIME_FORMATTER, DEFAULT_ZONE_ID);
        });
    }

    @Test
    void givenValidDateTimeStringAndZoneId_whenParseDefault_thenReturnsParsedZonedDateTime() {
        ZonedDateTime result = DateUtil.parseDefault(VALID_DATE_TIME_STR, DEFAULT_ZONE_ID);
        assertEquals(VALID_ZONED_DATE_TIME, result);
    }


    @Test
    void givenNullZoneId_whenParseDefault_thenReturnsNull() {
        ZonedDateTime result = DateUtil.parseDefault(VALID_DATE_TIME_STR, null);
        assertNull(result);
    }

    @Test
    void givenValidZonedDateTime_whenToDate_thenReturnsConvertedDate() {
        Date result = DateUtil.toDate(VALID_ZONED_DATE_TIME);
        assertNotNull(result);
        ZonedDateTime convertedBack = DateUtil.fromDate(result, DEFAULT_ZONE_ID);
        assertTrue(Math.abs(convertedBack.toInstant().toEpochMilli() -
                VALID_ZONED_DATE_TIME.toInstant().toEpochMilli()) < 1000);
    }


    @Test
    void givenValidDateAndZoneId_whenFromDate_thenReturnsConvertedZonedDateTime() {
        Date date = new Date();
        ZonedDateTime result = DateUtil.fromDate(date, DEFAULT_ZONE_ID);
        assertNotNull(result);
        Date convertedBack = DateUtil.toDate(result);
        assertTrue(Math.abs(convertedBack.getTime() - date.getTime()) < 1000);
    }


    @Test
    void givenValidZonedDateTimeAndTargetZoneId_whenConvertTimeZone_thenReturnsConvertedZonedDateTime() {
        ZonedDateTime utcTime = VALID_ZONED_DATE_TIME.withZoneSameInstant(UTC_ZONE_ID);
        ZonedDateTime result = DateUtil.convertTimeZone(utcTime, DEFAULT_ZONE_ID);
        assertEquals(VALID_ZONED_DATE_TIME.toInstant(), result.toInstant());
        assertEquals(DEFAULT_ZONE_ID, result.getZone());
    }

    @Test
    void whenNow_thenReturnsCurrentZonedDateTime() {
        ZonedDateTime before = ZonedDateTime.now();
        ZonedDateTime result = DateUtil.now();
        ZonedDateTime after = ZonedDateTime.now();

        assertNotNull(result);
        assertTrue(result.isAfter(before) || result.isEqual(before));
        assertTrue(result.isBefore(after) || result.isEqual(after));
    }

    @Test
    void givenValidZoneId_whenNowWithZoneId_thenReturnsCurrentZonedDateTimeInSpecifiedZone() {
        ZonedDateTime before = ZonedDateTime.now(UTC_ZONE_ID);
        ZonedDateTime result = DateUtil.now(UTC_ZONE_ID);
        ZonedDateTime after = ZonedDateTime.now(UTC_ZONE_ID);

        assertNotNull(result);
        assertEquals(UTC_ZONE_ID, result.getZone());
        assertTrue(result.isAfter(before) || result.isEqual(before));
        assertTrue(result.isBefore(after) || result.isEqual(after));
    }


    @Test
    void givenFirstTimeBeforeSecondTime_whenIsBefore_thenReturnsTrue() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertTrue(DateUtil.isBefore(date1, date2));
    }

    @Test
    void givenFirstTimeNotBeforeSecondTime_whenIsBefore_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 17, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isBefore(date1, date2));
    }

    @Test
    void givenNullFirstTime_whenIsBefore_thenReturnsFalse() {
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isBefore(null, date2));
    }

    @Test
    void givenNullSecondTime_whenIsBefore_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isBefore(date1, null));
    }

    @Test
    void givenFirstTimeAfterSecondTime_whenIsAfter_thenReturnsTrue() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 17, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertTrue(DateUtil.isAfter(date1, date2));
    }

    @Test
    void givenFirstTimeNotAfterSecondTime_whenIsAfter_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isAfter(date1, date2));
    }

    @Test
    void givenNullFirstTime_whenIsAfter_thenReturnsFalse() {
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isAfter(null, date2));
    }

    @Test
    void givenNullSecondTime_whenIsAfter_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isAfter(date1, null));
    }

    @Test
    void givenTwoEqualTimes_whenIsEqual_thenReturnsTrue() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        assertTrue(DateUtil.isEqual(date1, date2));
    }

    @Test
    void givenTwoNotEqualTimes_whenIsEqual_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isEqual(date1, date2));
    }

    @Test
    void givenNullFirstTime_whenIsEqual_thenReturnsFalse() {
        ZonedDateTime date2 = ZonedDateTime.of(2025, 7, 8, 16, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isEqual(null, date2));
    }

    @Test
    void givenNullSecondTime_whenIsEqual_thenReturnsFalse() {
        ZonedDateTime date1 = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        assertFalse(DateUtil.isEqual(date1, null));
    }

    @Test
    void givenValidZonedDateTime_whenAtStartOfDay_thenReturnsStartOfDayTime() {
        ZonedDateTime result = DateUtil.atStartOfDay(VALID_ZONED_DATE_TIME);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(8, result.getDayOfMonth());
        assertEquals(0, result.getHour());
        assertEquals(0, result.getMinute());
        assertEquals(0, result.getSecond());
    }


    @Test
    void givenValidZonedDateTime_whenAtEndOfDay_thenReturnsEndOfDayTime() {
        ZonedDateTime result = DateUtil.atEndOfDay(VALID_ZONED_DATE_TIME);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(8, result.getDayOfMonth());
        assertEquals(23, result.getHour());
        assertEquals(59, result.getMinute());
        assertEquals(59, result.getSecond());
        assertEquals(999000000, result.getNano());
    }



    @Test
    void givenFourthFormatDateTimeString_whenParseFlexibleDateTime_thenReturnsParsedZonedDateTime() {
        ZonedDateTime result = DateUtil.parseFlexibleDateTime("20250708150300");
        assertNotNull(result);
        assertEquals(2025, result.getYear());
        assertEquals(7, result.getMonthValue());
        assertEquals(8, result.getDayOfMonth());
        assertEquals(15, result.getHour());
        assertEquals(3, result.getMinute());
        assertEquals(0, result.getSecond());
    }

    @Test
    void givenEmptyDateTimeString_whenParseFlexibleDateTime_thenReturnsNull() {
        ZonedDateTime result = DateUtil.parseFlexibleDateTime("");
        assertNull(result);

        result = DateUtil.parseFlexibleDateTime("   ");
        assertNull(result);
    }

    @Test
    void givenInvalidDateTimeString_whenParseFlexibleDateTime_thenThrowsDateTimeParseException() {
        assertThrows(DateTimeParseException.class, () -> {
            DateUtil.parseFlexibleDateTime("invalid-date-format");
        });
    }
    @Test
    void givenValidZonedDateTime_whenFormatDateTime_thenReturnsFormattedString() {
        ZonedDateTime dateTime = ZonedDateTime.of(2025, 7, 8, 15, 3, 0, 0, DEFAULT_ZONE_ID);
        String result = DateUtil.formatDateTime(dateTime);
        assertEquals("2025-07-08 15:03:00", result);
    }
}