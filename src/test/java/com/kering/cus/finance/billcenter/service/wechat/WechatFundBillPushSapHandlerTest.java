package com.kering.cus.finance.billcenter.service.wechat;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.finance.billcenter.config.SftpConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.WechatFundFlowDAO;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.entity.WechatFundFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.util.DateUtil;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

@ExtendWith(MockitoExtension.class)
class WechatFundBillPushSapHandlerTest {

  @InjectMocks
  private WechatFundBillPushSapHandler handler;

  @Mock
  private WechatFundFlowDAO wechatFundFlowDAO;

  @Mock
  private BillTaskDAO billTaskDAO;

  @Mock
  private SftpConfig sftpConfig;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void givenWECHATAndFUND_FLOW_whenMatches_thenReturnsTrue() {
    assertTrue(handler.matches(Channel.WECHAT, BillType.FUND_FLOW));
    assertFalse(handler.matches(Channel.ALIPAY, BillType.FUND_FLOW));
    assertFalse(handler.matches(Channel.WECHAT, BillType.TRADE_FLOW));
  }

  @Test
  void givenIsSyncToSapFalse_whenArchive_thenReturnsNull() throws IOException {
    MerchantConfigEntity config = new MerchantConfigEntity();
    config.setIsSyncToSap(false);

    Map<String, File> result = handler.archive(1L, config);
    assertTrue(CollectionUtils.isEmpty(result));
  }

  @Test
  void givenNoData_whenArchive_thenReturnsNull() throws IOException {
    MerchantConfigEntity config = new MerchantConfigEntity();
    config.setIsSyncToSap(true);

    when(wechatFundFlowDAO.selectFundBillCount(1L)).thenReturn(0L);

    Map<String, File> result = handler.archive(1L, config);
    assertTrue(CollectionUtils.isEmpty(result));
  }

  @Test
  void givenDataExists_whenArchive_thenCreatesFile() throws IOException {
    MerchantConfigEntity config = new MerchantConfigEntity();
    config.setIsSyncToSap(true);
    config.setSapProfitCenter("PC123");
    config.setSapGlAccount1("GL1");
    config.setSapGlAccount2("GL2");

    when(wechatFundFlowDAO.selectFundBillCount(1L)).thenReturn(1L);

    Page<WechatFundFlowEntity> page = new Page<>();
    List<WechatFundFlowEntity> records = new ArrayList<>();
    WechatFundFlowEntity entity = new WechatFundFlowEntity();
    entity.setBrand("BRAND1");
    entity.setChannel(Channel.WECHAT);
    entity.setMerchantId("M123");
    entity.setAmount(BigDecimal.valueOf(100.00));
    entity.setBalance(BigDecimal.valueOf(200.00));
    records.add(entity);
    page.setRecords(records);

    when(wechatFundFlowDAO.selectFundBillPage(any(Page.class), eq(1L))).thenReturn(page);

    Map<String, File> result = handler.archive(1L, config);
    assertNotNull(result);
    File file = result.get(".csv");
    assertTrue(file.exists());

    Files.deleteIfExists(file.toPath());
    Files.deleteIfExists(result.get(".xlsx").toPath());

  }

  @Test
  void givenArchiveThrowsException_whenArchive_thenThrowsBillCenterBusinessException() {
    MerchantConfigEntity config = new MerchantConfigEntity();
    config.setIsSyncToSap(true);

    when(wechatFundFlowDAO.selectFundBillCount(1L)).thenReturn(1L);
    when(wechatFundFlowDAO.selectFundBillPage(any(Page.class), eq(1L)))
        .thenThrow(new RuntimeException("Test exception"));

    assertThrows(BillCenterBusinessException.class, () -> handler.archive(1L, config));
  }

  @Test
  void givenValidRecord_whenFormatCsvLine_thenReturnsFormattedLine() {
    try (MockedStatic<DateUtil> dateUtilMockedStatic = Mockito.mockStatic(DateUtil.class)) {
      dateUtilMockedStatic.when(() -> DateUtil.formatDefault(any())).thenReturn("2024-01-01");

      WechatFundFlowEntity wechatFundFlowEntity = new WechatFundFlowEntity();
      wechatFundFlowEntity.setBrand("BRAND1");
      wechatFundFlowEntity.setChannel(Channel.WECHAT);
      wechatFundFlowEntity.setMerchantId("M123");
      wechatFundFlowEntity.setAmount(BigDecimal.valueOf(100.00));
      wechatFundFlowEntity.setBalance(BigDecimal.valueOf(200.00));

      MerchantConfigEntity config = new MerchantConfigEntity();
      config.setSapProfitCenter("PC123");
      config.setSapGlAccount1("GL1");
      config.setSapGlAccount2("GL2");

      String line = handler.formatCsvLine(wechatFundFlowEntity, 1, config);
      assertNotNull(line);
      assertTrue(line.contains("1"));
      assertTrue(line.contains("BRAND1"));
      assertTrue(line.contains("WECHAT"));
      assertTrue(line.contains("M123"));
      assertTrue(line.contains("PC123"));
      assertTrue(line.contains("GL1"));
      assertTrue(line.contains("GL2"));
      assertTrue(line.contains("100.00"));
      assertTrue(line.contains("200.00"));
    }
  }
}
