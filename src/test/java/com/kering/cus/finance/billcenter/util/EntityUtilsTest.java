package com.kering.cus.finance.billcenter.util;

import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class EntityUtilsTest {

    private static class TestEntity extends SoftDeleteMyBatisBaseEntity<String> {
        // 测试用实体类
    }

    @Test
    void fill_ShouldSetAllFieldsCorrectly() {
        ZonedDateTime createDate = ZonedDateTime.now();
        TestEntity entity = new TestEntity();
        
        EntityUtils.fill(entity, createDate);
        
        assertEquals(createDate, entity.getCreatedDate());
        assertNotNull(entity.getModifiedDate());
        assertFalse(entity.getDeleted());
        assertEquals(0L, entity.getVersion());
    }
}