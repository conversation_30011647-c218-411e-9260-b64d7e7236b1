package com.kering.cus.finance.billcenter.service;

import com.google.common.collect.Maps;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.SyncState;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.service.event.BillTaskEventPublisher;
import com.kering.cus.finance.billcenter.service.support.SapBillArchiveHandler;
import com.kering.cus.finance.billcenter.util.EntityUtils;
import com.kering.cus.finance.billcenter.util.FileUtils2;
import java.io.File;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Path;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SapBillArchiveServiceTest {
    @Mock
    private BillCenterConfig billCenterConfig;
    @Mock
    private BillTaskEventPublisher eventPublisher;
    @Mock
    private AppStorageService appStorageService;
    @Mock
    private BillTaskDAO billTaskDAO;
    @Mock
    private TransitLogDAO transitLogDAO;
    @Mock
    private MerchantConfigDAO merchantConfigDAO;
    @Mock
    private List<SapBillArchiveHandler> sapBillArchiveHandlers;
    @Mock
    private BillTaskService billTaskService;

    @InjectMocks
    private SapBillArchiveService sapBillArchiveService;

    private static final String TEST_TRACE_ID = "test-trace-id";
    private static final String TEST_MERCHANT_ID = "test-merchant";
    private static final String TEST_BRAND = "test-brand";
    private static final Long TEST_TASK_ID = 1L;

    private BillTaskEntity createTestBillTaskEntity(SyncState syncState) {
        BillTaskEntity task = new BillTaskEntity();
        task.setId(TEST_TASK_ID);
        task.setTraceId(TEST_TRACE_ID);
        task.setChannel(Channel.ALIPAY);
        task.setBillType(BillType.TRADE_FLOW);
        task.setMerchantId(TEST_MERCHANT_ID);
        task.setBrand(TEST_BRAND);
        task.setSapSyncState(syncState);
        task.setVersion(1L);
        EntityUtils.fill(task, ZonedDateTime.now());
        return task;
    }

    @Test
    void givenTaskNotFound_whenArchiveIfNecessary_thenReturnsFalse() {
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.empty());

        boolean result = sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
    }

    @Test
    void givenInvalidSyncState_whenArchiveIfNecessary_thenReturnsFalse() {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.RUNNING);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));

        boolean result = sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
    }

    @Test
    void givenNoArchiveHandlerFound_whenArchiveIfNecessary_thenThrowsConfigurationException() {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any())).thenReturn(1);
        when(sapBillArchiveHandlers.stream()).thenReturn(Stream.of());

        boolean result = sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID);

        assertFalse(result);
        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any());
    }

    @Test
    void givenNoMerchantConfigFound_whenArchiveIfNecessary_thenThrowsConfigurationException() {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any())).thenReturn(1);

        SapBillArchiveHandler mockHandler = mock(SapBillArchiveHandler.class);
        when(mockHandler.matches(any(), any())).thenReturn(true);
        when(sapBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));

        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(null);

        boolean result = sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID);

        assertFalse(result);

        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any());
        verify(merchantConfigDAO).findByChannelAndMerchantId(any(), any());
    }

    @Test
    void givenArchiveHandlerThrowsException_whenArchiveIfNecessary_thenProcessFailed() throws IOException {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any())).thenReturn(1);

        SapBillArchiveHandler mockHandler = mock(SapBillArchiveHandler.class);
        when(mockHandler.matches(any(), any())).thenReturn(true);
        when(sapBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));

        MerchantConfigEntity merchant = new MerchantConfigEntity();
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

        when(mockHandler.archive(any(), any())).thenThrow(new RuntimeException("archive failed"));

        assertThrows(BillCenterBusinessException.class, () -> sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID));

        verify(billTaskDAO).findById(TEST_TASK_ID);
        verify(billTaskDAO).updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any());
        verify(merchantConfigDAO).findByChannelAndMerchantId(any(), any());
        verify(mockHandler).archive(any(), any());
    }

    @Test
    void givenAllConditionsMet_whenArchiveIfNecessary_thenProcessSuccessfully() throws IOException {
        BillTaskEntity task = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findById(TEST_TASK_ID)).thenReturn(Optional.of(task));
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any())).thenReturn(1);

        final Path tmp = FileUtils2.createTempFile("tmp", ".tmp");
        try {
            SapBillArchiveHandler mockHandler = mock(SapBillArchiveHandler.class);
            when(mockHandler.matches(any(), any())).thenReturn(true);
            when(sapBillArchiveHandlers.stream()).thenReturn(Stream.of(mockHandler));

            MerchantConfigEntity merchant = new MerchantConfigEntity();
            when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(merchant);

            Map<String, File> map= Maps.newHashMap();
            map.put(".csv", tmp.toFile());

            when(mockHandler.archive(any(), any())).thenReturn(map);

            boolean result = sapBillArchiveService.archiveIfNecessary(TEST_TASK_ID);

            assertTrue(result);
            verify(billTaskDAO).findById(TEST_TASK_ID);
            verify(billTaskDAO).updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any());
            verify(merchantConfigDAO).findByChannelAndMerchantId(any(), any());
            verify(mockHandler).archive(any(), any());
            verify(mockHandler).sendToSap(anyString(), any(), any());
        } finally {
            FileUtils.deleteQuietly(tmp.toFile());
        }
    }

    @Test
    void givenNoPendingTasks_whenArchiveAllIfNecessary_thenDoNothing() {
        when(billTaskDAO.findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of());

        sapBillArchiveService.archiveAllIfNecessary(Channel.ALIPAY);

        verify(billTaskDAO).findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), anyInt());
        verifyNoMoreInteractions(billTaskDAO, eventPublisher);
    }

    @Test
    void givenSingleBatchTasks_whenArchiveAllIfNecessary_thenDispatchAll() {
        BillTaskEntity task1 = createTestBillTaskEntity(SyncState.WAITING);
        BillTaskEntity task2 = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(task1, task2))
                .thenReturn(List.of());
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any()))
                .thenReturn(1);

        sapBillArchiveService.archiveAllIfNecessary(Channel.ALIPAY);

        verify(billTaskDAO, times(2)).updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(
                eq(SyncState.WAITING), any(), any(), any());
        verify(eventPublisher, times(2)).publishSapBillArchiveEvent(anyLong());
    }

    @Test
    void givenMultiBatchTasks_whenArchiveAllIfNecessary_thenDispatchWithinLimit() {
        List<BillTaskEntity> tasks = IntStream.range(0, 1500)
                .mapToObj(i -> createTestBillTaskEntity(SyncState.WAITING))
                .toList();

        when(billTaskDAO.findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(tasks.subList(0, 1000))
                .thenReturn(tasks.subList(1000, 1500))
                .thenReturn(List.of());
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), any(), any()))
                .thenReturn(1);

        sapBillArchiveService.archiveAllIfNecessary(Channel.ALIPAY);

        verify(billTaskDAO, times(2)).findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), eq(1000));
        verify(eventPublisher, times(1500)).publishSapBillArchiveEvent(anyLong());
    }

    @Test
    void givenUpdateFailed_whenArchiveAllIfNecessary_thenSkipTask() {
        BillTaskEntity task1 = createTestBillTaskEntity(SyncState.WAITING);
        BillTaskEntity task2 = createTestBillTaskEntity(SyncState.WAITING);
        when(billTaskDAO.findWaitSyncToSapTasksByChannel(any(), any(), any(), anyInt(), anyInt()))
                .thenReturn(List.of(task1, task2))
                .thenReturn(List.of());
        when(billTaskDAO.updateSapSyncStateAndNextRunTimeByIdAndSyncStatus(any(), any(), eq(task1.getId()), any()))
                .thenReturn(0);

        sapBillArchiveService.archiveAllIfNecessary(Channel.ALIPAY);

        verify(eventPublisher, never()).publishSapBillArchiveEvent(task1.getId());
    }

}
