
package com.kering.cus.finance.billcenter.service.jd;

import com.google.common.collect.Lists;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.JdBillConverter;
import com.kering.cus.finance.billcenter.dao.JdTradeFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdTradeFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import com.kering.cus.finance.billcenter.util.ZipUtils;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class JdTradeBillProcessHandlerTest {

    @InjectMocks
    private JdTradeBillProcessHandler handler;

    @Mock
    private JdBillConverter jdBillConverter;

    @Mock
    private JdTradeFlowDAO jdTradeFlowDAO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenJdChannelAndSftpGrantTypeAndTradeBillType_whenMatchesInvoked_thenReturnsTrue() {
        assertTrue(handler.matches(Channel.JD, GrantType.SFTP, BillType.TRADE_FLOW));
        assertFalse(handler.matches(Channel.ALIPAY, GrantType.SFTP, BillType.TRADE_FLOW));
        assertFalse(handler.matches(Channel.JD, GrantType.ISV, BillType.TRADE_FLOW));
        assertFalse(handler.matches(Channel.JD, GrantType.SFTP, BillType.FUND_FLOW));
    }


    @Test
    void givenCsvParseError_whenProcessInvoked_thenThrowsBillCenterBusinessException() throws Exception {


        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");


        try (MockedStatic<ZipUtils> zipUtilsMockedStatic = mockStatic(ZipUtils.class)) {
            zipUtilsMockedStatic.when(() -> ZipUtils.isZipFile(any(File.class))).thenReturn(false);

            CsvReaderUtils csvReaderUtils = mock(CsvReaderUtils.class);
            doThrow(new IOException("Parse error")).when(csvReaderUtils).forEach(any(Consumer.class));

            Path mockPath1 = mock(Path.class);
            when(mockPath1.getFileName()).thenReturn(Paths.get("140749989002_20250509.csv"));

            try (MockedStatic<CsvReaderUtils> csvReaderUtilsMockedStatic = mockStatic(CsvReaderUtils.class)) {
                csvReaderUtilsMockedStatic.when(CsvReaderUtils::builder).thenAnswer(invocation -> {
                    CsvReaderUtils.Builder builder = mock(CsvReaderUtils.Builder.class);
                    when(builder.headerLine(anyInt())).thenReturn(builder);
                    when(builder.charset(any(Charset.class))).thenReturn(builder);
                    when(builder.build()).thenReturn(csvReaderUtils);
                    return builder;
                });

                assertThrows(BillCenterBusinessException.class, () -> handler.process(mock(File.class), task));
            }
        }
    }


    @Test
    void givenValidZipFile_whenProcessInvoked_thenReturnsTrue() throws Exception {


        when(jdBillConverter.toJdTradeFlowEntity(any(), any())).thenReturn(mock(JdTradeFlowEntity.class));
        when(jdTradeFlowDAO.createBatch(any())).thenReturn(1);
        when(jdTradeFlowDAO.findByMerchantIdAndTransactionNos(anyList(),anyString())).thenReturn(Lists.newArrayList());

        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");

        File mockZipFile = mock(File.class);
        when(mockZipFile.getParent()).thenReturn("/tmp");

        CsvReaderUtils mockCsvReader = mock(CsvReaderUtils.class);
        doNothing().when(mockCsvReader).open(any());
        doAnswer(invocation -> {
            Consumer<Map<String, String>> consumer = invocation.getArgument(0);
            consumer.accept(createMockBillMap());
            return null;
        }).when(mockCsvReader).forEach(any());

        try (MockedStatic<ZipUtils> zipUtilMockedStatic = mockStatic(ZipUtils.class);
             MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
             MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class);
             MockedStatic<StringUtils> stringUtilMockedStatic = mockStatic(StringUtils.class);
             MockedStatic<CsvReaderUtils> mockedStatic = mockStatic(CsvReaderUtils.class)) {

            Path mockPath1 = mock(Path.class);
            when(mockPath1.getFileName()).thenReturn(Paths.get("140749989002_20250509.csv"));

            Path mockPath2 = mock(Path.class);
            when(mockPath2.getFileName()).thenReturn(Paths.get("140749989002_20250509_SUMMARY.txt"));

            mockedFiles.when(() -> Files.walk(any(Path.class))).thenReturn(Stream.of(mockPath1, mockPath2));
            mockedFiles.when(() -> Files.isRegularFile(any(Path.class))).thenReturn(true);

            zipUtilMockedStatic.when(() -> ZipUtils.isZipFile(mockZipFile)).thenReturn(true);
            zipUtilMockedStatic.when(() -> ZipUtils.unzip(any(), any(), any())).thenAnswer(invocation -> null);

            fileUtilsMockedStatic.when(() -> FileUtils.deleteQuietly(any())).thenReturn(true);

            stringUtilMockedStatic.when(() -> StringUtils.trimAndRemoveSpecialChars(anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            stringUtilMockedStatic.when(() -> StringUtils.parseAndFormatMap(any()))
                    .thenAnswer(invocation -> invocation.getArgument(0));


            CsvReaderUtils.Builder mockBuilder = mock(CsvReaderUtils.Builder.class);
            when(mockBuilder.headerLine(anyInt())).thenReturn(mockBuilder);
            when(mockBuilder.charset(any(Charset.class))).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockCsvReader);
            mockedStatic.when(CsvReaderUtils::builder).thenReturn(mockBuilder);

            List<String> process = handler.process(mockZipFile, task);
            assertTrue(CollectionUtils.isEmpty(process));
        }
    }



    @Test
    void givenInvalidZipFile_whenProcessInvoked_thenThrowsBillCenterBusinessException() {
        File tempFile = mock(File.class);
        BillTaskEntity task = mock(BillTaskEntity.class);

        try (MockedStatic<ZipUtils> zipUtilsMockedStatic = mockStatic(ZipUtils.class)) {
            zipUtilsMockedStatic.when(() -> ZipUtils.isZipFile(tempFile)).thenReturn(true);
            zipUtilsMockedStatic.when(() -> ZipUtils.unzip(any(), any())).thenThrow(new IOException("Invalid ZIP"));

            assertThrows(BillCenterBusinessException.class, () -> handler.process(tempFile, task));
        }
    }


    @Test
    void givenValidCsvFile_whenProcessInvoked_thenReturnsTrue() throws Exception {

        when(jdBillConverter.toJdTradeFlowEntity(any(), any())).thenReturn(mock(JdTradeFlowEntity.class));
        when(jdTradeFlowDAO.createBatch(any())).thenReturn(1);
        when(jdTradeFlowDAO.findByMerchantIdAndTransactionNos(anyList(),anyString())).thenReturn(Lists.newArrayList());

        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");

        File file = mock(File.class);
        when(file.getParent()).thenReturn("/tmp");

        CsvReaderUtils mockCsvReader = mock(CsvReaderUtils.class);
        doNothing().when(mockCsvReader).open(any());
        doAnswer(invocation -> {
            Consumer<Map<String, String>> consumer = invocation.getArgument(0);
            consumer.accept(createMockBillMap());
            return null;
        }).when(mockCsvReader).forEach(any());

        try (MockedStatic<ZipUtils> zipUtilMockedStatic = mockStatic(ZipUtils.class);
             MockedStatic<FileUtils> fileUtilsMockedStatic = mockStatic(FileUtils.class);
             MockedStatic<Files> mockedFiles = Mockito.mockStatic(Files.class);
             MockedStatic<StringUtils> stringUtilMockedStatic = mockStatic(StringUtils.class);
             MockedStatic<CsvReaderUtils> mockedStatic = mockStatic(CsvReaderUtils.class)) {

            Path mockPath1 = mock(Path.class);
            when(mockPath1.getFileName()).thenReturn(Paths.get("140749989002_20250509.csv"));


            zipUtilMockedStatic.when(() -> ZipUtils.isZipFile(file)).thenReturn(false);

            fileUtilsMockedStatic.when(() -> FileUtils.deleteQuietly(any())).thenReturn(true);

            stringUtilMockedStatic.when(() -> StringUtils.trimAndRemoveSpecialChars(anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            stringUtilMockedStatic.when(() -> StringUtils.parseAndFormatMap(any()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            CsvReaderUtils.Builder mockBuilder = mock(CsvReaderUtils.Builder.class);
            when(mockBuilder.headerLine(anyInt())).thenReturn(mockBuilder);
            when(mockBuilder.charset(any(Charset.class))).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockCsvReader);
            mockedStatic.when(CsvReaderUtils::builder).thenReturn(mockBuilder);

            List<String> process = handler.process(file, task);
            assertTrue(CollectionUtils.isEmpty(process));
        }
    }




    private Map<String, String> createMockBillMap() {
        Map<String, String> map = new HashMap<>();
        map.put("订单编号", "123456");
        map.put("单据编号", "T200P789012");
        map.put("金额", "100.00");
        map.put("钱包结算备注","2025年6月23日费项");
        return map;
    }

}