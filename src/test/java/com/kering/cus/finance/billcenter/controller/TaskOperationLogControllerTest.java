package com.kering.cus.finance.billcenter.controller;

import com.kering.cus.finance.billcenter.service.TaskOperationLogService;
import com.kering.cus.finance.billcenter.vo.CommonBusinessVO;
import com.kering.cus.finance.billcenter.vo.TaskOperationLogVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskOperationLogControllerTest {

    @Mock
    private TaskOperationLogService taskOperationLogService;

    @InjectMocks
    private TaskOperationLogController taskOperationLogController;

    @Test
    void givenValidTraceId_whenGetTaskOperationLogs_thenReturnsResponseEntity() {
        // Arrange
        String traceId = "123";
        TaskOperationLogVO vo = new TaskOperationLogVO();
        List<TaskOperationLogVO> voList = Arrays.asList(vo);
        CommonBusinessVO<List<TaskOperationLogVO>> businessVO = CommonBusinessVO.success(voList);

        when(taskOperationLogService.getTaskOperationLogs(traceId)).thenReturn(voList);

        // Act
        ResponseEntity<CommonBusinessVO<List<TaskOperationLogVO>>> result = taskOperationLogController.getTaskOperationLogs(traceId);

        // Assert
        assertNotNull(result);
        assertEquals(businessVO.getData(), result.getBody().getData());
        verify(taskOperationLogService, times(1)).getTaskOperationLogs(traceId);
    }
}
