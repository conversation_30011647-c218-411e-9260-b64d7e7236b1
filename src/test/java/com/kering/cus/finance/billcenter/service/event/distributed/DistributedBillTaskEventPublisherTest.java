package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.lib.message.queue.producer.Producer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DistributedBillTaskEventPublisherTest {

    @Mock
    private BillCenterConfig billCenterConfig;

    @Mock
    private Producer<DistributedBillTaskEvent> producer;

    @InjectMocks
    private DistributedBillTaskEventPublisher distributedBillTaskEventPublisher;

    @BeforeEach
    void setUp() {
        distributedBillTaskEventPublisher = new DistributedBillTaskEventPublisher(billCenterConfig, producer);
    }

    @Test
    void shouldPublishWaitAcquireEventToCorrectTopic() {
        // Arrange
        Long taskId = 1L;
        String expectedTopic = "ACQUIRE_TOPIC";
        when(billCenterConfig.getAcquireTaskTopic()).thenReturn("ACQUIRE_TOPIC");

        // Act
        distributedBillTaskEventPublisher.publishWaitAcquireEvent(taskId);

        // Assert
        verify(producer).sendMessage(eq(expectedTopic), any(DistributedBillTaskEvent.class));
    }

    @Test
    void shouldPublishWaitProcessEventToCorrectTopic() {
        // Arrange
        Long taskId = 1L;
        String expectedTopic = "PROCESS_TOPIC";
        when(billCenterConfig.getProcessTaskTopic()).thenReturn("PROCESS_TOPIC");

        // Act
        distributedBillTaskEventPublisher.publishWaitProcessEvent(taskId);

        // Assert
        verify(producer).sendMessage(eq(expectedTopic), any(DistributedBillTaskEvent.class));
    }

    @Test
    void shouldPublishMultipleWaitAcquireEvents() {
        // Arrange
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        String expectedTopic = "ACQUIRE_TOPIC";
        when(billCenterConfig.getAcquireTaskTopic()).thenReturn("ACQUIRE_TOPIC");

        // Act
        distributedBillTaskEventPublisher.publishWaitAcquireEvent(taskIds);

        // Assert
        verify(producer, times(3)).sendMessage(eq(expectedTopic), any(DistributedBillTaskEvent.class));
    }

    @Test
    void shouldPublishMultipleWaitProcessEvents() {
        // Arrange
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        String expectedTopic = "PROCESS_TOPIC";
        when(billCenterConfig.getProcessTaskTopic()).thenReturn("PROCESS_TOPIC");

        // Act
        distributedBillTaskEventPublisher.publishWaitProcessEvent(taskIds);

        // Assert
        verify(producer, times(3)).sendMessage(eq(expectedTopic), any(DistributedBillTaskEvent.class));
    }
}