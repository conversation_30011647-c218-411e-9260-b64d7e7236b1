package com.kering.cus.finance.billcenter.controller;

import com.google.common.collect.Maps;
import com.kering.cus.finance.billcenter.client.oauth2.AuthorizationInfo;
import com.kering.cus.finance.billcenter.config.BillCenterConfig;
import com.kering.cus.finance.billcenter.service.OauthGrantService;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.util.Map;

import static com.kering.cus.finance.billcenter.constant.Channel.ALIPAY;
import static com.kering.cus.finance.billcenter.constant.Channel.WECHAT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OauthControllerTest {

    @InjectMocks
    private OauthController oauthController;

    @Mock
    private OauthGrantService mockOauthGrantService;
    @Mock
    private BillCenterConfig billCenterConfig;

    @BeforeAll
    static void beforeAll() {
        Mockito.clearAllCaches();
        mockStatic(GenericRequestContextHolder.class);
    }

    @Test
    void givenValidAlipayAuthorization_whenRedirectToAuthorizeUri_thenRedirectsToAlipay() {
        //expected
        final String gwUrl = "https://open-api.alipay.com";
        when(mockOauthGrantService.getAuthorizeUrl(eq(ALIPAY), any())).thenReturn(gwUrl);
        when(billCenterConfig.getAlipayCallbackUrl()).thenReturn(gwUrl);

        ResponseEntity<Void> response = oauthController.redirectToAuthorizeUri(ALIPAY.name());

        assertEquals(HttpStatus.TEMPORARY_REDIRECT, response.getStatusCode());
        assertEquals(gwUrl, response.getHeaders().getFirst(HttpHeaders.LOCATION));
    }

    @Test
    void givenInvalidChannel_whenRedirectToAuthorizeUri_thenReturnsBadRequest() {
        //expected
        ResponseEntity<Void> response = oauthController.redirectToAuthorizeUri(WECHAT.name());

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void givenCallbackData_whenCallAlipayAuthorizeCallback_thenReturnOk() throws IOException {
        //expected
        final Map<String, String> callbackParams = Maps.newHashMap();
        final AuthorizationInfo authorizationInfo = new AuthorizationInfo();
        authorizationInfo.setMerchantId("MOCK0156");
        when(mockOauthGrantService.doAuthorizeCallback(ALIPAY, callbackParams)).thenReturn(authorizationInfo);

        ResponseEntity<String> response = oauthController.oauth2Callback(ALIPAY.name(), callbackParams);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void givenInvalidCallbackData_whenCallAlipayAuthorizeCallback_thenReturnBadRequest() throws IOException {
        //expected
        final Map<String, String> callbackParams = Maps.newHashMap();
        when(mockOauthGrantService.doAuthorizeCallback(ALIPAY, callbackParams)).thenReturn(null);

        when(billCenterConfig.getAlipayCallbackUrl()).thenReturn("https://open-api.alipay.com");
        ResponseEntity<String> response = oauthController.oauth2Callback(ALIPAY.name(), callbackParams);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void givenCallbackData_whenCallOtherAuthorizeCallback_thenReturnBadRequest() throws IOException {
        //expected
        final Map<String, String> callbackParams = Maps.newHashMap();
        ResponseEntity<String> response = oauthController.oauth2Callback(WECHAT.name(), callbackParams);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void givenCallbackData_whenHappenException_thenReturnBadRequest() throws IOException {
        //expected
        final Map<String, String> callbackParams = Maps.newHashMap();
        when(mockOauthGrantService.doAuthorizeCallback(any(), eq(callbackParams))).thenThrow(new IllegalStateException());

        when(billCenterConfig.getAlipayCallbackUrl()).thenReturn("https://open-api.alipay.com");
        ResponseEntity<String> response = oauthController.oauth2Callback(ALIPAY.name(), callbackParams);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void whenCallRefreshAllOauth2Token_thenVerifyServiceCalledAndReturnOk() {
        // Given
        doNothing().when(mockOauthGrantService).refreshAccessTokenIfNecessary();

        // When
        ResponseEntity<?> response = oauthController.refreshAllOauth2Token();

        // Then
        verify(mockOauthGrantService, times(1)).refreshAccessTokenIfNecessary();
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void whenCallRefreshSpecificOauth2Token_thenVerifyServiceCalledAndReturnOk() {
        // Given
        Long testId = 123L;
        doNothing().when(mockOauthGrantService).refreshAccessTokenIfNecessary(testId);

        // When
        ResponseEntity<?> response = oauthController.refreshOauth2Token(testId);

        // Then
        verify(mockOauthGrantService, times(1)).refreshAccessTokenIfNecessary(testId);
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}
