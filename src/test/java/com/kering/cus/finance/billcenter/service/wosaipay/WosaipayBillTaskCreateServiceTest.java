package com.kering.cus.finance.billcenter.service.wosaipay;

import com.kering.cus.finance.billcenter.config.WosaipayBillConfig;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WosaipayBillTaskCreateServiceTest {

    @Mock
    private WosaipayBillConfig wosaipayBillConfig;

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantConfigDAO merchantConfigDAO;
    @Mock
    private MerchantConfigEntity mockMerchantConfig;

    @InjectMocks
    private WosaipayBillTaskCreateService wosaipayBillTaskCreateService;

    @BeforeEach
    void setUp() {
        wosaipayBillTaskCreateService = new WosaipayBillTaskCreateService(wosaipayBillConfig, billTaskDAO, transitLogDAO, merchantConfigDAO);
    }

    @Test
    void givenWosaipayChannel_whenMatches_thenReturnTrue() {
        when(wosaipayBillConfig.getTradePathPattern()).thenReturn("/path/to/wosaipay");
        // Act & Assert
        assertTrue(wosaipayBillTaskCreateService.matches("/path/to/wosaipay"));
        assertFalse(wosaipayBillTaskCreateService.matches("/path/xx/"));
    }

    @Test
    void givenInvalidTradePath_whenCreateTask_thenReturnNull() {
        // Arrange
        String sftpPath = "/path/to/file";
        ZonedDateTime when = ZonedDateTime.now();

        when(wosaipayBillConfig.getTradePathPattern()).thenReturn(".*file_no.*");

        // Act
        Long taskId = wosaipayBillTaskCreateService.createTaskIfNecessary(sftpPath, when, "test");

        // Assert
        assertNull(taskId);
    }

    @Test
    void givenValidTradePath_whenCreateTask_thenReturnTaskId() {
        // Arrange
        String sftpPath = "/path/to/file";
        ZonedDateTime when = ZonedDateTime.now();

        when(wosaipayBillConfig.getTradePathPattern()).thenReturn(".*file.*");
        when(wosaipayBillConfig.getTradePathBrandPattern()).thenReturn("/path/(\\w+).*");
        when(merchantConfigDAO.findAnyOneByChannelAndBrand(any(), any())).thenReturn(mockMerchantConfig);
        when(billTaskDAO.create(any(BillTaskEntity.class))).thenAnswer(a -> {
            a.<BillTaskEntity>getArgument(0).setId(0L);
            return a.<BillTaskEntity>getArgument(0);
        });

        // Act
        Long taskId = wosaipayBillTaskCreateService.createTaskIfNecessary(sftpPath, when, "test");

        // Assert
        assertNotNull(taskId);
    }
}