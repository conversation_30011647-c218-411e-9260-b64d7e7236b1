package com.kering.cus.finance.billcenter.service.event.distributed;

import com.kering.cus.finance.billcenter.constant.TaskState;
import com.kering.cus.finance.billcenter.service.BillAcquireService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DistributedBillTaskAcquireEventConsumerTest {
    
    @Mock
    private BillAcquireService billAcquireService;
    
    @InjectMocks
    private DistributedBillTaskAcquireEventConsumer distributedBillTaskAcquireEventConsumer;

    @BeforeEach
    void setUp() {
        distributedBillTaskAcquireEventConsumer = new DistributedBillTaskAcquireEventConsumer(billAcquireService);
    }

    @Test
    void shouldCallAcquireWhenWaitAcquireState() {
        // Arrange
        Long taskId = 1L;
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskState()).thenReturn(TaskState.WAIT_ACQUIRE.name());
        when(event.getTaskId()).thenReturn(taskId);
        
        // Act
        distributedBillTaskAcquireEventConsumer.consume(event);
        
        // Assert
        verify(billAcquireService).acquireIfNecessary(taskId);
    }

    @Test
    void shouldCallAcquireWhenAcquireFailedState() {
        // Arrange
        Long taskId = 1L;
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskState()).thenReturn(TaskState.ACQUIRE_FAILED.name());
        when(event.getTaskId()).thenReturn(taskId);
        
        // Act
        distributedBillTaskAcquireEventConsumer.consume(event);
        
        // Assert
        verify(billAcquireService).acquireIfNecessary(taskId);
    }

    @Test
    void shouldNotCallAcquireWhenOtherState() {
        // Arrange
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskState()).thenReturn(TaskState.PROCESS_FAILED.name());
        
        // Act
        distributedBillTaskAcquireEventConsumer.consume(event);
        
        // Assert
        verify(billAcquireService, never()).acquireIfNecessary(anyLong());
    }

    @Test
    void shouldThrowExceptionWhenNullEvent() {
        // Arrange
        DistributedBillTaskEvent event = null;
        
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            distributedBillTaskAcquireEventConsumer.consume(event);
        });
    }

    @Test
    void shouldNotCallAcquireWhenNullState() {
        // Arrange
        DistributedBillTaskEvent event = mock(DistributedBillTaskEvent.class);
        when(event.getTaskState()).thenReturn(null);
        
        // Act
        distributedBillTaskAcquireEventConsumer.consume(event);
        
        // Assert
        verify(billAcquireService, never()).acquireIfNecessary(anyLong());
    }
}