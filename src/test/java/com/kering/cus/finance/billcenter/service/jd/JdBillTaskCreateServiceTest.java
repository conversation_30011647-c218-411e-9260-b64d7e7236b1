package com.kering.cus.finance.billcenter.service.jd;

import com.kering.cus.finance.billcenter.config.JdBillConfig;
import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.dao.BillTaskDAO;
import com.kering.cus.finance.billcenter.dao.MerchantConfigDAO;
import com.kering.cus.finance.billcenter.dao.TransitLogDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.MerchantConfigEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JdBillTaskCreateServiceTest {

    @Mock
    private JdBillConfig jdBillConfig;

    @Mock
    private BillTaskDAO billTaskDAO;

    @Mock
    private TransitLogDAO transitLogDAO;

    @Mock
    private MerchantConfigDAO merchantConfigDAO;
    @Mock
    private MerchantConfigEntity mockMerchantConfig;

    @InjectMocks
    private JdBillTaskCreateService jdBillTaskCreateService;

    @BeforeEach
    void setUp() {
        jdBillTaskCreateService = new JdBillTaskCreateService(
                jdBillConfig, billTaskDAO, transitLogDAO, merchantConfigDAO
        );
    }

    @Test
    void givenJdChannel_whenMatches_thenReturnTrue() {
        when(jdBillConfig.getTradePathPattern()).thenReturn("/jd/trade");
        when(jdBillConfig.getWalletPathPattern()).thenReturn("/jd/wallet");
        // Act & Assert
        assertTrue(jdBillTaskCreateService.matches("/jd/wallet"));
        assertFalse(jdBillTaskCreateService.matches("/wosaipay/wallet"));
    }

    @Test
    void givenPathPatterns_whenDetermineBillType_thenCorrectTypes() {
        // Arrange
        when(jdBillConfig.getTradePathPattern()).thenReturn(".*trade.*");
        when(jdBillConfig.getWalletPathPattern()).thenReturn(".*wallet.*");

        // Act & Assert
        assertEquals(BillType.TRADE_FLOW, jdBillTaskCreateService.determineBillType("/path/to/trade/file"));
        assertEquals(BillType.FUND_FLOW, jdBillTaskCreateService.determineBillType("/path/to/wallet/file"));
        assertNull(jdBillTaskCreateService.determineBillType("/path/to/unknown/file"));
    }

    @Test
    void givenValidTradeData_whenCreateTask_thenSuccess() {
        // Arrange
        when(jdBillConfig.getTradePathPattern()).thenReturn(".*trade.*");
        when(jdBillConfig.getTradePathMerchantPattern()).thenReturn(".*([0-9]+)_.*");
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(mockMerchantConfig);
        when(billTaskDAO.create(any(BillTaskEntity.class))).thenAnswer(a -> {
            a.<BillTaskEntity>getArgument(0).setId(0L);
            return a.<BillTaskEntity>getArgument(0);
        });

        // Act & Assert
        Long taskId = jdBillTaskCreateService.createTaskIfNecessary("/path/to/trade/file/123434_1234.zip", ZonedDateTime.now(), "TEST");
        assertNotNull(taskId);
    }

    @Test
    void givenValidWalletData_whenCreateTask_thenSuccess() {
        // Arrange
        when(jdBillConfig.getTradePathPattern()).thenReturn(".*trade.*");
        when(jdBillConfig.getWalletPathPattern()).thenReturn(".*wallet.*");
        when(jdBillConfig.getWalletPathMerchantPattern()).thenReturn(".*([0-9]+)_.*");
        when(merchantConfigDAO.findByChannelAndMerchantId(any(), any())).thenReturn(mockMerchantConfig);
        when(billTaskDAO.create(any(BillTaskEntity.class))).thenAnswer(a -> {
            a.<BillTaskEntity>getArgument(0).setId(0L);
            return a.<BillTaskEntity>getArgument(0);
        });

        // Act & Assert
        Long taskId = jdBillTaskCreateService.createTaskIfNecessary("/path/to/wallet/file/123434_1234.zip", ZonedDateTime.now(), "TEST");
        assertNotNull(taskId);
    }
}