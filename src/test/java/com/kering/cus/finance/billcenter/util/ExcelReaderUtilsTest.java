package com.kering.cus.finance.billcenter.util;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ExcelReaderUtilsTest {


    @TempDir
    Path tempDir;

    @Test
    void givenExcelFileWithRows_whenReadByIndex_thenAllRowsProcessed() throws Exception {
        File testFile = createTestExcelFile("Sheet1", 3, 5);

        List<Map<String, String>> allData = new ArrayList<>();
        Consumer<List<Map<String, String>>> batchConsumer = allData::addAll;

        try (InputStream is = Files.newInputStream(testFile.toPath())) {
            ExcelReaderUtils.readInBatches(is, 0, 0, 2, batchConsumer);
        }

        assertEquals(5, allData.size());
        assertEquals("Data1-1", allData.get(0).get("Header1"));
        assertEquals("Data3-2", allData.get(2).get("Header2"));
    }

    @Test
    void givenExcelFileWithSheetName_whenReadByName_thenAllRowsProcessed() throws Exception {
        File testFile = createTestExcelFile("DataSheet", 3, 10);

        List<Map<String, String>> allData = new ArrayList<>();
        Consumer<List<Map<String, String>>> batchConsumer = allData::addAll;

        try (InputStream is = Files.newInputStream(testFile.toPath())) {
            ExcelReaderUtils.readInBatches(is, "DataSheet", 0, 3, batchConsumer);
        }

        assertEquals(10, allData.size());
        assertEquals("Data1-1", allData.get(0).get("Header1"));
        assertEquals("Data10-3", allData.get(9).get("Header3"));
    }

    @Test
    void givenEmptyExcelFile_whenReadExcel_thenConsumerNotCalled() throws Exception {
        File emptyFile = createEmptyExcelFile();

        Consumer<List<Map<String, String>>> mockConsumer = mock(Consumer.class);

        try (InputStream is = Files.newInputStream(emptyFile.toPath())) {
            ExcelReaderUtils.readInBatches(is, 0, 0, 10, mockConsumer);
        }

        verify(mockConsumer, never()).accept(any());
    }

    @Test
    void givenNullInputStream_whenReadExcel_thenNoExceptionThrown() {
        assertDoesNotThrow(() -> ExcelReaderUtils.readInBatches(null, 0, 0, 10, batch -> {
        }));
    }

    @Test
    void givenInvalidSheetIndex_whenReadExcel_thenNoExceptionThrown() throws Exception {
        File testFile = createTestExcelFile("Sheet1", 2, 3);

        assertDoesNotThrow(() -> {
            try (InputStream is = Files.newInputStream(testFile.toPath())) {
                ExcelReaderUtils.readInBatches(is, 99, 0, 10, batch -> {
                });
            }
        });
    }

    @Test
    void givenNonExistingSheetName_whenReadExcel_thenNoExceptionThrown() throws Exception {
        File testFile = createTestExcelFile("Sheet1", 2, 3);

        assertDoesNotThrow(() -> {
            try (InputStream is = Files.newInputStream(testFile.toPath())) {
                ExcelReaderUtils.readInBatches(is, "NonExisting", 0, 10, batch -> {
                });
            }
        });
    }

    @Test
    void givenExcelWithDifferentDataTypes_whenReadExcel_thenConvertedToString() throws Exception {
        try (Workbook workbook = new XSSFWorkbook()){
            Sheet sheet = workbook.createSheet("DataTypes");

            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("String");
            headerRow.createCell(1).setCellValue("Number");
            headerRow.createCell(2).setCellValue("Boolean");
            headerRow.createCell(3).setCellValue("Formula");

            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("Text");
            dataRow.createCell(1).setCellValue(123.45);
            dataRow.createCell(2).setCellValue(true);
            dataRow.createCell(3).setCellFormula("A2+B2");

            File testFile = new File(tempDir.toFile(), "datatypes.xlsx");
            try (FileOutputStream fos = new FileOutputStream(testFile)) {
                workbook.write(fos);
            }

            List<Map<String, String>> result = new ArrayList<>();
            try (InputStream is = Files.newInputStream(testFile.toPath())) {
                ExcelReaderUtils.readInBatches(is, 0, 0, 10, result::addAll);
            }

            assertEquals(1, result.size());
            Map<String, String> row = result.get(0);
            assertEquals("Text", row.get("String"));
            assertEquals("123.45", row.get("Number"));
            assertEquals("TRUE", row.get("Boolean"));
            assertNotNull(row.get("Formula"));
        }

    }

    @Test
    void givenExcelWithEmptyHeaders_whenReadExcel_thenDefaultHeadersGenerat() throws Exception {
       try(Workbook workbook = new XSSFWorkbook()) {
           Sheet sheet = workbook.createSheet("EmptyHeaders");

           Row headerRow = sheet.createRow(0);
           headerRow.createCell(0);
           headerRow.createCell(1);

           Row dataRow = sheet.createRow(1);
           dataRow.createCell(0).setCellValue("Value1");
           dataRow.createCell(1).setCellValue("Value2");

           File testFile = new File(tempDir.toFile(), "empty_headers.xlsx");
           try (FileOutputStream fos = new FileOutputStream(testFile)) {
               workbook.write(fos);
           }

           List<Map<String, String>> result = new ArrayList<>();
           try (InputStream is = Files.newInputStream(testFile.toPath())) {
               ExcelReaderUtils.readInBatches(is, 0, 0, 10, result::addAll);
           }

           assertEquals(1, result.size());
           Map<String, String> row = result.get(0);
           assertEquals("Value1", row.get("Column_1"));
           assertEquals("Value2", row.get("Column_2"));
       }

    }

    private File createTestExcelFile(String sheetName, int columns, int rows) throws Exception {
        File testFile = new File(tempDir.toFile(), "test.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);

            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < columns; i++) {
                headerRow.createCell(i).setCellValue("Header" + (i + 1));
            }

            for (int i = 1; i <= rows; i++) {
                Row row = sheet.createRow(i);
                for (int j = 0; j < columns; j++) {
                    row.createCell(j).setCellValue("Data" + i + "-" + (j + 1));
                }
            }

            try (FileOutputStream fos = new FileOutputStream(testFile)) {
                workbook.write(fos);
            }
        }
        return testFile;
    }

    private File createEmptyExcelFile() throws Exception {
        File emptyFile = new File(tempDir.toFile(), "empty.xlsx");
        try (Workbook workbook = new XSSFWorkbook()) {
            workbook.createSheet("EmptySheet");
            try (FileOutputStream fos = new FileOutputStream(emptyFile)) {
                workbook.write(fos);
            }
        }
        return emptyFile;
    }
}
