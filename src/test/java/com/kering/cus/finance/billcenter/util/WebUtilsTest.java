package com.kering.cus.finance.billcenter.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import jakarta.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WebUtilsTest {

    @Mock
    private HttpServletRequest request;

    @Test
    void givenStandardHttpPort_whenGetApplicationUrl_thenReturnBaseUrl() {
        // Arrange
        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("localhost");
        when(request.getServerPort()).thenReturn(80);
        when(request.getContextPath()).thenReturn("/app");

        // Act
        String result = WebUtils.getApplicationUrl(request);

        // Assert
        assertEquals("http://localhost/app", result);
    }

    @Test
    void givenNonStandardPort_whenGetApplicationUrl_thenReturnUrlWithPort() {
        // Arrange
        when(request.getScheme()).thenReturn("https");
        when(request.getServerName()).thenReturn("example.com");
        when(request.getServerPort()).thenReturn(8443);
        when(request.getContextPath()).thenReturn("/api");

        // Act
        String result = WebUtils.getApplicationUrl(request);

        // Assert
        assertEquals("https://example.com:8443/api", result);
    }

    @Test
    void givenHttpsStandardPort_whenGetApplicationUrl_thenReturnUrlWithoutPort() {
        // Arrange
        when(request.getScheme()).thenReturn("https");
        when(request.getServerName()).thenReturn("secure.site");
        when(request.getServerPort()).thenReturn(443);
        when(request.getContextPath()).thenReturn("/");

        // Act
        String result = WebUtils.getApplicationUrl(request);

        // Assert
        assertEquals("https://secure.site/", result);
    }

    @Test
    void givenEmptyContextPath_whenGetApplicationUrl_thenReturnUrlWithoutContextPath() {
        // Arrange
        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("test.org");
        when(request.getServerPort()).thenReturn(8080);
        when(request.getContextPath()).thenReturn("");

        // Act
        String result = WebUtils.getApplicationUrl(request);

        // Assert
        assertEquals("http://test.org:8080", result);
    }

    @Test
    void givenNullContextPath_whenGetApplicationUrl_thenReturnUrlWithoutContextPath() {
        // Arrange
        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("null-test.org");
        when(request.getServerPort()).thenReturn(8080);
        when(request.getContextPath()).thenReturn(null);

        // Act
        String result = WebUtils.getApplicationUrl(request);

        // Assert
        assertEquals("http://null-test.org:8080", result);
    }
}