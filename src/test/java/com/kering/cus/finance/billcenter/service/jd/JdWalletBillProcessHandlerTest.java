
package com.kering.cus.finance.billcenter.service.jd;

import com.kering.cus.finance.billcenter.constant.BillType;
import com.kering.cus.finance.billcenter.constant.Channel;
import com.kering.cus.finance.billcenter.constant.GrantType;
import com.kering.cus.finance.billcenter.converter.JdBillConverter;
import com.kering.cus.finance.billcenter.dao.JdWalletFlowDAO;
import com.kering.cus.finance.billcenter.entity.BillTaskEntity;
import com.kering.cus.finance.billcenter.entity.JdWalletFlowEntity;
import com.kering.cus.finance.billcenter.exception.BillCenterBusinessException;
import com.kering.cus.finance.billcenter.util.CsvReaderUtils;
import com.kering.cus.finance.billcenter.util.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JdWalletBillProcessHandlerTest {

    @InjectMocks
    private JdWalletBillProcessHandler handler;

    @Mock
    private JdBillConverter jdBillConverter;

    @Mock
    private JdWalletFlowDAO jdWalletFlowDAO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenJdSftpFundFlow_whenMatches_thenReturnTrue() {
        assertTrue(handler.matches(Channel.JD, GrantType.SFTP, BillType.FUND_FLOW));
        assertFalse(handler.matches(Channel.ALIPAY, GrantType.SFTP, BillType.FUND_FLOW));
        assertFalse(handler.matches(Channel.JD, GrantType.ISV, BillType.FUND_FLOW));
        assertFalse(handler.matches(Channel.JD, GrantType.SFTP, BillType.TRADE_FLOW));
    }

    @Test
    void givenInvalidFile_whenProcess_thenReturnsFalse() {
        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");

        File file = mock(File.class);
        when(file.getName()).thenReturn("test.txt");

        List<String> process = handler.process(file, task);
        assertTrue(CollectionUtils.isEmpty(process));
    }

    @Test
    void testParsingBillFile_ioException() throws IOException {
        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");
        File file = mock(File.class);
        when(file.getName()).thenReturn("test.csv");
        CsvReaderUtils csvReaderUtils = mock(CsvReaderUtils.class);
        doThrow(new IOException("Parse error")).when(csvReaderUtils).forEach(any());
        try (MockedStatic<CsvReaderUtils> csvReaderUtilsMockedStatic = mockStatic(CsvReaderUtils.class)) {
            csvReaderUtilsMockedStatic.when(CsvReaderUtils::builder).thenAnswer(invocation -> {
                CsvReaderUtils.Builder builder = mock(CsvReaderUtils.Builder.class);
                when(builder.headerLine(anyInt())).thenReturn(builder);
                when(builder.build()).thenReturn(csvReaderUtils);
                return builder;
            });

            assertThrows(BillCenterBusinessException.class, () -> handler.process(file, task));
        }
    }


    @Test
    void givenValidFile_whenProcess_thenProcessesSuccessfully() throws IOException {
        BillTaskEntity task = new BillTaskEntity();
        task.setMerchantId("test");
        task.setIsSyncToBlackline(false);

        File file = mock(File.class);
        when(file.getName()).thenReturn("test.csv");

        when(jdBillConverter.toJdWalletFlowEntity(any(), any())).thenReturn(mock(JdWalletFlowEntity.class));
        when(jdWalletFlowDAO.findJdWalletBillDetails(any())).thenReturn(null);

        CsvReaderUtils mockCsvReader = mock(CsvReaderUtils.class);
        doNothing().when(mockCsvReader).open(any());

        doAnswer(invocation -> {
            Consumer<Map<String, String>> consumer = invocation.getArgument(0);
            consumer.accept(createMockBillMap());
            return null;
        }).when(mockCsvReader).forEach(any());
        try (
                MockedStatic<StringUtils> stringUtilMockedStatic = mockStatic(StringUtils.class);
                MockedStatic<CsvReaderUtils> mockedStatic = mockStatic(CsvReaderUtils.class)) {

            stringUtilMockedStatic.when(() -> StringUtils.trimAndRemoveSpecialChars(anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            stringUtilMockedStatic.when(() -> StringUtils.parseAndFormatMap(any()))
                    .thenAnswer(invocation -> invocation.getArgument(0));

            CsvReaderUtils.Builder mockBuilder = mock(CsvReaderUtils.Builder.class);
            when(mockBuilder.headerLine(anyInt())).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockCsvReader);
            mockedStatic.when(CsvReaderUtils::builder).thenReturn(mockBuilder);

            List<String> process = handler.process(file, task);
            assertTrue(CollectionUtils.isEmpty(process));
        }


    }


    private Map<String, String> createMockBillMap() {
        Map<String, String> map = new HashMap<>();
        map.put("日期", "2025-05-11 14:25:24");
        map.put("商户订单号", "T200P789012");
        map.put("收入金额", "100.00");
        map.put("支出金额", "100.00");
        map.put("交易备注", "售后服务单号2974585770，对应订单号313395962212，因该订单已结算，从钱包可用余额中扣收该笔费用用于处理消费者售后退款问题");
        return map;
    }

}